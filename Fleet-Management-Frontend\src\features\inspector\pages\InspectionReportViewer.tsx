import React, { useState } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { useAppDispatch } from '@/hooks/redux';
import { addNotification } from '@/store/slices/uiSlice';
import { 
  FileText,
  Download,
  Search,
  Calendar,
  Car,
  Building,
  CheckCircle,
  XCircle,
  AlertTriangle,
  Eye,
  Share,
  Printer
} from 'lucide-react';

interface InspectionReport {
  id: string;
  reportNumber: string;
  type: 'vehicle' | 'merchant';
  subject: {
    name: string;
    registration?: string;
    contactPerson?: string;
  };
  inspector: string;
  completedDate: string;
  location: string;
  status: 'Pass' | 'Fail' | 'Conditional Pass';
  totalItems: number;
  passedItems: number;
  failedItems: number;
  naItems: number;
  summary: string;
  recommendations?: string;
  nextInspectionDue?: string;
}

const InspectionReportViewer: React.FC = () => {
  const dispatch = useAppDispatch();
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [typeFilter, setTypeFilter] = useState('all');
  const [dateFilter, setDateFilter] = useState('all');

  // Mock data
  const reports: InspectionReport[] = [
    {
      id: 'RPT-2025-001',
      reportNumber: 'INS-VEH-2025-001',
      type: 'vehicle',
      subject: {
        name: 'Toyota Hilux',
        registration: 'GP123ABC'
      },
      inspector: 'John Doe',
      completedDate: '2025-01-29T11:15:00Z',
      location: 'Department of Health, Pretoria',
      status: 'Pass',
      totalItems: 25,
      passedItems: 24,
      failedItems: 0,
      naItems: 1,
      summary: 'Vehicle passed all safety inspections. Minor cosmetic issues noted.',
      recommendations: 'Schedule tire rotation in next 3 months.',
      nextInspectionDue: '2026-01-29'
    },
    {
      id: 'RPT-2025-002',
      reportNumber: 'INS-MER-2025-001',
      type: 'merchant',
      subject: {
        name: 'AutoFix Workshop',
        contactPerson: 'John Smith'
      },
      inspector: 'Jane Smith',
      completedDate: '2025-01-28T14:30:00Z',
      location: 'AutoFix Workshop, Johannesburg',
      status: 'Conditional Pass',
      totalItems: 35,
      passedItems: 30,
      failedItems: 3,
      naItems: 2,
      summary: 'Facility meets most requirements. Safety equipment needs updating.',
      recommendations: 'Update fire extinguishers and first aid kit within 30 days.',
      nextInspectionDue: '2025-07-28'
    }
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'Pass': return 'bg-green-100 text-green-800';
      case 'Fail': return 'bg-red-100 text-red-800';
      case 'Conditional Pass': return 'bg-yellow-100 text-yellow-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'Pass': return <CheckCircle className="h-4 w-4" />;
      case 'Fail': return <XCircle className="h-4 w-4" />;
      case 'Conditional Pass': return <AlertTriangle className="h-4 w-4" />;
      default: return <FileText className="h-4 w-4" />;
    }
  };

  const filteredReports = reports.filter(report => {
    const matchesSearch = 
      report.subject.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      report.reportNumber.toLowerCase().includes(searchTerm.toLowerCase()) ||
      report.inspector.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesStatus = statusFilter === 'all' || report.status.toLowerCase().replace(' ', '_') === statusFilter;
    const matchesType = typeFilter === 'all' || report.type === typeFilter;
    
    return matchesSearch && matchesStatus && matchesType;
  });

  const handleViewReport = (reportId: string) => {
    dispatch(addNotification({
      type: 'info',
      message: 'Opening Report',
      details: 'Loading detailed inspection report...'
    }));
  };

  const handleDownloadReport = (reportId: string) => {
    dispatch(addNotification({
      type: 'success',
      message: 'Download Started',
      details: 'Inspection report PDF is being downloaded.'
    }));
  };

  const handleShareReport = (reportId: string) => {
    dispatch(addNotification({
      type: 'info',
      message: 'Share Report',
      details: 'Report sharing options opened.'
    }));
  };

  return (
    <div className="container mx-auto px-6 py-8 space-y-6 max-w-7xl">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Inspection Reports</h1>
          <p className="text-gray-600">View and manage inspection reports</p>
        </div>
      </div>

      {/* Filters */}
      <Card>
        <CardContent className="p-4">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <Input
                placeholder="Search reports..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
            
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger>
                <SelectValue placeholder="Filter by status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Statuses</SelectItem>
                <SelectItem value="pass">Pass</SelectItem>
                <SelectItem value="fail">Fail</SelectItem>
                <SelectItem value="conditional_pass">Conditional Pass</SelectItem>
              </SelectContent>
            </Select>

            <Select value={typeFilter} onValueChange={setTypeFilter}>
              <SelectTrigger>
                <SelectValue placeholder="Filter by type" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Types</SelectItem>
                <SelectItem value="vehicle">Vehicle</SelectItem>
                <SelectItem value="merchant">Merchant</SelectItem>
              </SelectContent>
            </Select>

            <Select value={dateFilter} onValueChange={setDateFilter}>
              <SelectTrigger>
                <SelectValue placeholder="Filter by date" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Time</SelectItem>
                <SelectItem value="today">Today</SelectItem>
                <SelectItem value="week">This Week</SelectItem>
                <SelectItem value="month">This Month</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Reports List */}
      <div className="space-y-4">
        {filteredReports.map((report) => (
          <Card key={report.id} className="hover:shadow-md transition-shadow">
            <CardContent className="p-4">
              <div className="space-y-4">
                {/* Header */}
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <div className="flex items-center space-x-2 mb-2">
                      {report.type === 'vehicle' ? (
                        <Car className="h-5 w-5 text-blue-600" />
                      ) : (
                        <Building className="h-5 w-5 text-purple-600" />
                      )}
                      <h3 className="font-semibold">{report.subject.name}</h3>
                      {report.subject.registration && (
                        <Badge variant="outline">{report.subject.registration}</Badge>
                      )}
                    </div>
                    <p className="text-sm text-gray-600">Report: {report.reportNumber}</p>
                  </div>
                  
                  <Badge className={getStatusColor(report.status)}>
                    {getStatusIcon(report.status)}
                    <span className="ml-1">{report.status}</span>
                  </Badge>
                </div>

                {/* Details */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                  <div className="space-y-2">
                    <div className="flex items-center space-x-2">
                      <Calendar className="h-4 w-4 text-gray-400" />
                      <span>Completed: {new Date(report.completedDate).toLocaleDateString()}</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <span className="font-medium">Inspector:</span>
                      <span>{report.inspector}</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <span className="font-medium">Location:</span>
                      <span className="truncate">{report.location}</span>
                    </div>
                  </div>
                  
                  <div className="space-y-2">
                    <div className="flex items-center space-x-2">
                      <span className="font-medium">Items:</span>
                      <span>{report.totalItems} total</span>
                    </div>
                    <div className="flex items-center space-x-4 text-xs">
                      <span className="text-green-600">✓ {report.passedItems}</span>
                      <span className="text-red-600">✗ {report.failedItems}</span>
                      <span className="text-gray-600">N/A {report.naItems}</span>
                    </div>
                    {report.nextInspectionDue && (
                      <div className="flex items-center space-x-2">
                        <span className="font-medium">Next Due:</span>
                        <span>{new Date(report.nextInspectionDue).toLocaleDateString()}</span>
                      </div>
                    )}
                  </div>
                </div>

                {/* Summary */}
                <div className="p-3 bg-gray-50 rounded text-sm">
                  <strong>Summary:</strong> {report.summary}
                  {report.recommendations && (
                    <div className="mt-2">
                      <strong>Recommendations:</strong> {report.recommendations}
                    </div>
                  )}
                </div>

                {/* Actions */}
                <div className="flex flex-wrap gap-2">
                  <Button size="sm" onClick={() => handleViewReport(report.id)}>
                    <Eye className="h-4 w-4 mr-2" />
                    View Details
                  </Button>
                  
                  <Button size="sm" variant="outline" onClick={() => handleDownloadReport(report.id)}>
                    <Download className="h-4 w-4 mr-2" />
                    Download PDF
                  </Button>
                  
                  <Button size="sm" variant="outline" onClick={() => handleShareReport(report.id)}>
                    <Share className="h-4 w-4 mr-2" />
                    Share
                  </Button>
                  
                  <Button size="sm" variant="outline" onClick={() => window.print()}>
                    <Printer className="h-4 w-4 mr-2" />
                    Print
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {filteredReports.length === 0 && (
        <Card>
          <CardContent className="p-8 text-center">
            <FileText className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No reports found</h3>
            <p className="text-gray-600">
              {searchTerm || statusFilter !== 'all' || typeFilter !== 'all' || dateFilter !== 'all'
                ? 'Try adjusting your search criteria or filters.'
                : 'No inspection reports available.'}
            </p>
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default InspectionReportViewer;


