import React from 'react';
import { useAppSelector, useAppDispatch } from '@/hooks/redux';
import { closeModal } from '@/store/slices/uiSlice';
import { AlertTriangle, CheckCircle, X } from 'lucide-react';

const DriverModals: React.FC = () => {
  const modals = useAppSelector((state) => state.ui.modals);
  const dispatch = useAppDispatch();

  const handleClose = (modalId: string) => {
    dispatch(closeModal(modalId));
  };

  const renderModal = (modal: any) => {
    const { id, props } = modal;

    return (
      <div key={id} className="fixed inset-0 z-50 overflow-y-auto">
        <div className="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
          <div className="fixed inset-0 transition-opacity" aria-hidden="true">
            <div className="absolute inset-0 bg-gray-500 opacity-75"></div>
          </div>

          <div className="inline-block align-bottom bg-white rounded-lg px-4 pt-5 pb-4 text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full sm:p-6">
            <div className="sm:flex sm:items-start">
              <div className={`mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full sm:mx-0 sm:h-10 sm:w-10 ${
                props.title?.includes('Failed') || props.title?.includes('Critical') || props.title?.includes('Emergency')
                  ? 'bg-red-100'
                  : props.title?.includes('Complete') || props.title?.includes('Success')
                  ? 'bg-green-100'
                  : 'bg-blue-100'
              }`}>
                {props.title?.includes('Failed') || props.title?.includes('Critical') || props.title?.includes('Emergency') ? (
                  <AlertTriangle className="h-6 w-6 text-red-600" />
                ) : props.title?.includes('Complete') || props.title?.includes('Success') ? (
                  <CheckCircle className="h-6 w-6 text-green-600" />
                ) : (
                  <AlertTriangle className="h-6 w-6 text-blue-600" />
                )}
              </div>
              <div className="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left flex-1">
                <h3 className="text-lg leading-6 font-medium text-gray-900">
                  {props.title}
                </h3>
                <div className="mt-2">
                  <p className="text-sm text-gray-500">
                    {props.message}
                  </p>
                </div>
              </div>
              <button
                onClick={() => handleClose(id)}
                className="absolute top-4 right-4 text-gray-400 hover:text-gray-600"
              >
                <X className="h-5 w-5" />
              </button>
            </div>
            <div className="mt-5 sm:mt-4 sm:flex sm:flex-row-reverse">
              <button
                type="button"
                onClick={() => {
                  props.onConfirm?.();
                  handleClose(id);
                }}
                className={`w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 text-base font-medium text-white focus:outline-none focus:ring-2 focus:ring-offset-2 sm:ml-3 sm:w-auto sm:text-sm ${
                  props.title?.includes('Failed') || props.title?.includes('Critical') || props.title?.includes('Emergency')
                    ? 'bg-red-600 hover:bg-red-700 focus:ring-red-500'
                    : props.title?.includes('Complete') || props.title?.includes('Success')
                    ? 'bg-green-600 hover:bg-green-700 focus:ring-green-500'
                    : 'bg-blue-600 hover:bg-blue-700 focus:ring-blue-500'
                }`}
              >
                {props.confirmText || 'Confirm'}
              </button>
              <button
                type="button"
                onClick={() => handleClose(id)}
                className="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:mt-0 sm:w-auto sm:text-sm"
              >
                {props.cancelText || 'Cancel'}
              </button>
            </div>
          </div>
        </div>
      </div>
    );
  };

  return (
    <>
      {modals.map(renderModal)}
    </>
  );
};

export default DriverModals;