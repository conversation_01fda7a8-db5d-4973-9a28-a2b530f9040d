---
type: "manual"
---

# Database Development Rules for RT46-2026

## PostgreSQL Best Practices

### Schema Design Principles
- **Normalize to 3NF minimum** - Eliminate data redundancy and update anomalies
- **Use meaningful table and column names** - government_vehicles, not gv_tbl
- **Implement proper constraints** - Foreign keys, check constraints, unique constraints
- **Use appropriate data types** - UUID for IDs, DECIMAL for currency, TIMESTAMP for dates
- **Design for audit trails** - created_at, updated_at, created_by, updated_by columns
- **Plan for soft deletes** - is_deleted boolean instead of hard deletes for compliance

### Indexing Strategy
- **Primary keys** automatically get unique indexes
- **Foreign keys** should have indexes for join performance
- **Frequently queried columns** need indexes (status, department_id, created_at)
- **Composite indexes** for multi-column queries (vehicle_id, status, date)
- **Partial indexes** for filtered queries (WHERE status = 'active')
- **GIN indexes** for array and JSONB columns (vendor specializations)
- **Full-text search indexes** for document content searching

### Performance Optimization
- **Query analysis** - Use EXPLAIN ANALYZE for slow queries
- **Connection pooling** - 20 base connections, 30 max overflow
- **Read replicas** - Separate reporting queries from transactional operations
- **Materialized views** - Pre-aggregate complex analytical queries
- **Partitioning** - Partition large tables by date or department
- **Vacuum and analyze** - Regular maintenance for optimal performance

## SQLAlchemy Core Implementation

### Table Definitions
- **Use declarative metadata** for table structure
- **Implement proper relationships** with foreign key constraints
- **Add check constraints** for business rule enforcement
- **Use server defaults** where appropriate (timestamps, UUIDs)
- **Define indexes explicitly** in table definitions
- **Use meaningful constraint names** for easier debugging

### Query Patterns
- **Use async operations** throughout - asyncpg for PostgreSQL
- **Implement connection pooling** with proper configuration
- **Use transactions** for multi-table operations
- **Implement proper error handling** for database exceptions
- **Use parameterized queries** to prevent SQL injection
- **Implement query timeouts** for long-running operations

### Migration Management
- **Use Alembic** for database schema migrations
- **Version all schema changes** with descriptive names
- **Test migrations** on staging before production
- **Implement rollback strategies** for failed migrations
- **Document breaking changes** and required data migrations
- **Backup before migrations** - especially on production

## Data Access Layer Patterns

### Repository Pattern Implementation
- **One repository per aggregate root** (VehicleRepository, WorkOrderRepository)
- **Abstract database operations** behind repository interfaces
- **Implement async methods** for all database operations
- **Use dependency injection** for repository registration
- **Handle database errors** gracefully with proper exceptions
- **Implement caching** for frequently accessed data

### Query Optimization Techniques
- **Select only needed columns** - avoid SELECT *
- **Use joins efficiently** - LEFT JOIN vs INNER JOIN appropriately
- **Implement pagination** for large result sets
- **Use bulk operations** for multiple inserts/updates
- **Cache frequently accessed data** in Redis
- **Monitor slow queries** and optimize proactively

### Transaction Management
- **Use database transactions** for multi-table operations
- **Implement proper isolation levels** based on operation type
- **Handle deadlocks** with retry mechanisms
- **Keep transactions short** to avoid blocking
- **Use savepoints** for partial rollbacks in complex operations
- **Implement timeout handling** for long-running transactions

## Data Security and Compliance

### POPIA Data Protection
- **Encrypt sensitive columns** at application level
- **Implement data masking** for non-production environments
- **Use role-based access control** for database users
- **Log all data access** for audit trails
- **Implement data retention policies** with automatic purging
- **Support right to deletion** for personal data

### Audit Trail Implementation
- **Track all data modifications** with timestamp and user
- **Use triggers or application-level logging** for audit trails
- **Store audit data separately** from operational data
- **Implement tamper-proof logging** for compliance
- **Regular audit log archival** to manage storage costs
- **Provide audit report generation** capabilities

### Database Security
- **Use connection string encryption** for credentials
- **Implement least privilege access** for application users
- **Regular security updates** for PostgreSQL and dependencies
- **Monitor for SQL injection attempts** in application logs
- **Use SSL/TLS** for all database connections
- **Implement database firewall rules** for network security

## Backup and Recovery

### Backup Strategy
- **Automated daily backups** with point-in-time recovery
- **Cross-region backup replication** for disaster recovery
- **Regular backup testing** to ensure restoration capability
- **Retention policy** aligned with government requirements
- **Backup encryption** for data protection
- **Document recovery procedures** for operations team

### High Availability
- **Multi-AZ deployment** for automatic failover
- **Read replicas** for load distribution and disaster recovery
- **Connection pooling** to handle connection failures
- **Health monitoring** with automated alerting
- **Failover testing** to validate recovery procedures
- **Database proxy** for connection management and failover

## Development Workflow

### Local Development
- **Use Docker containers** for consistent development environments
- **Seed data scripts** for development and testing
- **Database reset procedures** for clean development state
- **Environment-specific configurations** for different stages
- **Local migration testing** before staging deployment
- **Development database monitoring** for performance issues

### Testing Practices
- **Unit tests** for repository layer with test database
- **Integration tests** for database operations
- **Performance tests** for query optimization validation
- **Data migration tests** for schema changes
- **Backup and recovery tests** for disaster scenarios
- **Load testing** for concurrent user scenarios

## Monitoring and Observability

### Database Monitoring
- **Query performance monitoring** with automatic alerting
- **Connection pool monitoring** for resource utilization
- **Slow query logging** and analysis
- **Database size monitoring** for capacity planning
- **Index usage analysis** for optimization opportunities
- **Deadlock detection and analysis** for performance tuning

### Application-Level Monitoring
- **Database operation metrics** in application monitoring
- **Error rate tracking** for database operations
- **Response time monitoring** for database queries
- **Cache hit rate monitoring** for Redis operations
- **Transaction success/failure rates** for reliability metrics
- **Data consistency checks** for integrity validation

## Common Anti-Patterns to Avoid

### Query Anti-Patterns
- **N+1 queries** - Use eager loading or batch queries instead
- **SELECT \*** - Always specify required columns
- **Missing indexes** on frequently queried columns
- **Cartesian products** in joins without proper conditions
- **Unnecessary subqueries** when joins would be more efficient
- **Large transactions** that block other operations

### Schema Anti-Patterns
- **God tables** with too many columns
- **Missing foreign key constraints** leading to orphaned data
- **Inappropriate data types** (VARCHAR for numbers, TEXT for enums)
- **Missing indexes** on foreign key columns
- **Denormalization without justification** leading to data inconsistency
- **No audit trail** for critical business data

