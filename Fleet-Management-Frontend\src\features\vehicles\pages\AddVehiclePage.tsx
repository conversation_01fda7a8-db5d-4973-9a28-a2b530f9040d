
import React from 'react';
import { useNavigate } from 'react-router-dom';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { ArrowLeft } from 'lucide-react';
import { VehicleForm, type VehicleFormData } from '../components/VehicleForm';
import { useCreateVehicleMutation } from '@/store/api/vehicleApi';
import toast from 'react-hot-toast';
import type { VehicleRequest } from '@/types/vehicle';

export const AddVehiclePage: React.FC = () => {
  const navigate = useNavigate();
  const [createVehicle, { isLoading }] = useCreateVehicleMutation();

  const handleSubmit = async (data: VehicleFormData) => {
    try {
      // Transform frontend data to backend format
      const vehicleRequest: VehicleRequest = {
        registration_number: data.registrationNumber,
        make: data.make,
        model: data.model,
        year: data.year,
        vin: data.vin,
        engine_number: data.engineNumber,
        fuel_type: data.fuelType,
        vehicle_type: data.category,
        department: data.department,
        current_mileage: data.odometerReading,
        purchase_date: data.purchaseDate,
        purchase_price: data.purchasePrice,
        insurance_expiry: data.insuranceExpiry,
        license_expiry: data.licenseExpiry,
        assigned_driver_id: data.assignedDriver || undefined,
        location: data.location,
      };

      const result = await createVehicle(vehicleRequest).unwrap();
      toast.success('Vehicle registered successfully');
      navigate(`/vehicles/${result.id}`);
    } catch (error: unknown) {
      if (error && typeof error === 'object' && 'data' in error && 
          error.data && typeof error.data === 'object' && 'message' in error.data &&
          typeof error.data.message === 'string') {
        toast.error(error.data.message);
      } else {
        toast.error('Failed to create vehicle');
      }
    }
  };

  const handleCancel = () => {
    navigate('/vehicles');
  };

  return (
    <div className="container mx-auto px-3 sm:px-4 py-4 sm:py-6">
      {/* Header */}
      <div className="mb-4 sm:mb-6">
        <Button
          variant="ghost"
          onClick={() => navigate('/vehicles')}
          className="mb-3 sm:mb-4"
        >
          <ArrowLeft className="h-4 w-4 mr-2" />
          <span className="hidden sm:inline">Back to Vehicles</span>
          <span className="sm:hidden">Back</span>
        </Button>

        <div>
          <h1 className="text-xl sm:text-2xl font-bold text-gray-900">Add New Vehicle</h1>
          <p className="text-sm sm:text-base text-gray-600">Register a new vehicle in the fleet management system</p>
        </div>
      </div>

      {/* Form */}
      <Card className="w-full">
        <CardHeader className="p-4 sm:p-6">
          <CardTitle className="text-lg sm:text-xl">Vehicle Registration</CardTitle>
        </CardHeader>
        <CardContent className="p-4 sm:p-6">
          <VehicleForm
            onSubmit={handleSubmit}
            onCancel={handleCancel}
            isLoading={isLoading}
            submitLabel="Register Vehicle"
          />
        </CardContent>
      </Card>
    </div>
  );
};








