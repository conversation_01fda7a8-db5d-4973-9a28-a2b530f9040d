/* Landing Page Animations and Styles */

@import url('https://fonts.googleapis.com/css2?family=IBM+Plex+Sans:wght@300;400;500;600;700&display=swap');

/* Floating animation */
@keyframes float {
  0% { 
    transform: translateY(0px); 
  }
  50% { 
    transform: translateY(-20px); 
  }
  100% { 
    transform: translateY(0px); 
  }
}

.floating {
  animation: float 6s ease-in-out infinite;
}

/* Gradient backgrounds */
.gradient-bg {
  background: linear-gradient(to right, #3B82F6, #8B5CF6);
}

/* Smooth transitions for all interactive elements */
.transition-all {
  transition: all 0.3s ease;
}

/* Enhanced hover effects */
.hover-lift:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
}

/* Custom scrollbar for webkit browsers */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f5f9;
}

::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

/* Smooth scroll behavior */
html {
  scroll-behavior: smooth;
}

/* Focus styles for accessibility */
button:focus,
a:focus {
  outline: 2px solid #3B82F6;
  outline-offset: 2px;
}

/* Custom button hover effects */
.btn-primary {
  background: linear-gradient(to right, #3B82F6, #8B5CF6);
  transition: all 0.3s ease;
}

.btn-primary:hover {
  opacity: 0.9;
  transform: translateY(-1px);
  box-shadow: 0 8px 20px rgba(59, 130, 246, 0.3);
}

/* Card hover effects */
.card-hover {
  transition: all 0.3s ease;
}

.card-hover:hover {
  transform: translateY(-4px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}



/* Text gradient effect */
.text-gradient {
  background: linear-gradient(to right, #3B82F6, #8B5CF6);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Pulse animation for call-to-action elements */
@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.8;
  }
}

.pulse {
  animation: pulse 2s infinite;
}

/* Fade in animation */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.fade-in {
  animation: fadeIn 0.6s ease-out;
}

/* Stagger animation for list items */
.stagger-item {
  animation: fadeIn 0.6s ease-out;
}

.stagger-item:nth-child(1) { animation-delay: 0.1s; }
.stagger-item:nth-child(2) { animation-delay: 0.2s; }
.stagger-item:nth-child(3) { animation-delay: 0.3s; }
.stagger-item:nth-child(4) { animation-delay: 0.4s; }
.stagger-item:nth-child(5) { animation-delay: 0.5s; }
.stagger-item:nth-child(6) { animation-delay: 0.6s; }

/* Mobile optimizations */
@media (max-width: 768px) {
  .floating {
    animation-duration: 4s;
  }
  
  .card-hover:hover {
    transform: none;
  }
  
  .btn-primary:hover {
    transform: none;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .text-slate-600 {
    color: #1e293b !important;
  }
  
  .text-slate-400 {
    color: #475569 !important;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .floating,
  .pulse,
  .fade-in,
  .stagger-item {
    animation: none;
  }
  
  .transition-all,
  .card-hover,
  .btn-primary {
    transition: none;
  }
}
