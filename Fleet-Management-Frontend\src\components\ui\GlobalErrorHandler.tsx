import React, { useEffect, useRef } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { RootState } from '@/store';
import { removeError } from '@/store/slices/errorSlice';
import { NetworkStatusIndicator } from './NetworkStatusIndicator';
import { GlobalErrorDisplay } from './GlobalErrorDisplay';
import { ErrorNotifications } from './ErrorNotifications';

const GlobalErrorHandler: React.FC = () => {
  const dispatch = useDispatch();
  const { errors, globalError, networkStatus } = useSelector((state: RootState) => state.error);
  const timerRefs = useRef<{ [key: string]: NodeJS.Timeout }>({});

  // Auto-remove errors after 10 seconds
  useEffect(() => {
    errors.forEach(error => {
      if (!error.retryable && !timerRefs.current[error.id]) {
        timerRefs.current[error.id] = setTimeout(() => {
          dispatch(removeError(error.id));
          delete timerRefs.current[error.id];
        }, 10000);
      }
    });

    // Cleanup function to clear all timers
    return () => {
      Object.values(timerRefs.current).forEach(timer => clearTimeout(timer));
      timerRefs.current = {};
    };
  }, [errors, dispatch]);

  const handleDismissError = (errorId: string) => {
    dispatch(removeError(errorId));
    if (timerRefs.current[errorId]) {
      clearTimeout(timerRefs.current[errorId]);
      delete timerRefs.current[errorId];
    }
  };

  return (
    <>
      <NetworkStatusIndicator networkStatus={networkStatus} />
      {globalError && (
        <GlobalErrorDisplay 
          error={globalError}
          onDismiss={handleDismissError} 
        />
      )}
      <ErrorNotifications
        errors={errors}
        globalErrorId={globalError?.id}
        onDismiss={handleDismissError}
      />
    </>
  );
};

export default GlobalErrorHandler;




