import type { Merchant } from '@/types/merchant';
import { mockMerchants } from './mockMerchants';

class MerchantService {
  static async fetchMerchants(): Promise<Merchant[]> {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 1000));
    return mockMerchants;
  }

  static async linkMerchants(merchantIds: string[]): Promise<void> {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 500));
    console.log('Linking merchants:', merchantIds);
  }

  static async unlinkMerchants(merchantIds: string[]): Promise<void> {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 500));
    console.log('Unlinking merchants:', merchantIds);
  }
}

export { MerchantService };
