
import React from 'react';
import { NavLink, useLocation } from 'react-router-dom';
import { useAppSelector } from '@/hooks/redux';
import logo from '@/assets/image1.png';
import { 
  LayoutDashboard, 
  Car, 
  Wrench, 
  Users, 
  FileText, 
  Settings, 
  Calendar,
  ClipboardList,
  TrendingUp,
  Shield,
  Fuel,
  CheckCircle,
  AlertTriangle,
  Navigation
} from 'lucide-react';

interface SidebarProps {
  isOpen: boolean;
  onClose: () => void;
}

const Sidebar: React.FC<SidebarProps> = ({ isOpen, onClose }) => {
  const { user } = useAppSelector((state) => state.auth);
  const location = useLocation();

  const getMenuItems = () => {
    const role = user?.role;
    
    const commonItems = [
      { name: 'Dashboard', href: '/dashboard', icon: LayoutDashboard },
      { name: 'Reports', href: '/reports', icon: FileText },
      { name: 'Settings', href: '/settings', icon: Settings }
    ];

    const roleSpecificItems = {
      fleet_manager: [
        { name: 'Vehicles', href: '/vehicles', icon: Car },
        { name: 'Work Orders', href: '/work-orders', icon: Wrench },
        { name: 'Maintenance', href: '/maintenance/requests', icon: ClipboardList },
        { name: 'Vendors', href: '/vendors', icon: Users },
        { name: 'Live Tracking', href: '/tracking', icon: Navigation }
      ],
      transport_officer: [
        { name: 'Transport Dashboard', href: '/transport/dashboard', icon: LayoutDashboard },
        { name: 'Vehicles', href: '/vehicles', icon: Car },
        { name: 'Maintenance Schedule', href: '/transport/maintenance/schedule', icon: Calendar },
        { name: 'Trip Logs', href: '/transport/trip-logs', icon: TrendingUp },
        { name: 'Booking Calendar', href: '/transport/booking-calendar', icon: Calendar },
        { name: 'Live Tracking', href: '/tracking', icon: Navigation }
      ],
      driver: [
        { name: 'Driver Dashboard', href: '/driver/dashboard', icon: LayoutDashboard },
        { name: 'Book Vehicle', href: '/driver/book-vehicle', icon: Calendar },
        { name: 'Pre-Trip Check', href: '/driver/pre-trip-check', icon: CheckCircle },
        { name: 'Report Issue', href: '/driver/report-issue', icon: AlertTriangle },
        { name: 'Fuel Log', href: '/driver/fuel-log', icon: Fuel },
        { name: 'Trip History', href: '/driver/trip-history', icon: TrendingUp }
      ],
      admin: [
        { name: 'Vehicles', href: '/vehicles', icon: Car },
        { name: 'Work Orders', href: '/work-orders', icon: Wrench },
        { name: 'Maintenance', href: '/maintenance/requests', icon: ClipboardList },
        { name: 'Vendors', href: '/vendors', icon: Users },
        { name: 'Live Tracking', href: '/tracking', icon: Navigation },
        { name: 'User Management', href: '/admin/users', icon: Shield }
      ],
      finance_officer: [
        { name: 'Finance Dashboard', href: '/finance/dashboard', icon: LayoutDashboard },
        { name: 'Invoice Queue', href: '/finance/invoices', icon: FileText },
        { name: 'Payment Tracker', href: '/finance/payments', icon: TrendingUp },
        { name: 'Budget Management', href: '/finance/budget', icon: Settings },
        { name: 'Reports', href: '/reports', icon: FileText }
      ]
    };

    return [
      ...commonItems,
      ...(roleSpecificItems[role as keyof typeof roleSpecificItems] || [])
    ];
  };

  const menuItems = getMenuItems();

  return (
    <>
      {/* Mobile overlay */}
      {isOpen && (
        <div 
          className="fixed inset-0 bg-black bg-opacity-50 z-40 lg:hidden" 
          onClick={onClose}
        />
      )}
      
      {/* Sidebar */}
      <div className={`
        fixed top-0 left-0 z-50 h-full w-64 bg-white shadow-lg transform transition-transform duration-300 ease-in-out
        lg:relative lg:translate-x-0 lg:shadow-none lg:border-r lg:border-gray-200
        ${isOpen ? 'translate-x-0' : '-translate-x-full'}
      `}>
        <div className="flex flex-col h-full">
          {/* Header */}
          <div className="flex items-center justify-between p-4 border-b border-gray-200">
            <div className="flex items-center">
              <img src={logo} alt="RT46 Fleet Logo" className="h-10 w-auto object-contain" />
            </div>
            <button 
              onClick={onClose}
              className="lg:hidden p-1 rounded-md hover:bg-gray-100"
            >
              ×
            </button>
          </div>

          {/* Navigation */}
          <nav className="flex-1 overflow-y-auto p-4">
            <ul className="space-y-2">
              {menuItems.map((item) => {
                const IconComponent = item.icon;
                return (
                  <li key={item.name}>
                    <NavLink
                      to={item.href}
                      onClick={onClose}
                      className={({ isActive }) =>
                        `flex items-center px-3 py-2 rounded-md text-sm font-medium transition-colors ${
                          isActive
                            ? 'bg-blue-100 text-blue-700'
                            : 'text-gray-600 hover:bg-gray-100 hover:text-gray-900'
                        }`
                      }
                    >
                      <IconComponent className="mr-3 h-5 w-5" />
                      {item.name}
                    </NavLink>
                  </li>
                );
              })}
            </ul>
          </nav>

          {/* User info */}
          <div className="p-4 border-t border-gray-200">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <div className="h-8 w-8 rounded-full bg-blue-500 flex items-center justify-center">
                  <span className="text-sm font-medium text-white">
                    {user?.firstName?.charAt(0) || user?.email?.charAt(0) || 'U'}
                  </span>
                </div>
              </div>
              <div className="ml-3">
                <p className="text-sm font-medium text-gray-900">
                  {user?.firstName && user?.lastName 
                    ? `${user.firstName} ${user.lastName}` 
                    : user?.email || 'User'}
                </p>
                <p className="text-xs text-gray-500 capitalize">{user?.role?.replace('_', ' ')}</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default Sidebar;






