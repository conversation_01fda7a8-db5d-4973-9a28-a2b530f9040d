import React, { useState } from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Label } from '@/components/ui/label';
import { useAppDispatch } from '@/hooks/redux';
import { addNotification } from '@/store/slices/uiSlice';
import { Calendar, Clock, MapPin, User, Car, Building } from 'lucide-react';

interface ScheduleInspectionFormProps {
  onClose: () => void;
  onSubmit: (data: any) => void;
}

const ScheduleInspectionForm: React.FC<ScheduleInspectionFormProps> = ({ onClose, onSubmit }) => {
  const dispatch = useAppDispatch();
  const [formData, setFormData] = useState({
    type: '',
    subjectName: '',
    registration: '',
    contact<PERSON>erson: '',
    phone: '',
    date: '',
    time: '',
    duration: '45',
    location: '',
    inspector: '',
    priority: 'Medium',
    reason: ''
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.type || !formData.subjectName || !formData.date || !formData.time || !formData.location) {
      dispatch(addNotification({
        type: 'error',
        message: 'Missing Required Fields',
        details: 'Please fill in all required fields.'
      }));
      return;
    }

    onSubmit(formData);
    dispatch(addNotification({
      type: 'success',
      message: 'Inspection Scheduled',
      details: 'New inspection has been added to the calendar.'
    }));
    onClose();
  };

  return (
    <Card className="w-full max-w-2xl">
      <CardHeader>
        <CardTitle className="flex items-center space-x-2">
          <Calendar className="h-5 w-5" />
          <span>Schedule New Inspection</span>
        </CardTitle>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-4">
          {/* Inspection Type */}
          <div className="space-y-2">
            <Label htmlFor="type">Inspection Type *</Label>
            <Select value={formData.type} onValueChange={(value) => setFormData({...formData, type: value})}>
              <SelectTrigger>
                <SelectValue placeholder="Select inspection type" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="vehicle">Vehicle Inspection</SelectItem>
                <SelectItem value="merchant">Merchant Inspection</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Subject Details */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="subjectName">
                {formData.type === 'vehicle' ? 'Vehicle Name' : 'Business Name'} *
              </Label>
              <Input
                id="subjectName"
                value={formData.subjectName}
                onChange={(e) => setFormData({...formData, subjectName: e.target.value})}
                placeholder={formData.type === 'vehicle' ? 'e.g., Toyota Hilux' : 'e.g., AutoFix Workshop'}
              />
            </div>

            {formData.type === 'vehicle' ? (
              <div className="space-y-2">
                <Label htmlFor="registration">Registration Number</Label>
                <Input
                  id="registration"
                  value={formData.registration}
                  onChange={(e) => setFormData({...formData, registration: e.target.value})}
                  placeholder="e.g., GP123ABC"
                />
              </div>
            ) : (
              <div className="space-y-2">
                <Label htmlFor="contactPerson">Contact Person</Label>
                <Input
                  id="contactPerson"
                  value={formData.contactPerson}
                  onChange={(e) => setFormData({...formData, contactPerson: e.target.value})}
                  placeholder="e.g., John Smith"
                />
              </div>
            )}
          </div>

          {/* Contact & Schedule */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="phone">Phone Number</Label>
              <Input
                id="phone"
                value={formData.phone}
                onChange={(e) => setFormData({...formData, phone: e.target.value})}
                placeholder="e.g., ************"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="inspector">Inspector</Label>
              <Select value={formData.inspector} onValueChange={(value) => setFormData({...formData, inspector: value})}>
                <SelectTrigger>
                  <SelectValue placeholder="Assign inspector" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="John Doe">John Doe</SelectItem>
                  <SelectItem value="Jane Smith">Jane Smith</SelectItem>
                  <SelectItem value="Mike Johnson">Mike Johnson</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          {/* Date & Time */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="space-y-2">
              <Label htmlFor="date">Date *</Label>
              <Input
                id="date"
                type="date"
                value={formData.date}
                onChange={(e) => setFormData({...formData, date: e.target.value})}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="time">Time *</Label>
              <Input
                id="time"
                type="time"
                value={formData.time}
                onChange={(e) => setFormData({...formData, time: e.target.value})}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="duration">Duration (minutes)</Label>
              <Select value={formData.duration} onValueChange={(value) => setFormData({...formData, duration: value})}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="30">30 minutes</SelectItem>
                  <SelectItem value="45">45 minutes</SelectItem>
                  <SelectItem value="60">1 hour</SelectItem>
                  <SelectItem value="90">1.5 hours</SelectItem>
                  <SelectItem value="120">2 hours</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          {/* Location */}
          <div className="space-y-2">
            <Label htmlFor="location">Location *</Label>
            <Input
              id="location"
              value={formData.location}
              onChange={(e) => setFormData({...formData, location: e.target.value})}
              placeholder="e.g., Department of Health, Pretoria"
            />
          </div>

          {/* Priority */}
          <div className="space-y-2">
            <Label htmlFor="priority">Priority</Label>
            <Select value={formData.priority} onValueChange={(value) => setFormData({...formData, priority: value})}>
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="Low">Low</SelectItem>
                <SelectItem value="Medium">Medium</SelectItem>
                <SelectItem value="High">High</SelectItem>
                <SelectItem value="Critical">Critical</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Reason */}
          <div className="space-y-2">
            <Label htmlFor="reason">Inspection Reason</Label>
            <Textarea
              id="reason"
              value={formData.reason}
              onChange={(e) => setFormData({...formData, reason: e.target.value})}
              placeholder="e.g., Annual Safety Inspection, Quarterly Facility Check"
              className="min-h-[80px]"
            />
          </div>

          {/* Actions */}
          <div className="flex space-x-4 pt-4">
            <Button type="button" variant="outline" onClick={onClose} className="flex-1">
              Cancel
            </Button>
            <Button type="submit" className="flex-1">
              Schedule Inspection
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  );
};

export default ScheduleInspectionForm;