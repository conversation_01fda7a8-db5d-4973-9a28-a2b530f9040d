import React, { useState } from 'react';
import { 
  User, 
  Bell, 
  Shield, 
  Palette, 
  Key,
  HelpCircle, 
  Save 
} from 'lucide-react';
import { useNotifications } from '@/hooks/useNotifications';

const SettingsPage: React.FC = () => {
  const { showSuccess } = useNotifications();
  const [activeTab, setActiveTab] = useState('profile');

  const tabs = [
    { id: 'profile', label: 'Profile', icon: User },
    { id: 'notifications', label: 'Notifications', icon: Bell },
    { id: 'security', label: 'Security', icon: Shield },
    { id: 'preferences', label: 'Preferences', icon: Palette },
    { id: 'roles', label: 'Roles & Permissions', icon: Key },
    { id: 'help', label: 'Help & Support', icon: HelpCircle },
  ];

  const handleSave = () => {
    showSuccess('Settings saved successfully');
  };

  return (
    <div className="space-y-4 sm:space-y-6 p-3 sm:p-4 lg:p-6">
      <div>
        <h1 className="text-xl sm:text-2xl font-bold text-gray-900">Settings</h1>
        <p className="text-sm sm:text-base text-gray-600">Manage your account settings and preferences</p>
      </div>

      <div className="bg-white shadow rounded-lg">
        <div className="border-b border-gray-200">
          <nav className="flex overflow-x-auto space-x-4 sm:space-x-8 px-3 sm:px-6 scrollbar-hide">
            {tabs.map((tab) => {
              const Icon = tab.icon;
              return (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`flex items-center space-x-1 sm:space-x-2 py-3 sm:py-4 px-1 border-b-2 font-medium text-xs sm:text-sm whitespace-nowrap ${
                    activeTab === tab.id
                      ? 'border-blue-500 text-blue-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }`}
                >
                  <Icon className="h-3 w-3 sm:h-4 sm:w-4 flex-shrink-0" />
                  <span className="hidden sm:inline">{tab.label}</span>
                  <span className="sm:hidden">{tab.label.split(' ')[0]}</span>
                </button>
              );
            })}
          </nav>
        </div>

        <div className="p-6">
          {activeTab === 'profile' && <ProfileSettings onSave={handleSave} />}
          {activeTab === 'notifications' && <NotificationSettings onSave={handleSave} />}
          {activeTab === 'security' && <SecuritySettings onSave={handleSave} />}
          {activeTab === 'preferences' && <PreferenceSettings onSave={handleSave} />}
          {activeTab === 'roles' && <RolePermissionSettings onSave={handleSave} />}
          {activeTab === 'help' && <HelpSupport />}
        </div>
      </div>
    </div>
  );
};

const ProfileSettings: React.FC<{ onSave: () => void }> = ({ onSave }) => {
  return (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-medium text-gray-900">Profile Information</h3>
        <p className="text-sm text-gray-600">Update your personal information and contact details.</p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">First Name</label>
          <input
            type="text"
            defaultValue="John"
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          />
        </div>
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">Last Name</label>
          <input
            type="text"
            defaultValue="Doe"
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          />
        </div>
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">Email</label>
          <input
            type="email"
            defaultValue="<EMAIL>"
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          />
        </div>
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">Phone</label>
          <input
            type="tel"
            defaultValue="+27 11 123 4567"
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          />
        </div>
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">Department</label>
          <select className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
            <option>Transport Department</option>
            <option>Finance Department</option>
            <option>Operations</option>
          </select>
        </div>
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">Role</label>
          <input
            type="text"
            value="Fleet Manager"
            disabled
            className="w-full px-3 py-2 border border-gray-300 rounded-lg bg-gray-50 text-gray-500"
          />
        </div>
      </div>

      <div className="flex justify-end">
        <button
          onClick={onSave}
          className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
        >
          Save Changes
        </button>
      </div>
    </div>
  );
};

const NotificationSettings: React.FC<{ onSave: () => void }> = ({ onSave }) => {
  const [settings, setSettings] = useState({
    emailNotifications: true,
    smsNotifications: false,
    pushNotifications: true,
    maintenanceAlerts: true,
    slaBreachAlerts: true,
    invoiceAlerts: true,
    trafficFineAlerts: true,
    weeklyReports: true,
    monthlyReports: false,
  });

  const handleToggle = (key: keyof typeof settings) => {
    setSettings(prev => ({ ...prev, [key]: !prev[key] }));
  };

  return (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-medium text-gray-900">Notification Preferences</h3>
        <p className="text-sm text-gray-600">Choose how you want to receive notifications.</p>
      </div>

      <div className="space-y-4">
        <div className="border-b border-gray-200 pb-4">
          <h4 className="font-medium text-gray-900 mb-3">Delivery Methods</h4>
          <div className="space-y-3">
            {[
              { key: 'emailNotifications', label: 'Email Notifications', description: 'Receive notifications via email' },
              { key: 'smsNotifications', label: 'SMS Notifications', description: 'Receive critical alerts via SMS' },
              { key: 'pushNotifications', label: 'Push Notifications', description: 'Browser push notifications' },
            ].map(({ key, label, description }) => (
              <div key={key} className="flex items-center justify-between">
                <div>
                  <div className="font-medium text-gray-900">{label}</div>
                  <div className="text-sm text-gray-600">{description}</div>
                </div>
                <label className="relative inline-flex items-center cursor-pointer">
                  <input
                    type="checkbox"
                    checked={settings[key as keyof typeof settings]}
                    onChange={() => handleToggle(key as keyof typeof settings)}
                    className="sr-only peer"
                  />
                  <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                </label>
              </div>
            ))}
          </div>
        </div>

        <div className="border-b border-gray-200 pb-4">
          <h4 className="font-medium text-gray-900 mb-3">Alert Types</h4>
          <div className="space-y-3">
            {[
              { key: 'maintenanceAlerts', label: 'Maintenance Alerts', description: 'Vehicle service due notifications' },
              { key: 'slaBreachAlerts', label: 'SLA Breach Alerts', description: 'Service level agreement violations' },
              { key: 'invoiceAlerts', label: 'Invoice Alerts', description: 'Payment due and overdue notices' },
              { key: 'trafficFineAlerts', label: 'Traffic Fine Alerts', description: 'New traffic violation notifications' },
            ].map(({ key, label, description }) => (
              <div key={key} className="flex items-center justify-between">
                <div>
                  <div className="font-medium text-gray-900">{label}</div>
                  <div className="text-sm text-gray-600">{description}</div>
                </div>
                <label className="relative inline-flex items-center cursor-pointer">
                  <input
                    type="checkbox"
                    checked={settings[key as keyof typeof settings]}
                    onChange={() => handleToggle(key as keyof typeof settings)}
                    className="sr-only peer"
                  />
                  <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                </label>
              </div>
            ))}
          </div>
        </div>

        <div>
          <h4 className="font-medium text-gray-900 mb-3">Reports</h4>
          <div className="space-y-3">
            {[
              { key: 'weeklyReports', label: 'Weekly Reports', description: 'Fleet performance summary every week' },
              { key: 'monthlyReports', label: 'Monthly Reports', description: 'Comprehensive monthly fleet analysis' },
            ].map(({ key, label, description }) => (
              <div key={key} className="flex items-center justify-between">
                <div>
                  <div className="font-medium text-gray-900">{label}</div>
                  <div className="text-sm text-gray-600">{description}</div>
                </div>
                <label className="relative inline-flex items-center cursor-pointer">
                  <input
                    type="checkbox"
                    checked={settings[key as keyof typeof settings]}
                    onChange={() => handleToggle(key as keyof typeof settings)}
                    className="sr-only peer"
                  />
                  <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                </label>
              </div>
            ))}
          </div>
        </div>
      </div>

      <div className="flex justify-end">
        <button
          onClick={onSave}
          className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
        >
          Save Preferences
        </button>
      </div>
    </div>
  );
};

const SecuritySettings: React.FC<{ onSave: () => void }> = ({ onSave }) => {
  return (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-medium text-gray-900">Security Settings</h3>
        <p className="text-sm text-gray-600">Manage your account security and authentication.</p>
      </div>

      <div className="space-y-6">
        <div className="border border-gray-200 rounded-lg p-4">
          <div className="flex items-center justify-between mb-4">
            <div>
              <h4 className="font-medium text-gray-900">Change Password</h4>
              <p className="text-sm text-gray-600">Update your account password</p>
            </div>
            <Key className="h-5 w-5 text-gray-400" />
          </div>
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Current Password</label>
              <input
                type="password"
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">New Password</label>
              <input
                type="password"
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Confirm New Password</label>
              <input
                type="password"
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>
            <button
              onClick={onSave}
              className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
            >
              Update Password
            </button>
          </div>
        </div>

        <div className="border border-gray-200 rounded-lg p-4">
          <div className="flex items-center justify-between mb-4">
            <div>
              <h4 className="font-medium text-gray-900">Two-Factor Authentication</h4>
              <p className="text-sm text-gray-600">Add an extra layer of security to your account</p>
            </div>
            <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
              Disabled
            </span>
          </div>
          <button className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors">
            Enable 2FA
          </button>
        </div>

        <div className="border border-gray-200 rounded-lg p-4">
          <div className="flex items-center justify-between mb-4">
            <div>
              <h4 className="font-medium text-gray-900">Active Sessions</h4>
              <p className="text-sm text-gray-600">Manage your active login sessions</p>
            </div>
          </div>
          <div className="space-y-3">
            <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
              <div>
                <div className="font-medium text-gray-900">Current Session</div>
                <div className="text-sm text-gray-600">Chrome on Windows • Johannesburg, South Africa</div>
                <div className="text-xs text-gray-500">Last active: Now</div>
              </div>
              <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                Current
              </span>
            </div>
            <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
              <div>
                <div className="font-medium text-gray-900">Mobile Session</div>
                <div className="text-sm text-gray-600">Safari on iPhone • Cape Town, South Africa</div>
                <div className="text-xs text-gray-500">Last active: 2 hours ago</div>
              </div>
              <button className="text-sm text-red-600 hover:text-red-700">Revoke</button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

const PreferenceSettings: React.FC<{ onSave: () => void }> = ({ onSave }) => {
  return (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-medium text-gray-900">Preferences</h3>
        <p className="text-sm text-gray-600">Customize your application experience.</p>
      </div>

      <div className="space-y-6">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">Language</label>
          <select className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
            <option>English</option>
            <option>Afrikaans</option>
            <option>Zulu</option>
            <option>Xhosa</option>
          </select>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">Timezone</label>
          <select className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
            <option>Africa/Johannesburg (SAST)</option>
            <option>Africa/Cape_Town (SAST)</option>
          </select>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">Date Format</label>
          <select className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
            <option>DD/MM/YYYY</option>
            <option>MM/DD/YYYY</option>
            <option>YYYY-MM-DD</option>
          </select>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">Currency</label>
          <select className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
            <option>ZAR (South African Rand)</option>
            <option>USD (US Dollar)</option>
            <option>EUR (Euro)</option>
          </select>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">Items per Page</label>
          <select className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
            <option>10</option>
            <option>25</option>
            <option>50</option>
            <option>100</option>
          </select>
        </div>
      </div>

      <div className="flex justify-end">
        <button
          onClick={onSave}
          className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
        >
          Save Preferences
        </button>
      </div>
    </div>
  );
};

const HelpSupport: React.FC = () => {
  return (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-medium text-gray-900">Help & Support</h3>
        <p className="text-sm text-gray-600">Get help and support for the fleet management system.</p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="border border-gray-200 rounded-lg p-4">
          <h4 className="font-medium text-gray-900 mb-2">Documentation</h4>
          <p className="text-sm text-gray-600 mb-4">Access user guides and system documentation</p>
          <button className="text-blue-600 hover:text-blue-700 text-sm font-medium">
            View Documentation →
          </button>
        </div>

        <div className="border border-gray-200 rounded-lg p-4">
          <h4 className="font-medium text-gray-900 mb-2">Contact Support</h4>
          <p className="text-sm text-gray-600 mb-4">Get help from our support team</p>
          <button className="text-blue-600 hover:text-blue-700 text-sm font-medium">
            Submit Ticket →
          </button>
        </div>

        <div className="border border-gray-200 rounded-lg p-4">
          <h4 className="font-medium text-gray-900 mb-2">System Status</h4>
          <p className="text-sm text-gray-600 mb-4">Check current system status and updates</p>
          <div className="flex items-center space-x-2">
            <div className="w-2 h-2 bg-green-500 rounded-full"></div>
            <span className="text-sm text-green-600">All systems operational</span>
          </div>
        </div>

        <div className="border border-gray-200 rounded-lg p-4">
          <h4 className="font-medium text-gray-900 mb-2">Training Resources</h4>
          <p className="text-sm text-gray-600 mb-4">Access training materials and videos</p>
          <button className="text-blue-600 hover:text-blue-700 text-sm font-medium">
            View Training →
          </button>
        </div>
      </div>

      <div className="border border-gray-200 rounded-lg p-4">
        <h4 className="font-medium text-gray-900 mb-2">System Information</h4>
        <div className="grid grid-cols-2 gap-4 text-sm">
          <div>
            <span className="text-gray-600">Version:</span>
            <span className="ml-2 font-medium">RT46-2026 v1.0.0</span>
          </div>
          <div>
            <span className="text-gray-600">Last Updated:</span>
            <span className="ml-2 font-medium">2024-01-15</span>
          </div>
          <div>
            <span className="text-gray-600">Environment:</span>
            <span className="ml-2 font-medium">Production</span>
          </div>
          <div>
            <span className="text-gray-600">Region:</span>
            <span className="ml-2 font-medium">South Africa</span>
          </div>
        </div>
      </div>
    </div>
  );
};

const RolePermissionSettings: React.FC<{ onSave: () => void }> = ({ onSave }) => {
  const [selectedUser, setSelectedUser] = useState<string>('');
  const [selectedRole, setSelectedRole] = useState<string>('');
  const [showAddUser, setShowAddUser] = useState(false);
  const [newUserData, setNewUserData] = useState({
    name: '',
    email: '',
    role: '',
    department: ''
  });

  // Mock data - replace with real API
  const roles = [
    {
      id: 'super_admin',
      name: 'Super Admin',
      description: 'Full system access with all permissions',
      userCount: 2,
      permissions: ['ALL']
    },
    {
      id: 'admin',
      name: 'Admin',
      description: 'Administrative access to most system functions',
      userCount: 5,
      permissions: ['VEHICLE_MANAGE', 'USER_MANAGE', 'WORK_ORDER_MANAGE', 'VENDOR_MANAGE', 'AUDIT_VIEW']
    },
    {
      id: 'fleet_manager',
      name: 'Fleet Manager',
      description: 'Manage fleet operations and maintenance',
      userCount: 8,
      permissions: ['VEHICLE_VIEW', 'VEHICLE_UPDATE', 'WORK_ORDER_MANAGE', 'VENDOR_VIEW', 'PAYMENT_VIEW']
    },
    {
      id: 'transport_officer',
      name: 'Transport Officer',
      description: 'Handle daily transport operations',
      userCount: 12,
      permissions: ['VEHICLE_VIEW', 'VEHICLE_UPDATE', 'WORK_ORDER_CREATE', 'VENDOR_VIEW']
    },
    {
      id: 'driver',
      name: 'Driver',
      description: 'Basic vehicle access and reporting',
      userCount: 45,
      permissions: ['VEHICLE_VIEW', 'WORK_ORDER_VIEW']
    },
    {
      id: 'vendor',
      name: 'Vendor',
      description: 'Access to assigned work orders and payments',
      userCount: 23,
      permissions: ['WORK_ORDER_VIEW', 'PAYMENT_VIEW']
    }
  ];

  const users = [
    { id: '1', name: 'John Doe', email: '<EMAIL>', role: 'Fleet Manager', department: 'Public Works', status: 'Active' },
    { id: '2', name: 'Jane Smith', email: '<EMAIL>', role: 'Transport Officer', department: 'Health', status: 'Active' },
    { id: '3', name: 'Mike Johnson', email: '<EMAIL>', role: 'Driver', department: 'Education', status: 'Active' },
    { id: '4', name: 'Sarah Wilson', email: '<EMAIL>', role: 'Admin', department: 'IT', status: 'Inactive' },
  ];

  const permissions = [
    { id: 'VEHICLE_VIEW', name: 'View Vehicles', category: 'Vehicle Management' },
    { id: 'VEHICLE_CREATE', name: 'Create Vehicles', category: 'Vehicle Management' },
    { id: 'VEHICLE_UPDATE', name: 'Update Vehicles', category: 'Vehicle Management' },
    { id: 'VEHICLE_DELETE', name: 'Delete Vehicles', category: 'Vehicle Management' },
    { id: 'WORK_ORDER_VIEW', name: 'View Work Orders', category: 'Work Order Management' },
    { id: 'WORK_ORDER_CREATE', name: 'Create Work Orders', category: 'Work Order Management' },
    { id: 'WORK_ORDER_ASSIGN', name: 'Assign Work Orders', category: 'Work Order Management' },
    { id: 'WORK_ORDER_APPROVE', name: 'Approve Work Orders', category: 'Work Order Management' },
    { id: 'VENDOR_VIEW', name: 'View Vendors', category: 'Vendor Management' },
    { id: 'VENDOR_MANAGE', name: 'Manage Vendors', category: 'Vendor Management' },
    { id: 'USER_MANAGE', name: 'Manage Users', category: 'User Management' },
    { id: 'PAYMENT_VIEW', name: 'View Payments', category: 'Financial' },
    { id: 'PAYMENT_APPROVE', name: 'Approve Payments', category: 'Financial' },
    { id: 'AUDIT_VIEW', name: 'View Audit Logs', category: 'Compliance' },
    { id: 'SYSTEM_CONFIG', name: 'System Configuration', category: 'Administration' },
  ];

  const getPermissionsByCategory = () => {
    const grouped = permissions.reduce((acc, permission) => {
      if (!acc[permission.category]) {
        acc[permission.category] = [];
      }
      acc[permission.category].push(permission);
      return acc;
    }, {} as Record<string, typeof permissions>);
    return grouped;
  };

  const handleAddUser = () => {
    console.log('Adding user:', newUserData);
    setShowAddUser(false);
    setNewUserData({ name: '', email: '', role: '', department: '' });
    onSave();
  };

  const handleRoleChange = (userId: string, newRole: string) => {
    console.log('Changing role for user:', userId, 'to:', newRole);
    onSave();
  };

  return (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-medium text-gray-900">Role & Permission Management</h3>
        <p className="text-sm text-gray-600">Manage user roles and system permissions.</p>
      </div>

      {/* Role Overview */}
      <div>
        <h4 className="font-medium text-gray-900 mb-3">System Roles</h4>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {roles.map((role) => (
            <div key={role.id} className="border border-gray-200 rounded-lg p-4">
              <div className="flex items-center justify-between mb-2">
                <h5 className="font-medium text-gray-900">{role.name}</h5>
                <span className="text-sm text-gray-500">{role.userCount} users</span>
              </div>
              <p className="text-sm text-gray-600 mb-3">{role.description}</p>
              <div className="flex flex-wrap gap-1">
                {role.permissions.slice(0, 3).map((permission) => (
                  <span key={permission} className="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded">
                    {permission === 'ALL' ? 'All Permissions' : permission.replace('_', ' ')}
                  </span>
                ))}
                {role.permissions.length > 3 && (
                  <span className="px-2 py-1 bg-gray-100 text-gray-600 text-xs rounded">
                    +{role.permissions.length - 3} more
                  </span>
                )}
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* User Management */}
      <div>
        <div className="flex items-center justify-between mb-3">
          <h4 className="font-medium text-gray-900">User Management</h4>
          <button
            onClick={() => setShowAddUser(true)}
            className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors text-sm"
          >
            Add User
          </button>
        </div>

        <div className="border border-gray-200 rounded-lg overflow-hidden">
          <table className="w-full">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-4 py-3 text-left text-sm font-medium text-gray-900">User</th>
                <th className="px-4 py-3 text-left text-sm font-medium text-gray-900">Role</th>
                <th className="px-4 py-3 text-left text-sm font-medium text-gray-900">Department</th>
                <th className="px-4 py-3 text-left text-sm font-medium text-gray-900">Status</th>
                <th className="px-4 py-3 text-left text-sm font-medium text-gray-900">Actions</th>
              </tr>
            </thead>
            <tbody className="divide-y divide-gray-200">
              {users.map((user) => (
                <tr key={user.id}>
                  <td className="px-4 py-3">
                    <div>
                      <div className="font-medium text-gray-900">{user.name}</div>
                      <div className="text-sm text-gray-500">{user.email}</div>
                    </div>
                  </td>
                  <td className="px-4 py-3">
                    <select
                      value={user.role}
                      onChange={(e) => handleRoleChange(user.id, e.target.value)}
                      className="text-sm border border-gray-300 rounded px-2 py-1"
                    >
                      {roles.map((role) => (
                        <option key={role.id} value={role.name}>{role.name}</option>
                      ))}
                    </select>
                  </td>
                  <td className="px-4 py-3 text-sm text-gray-900">{user.department}</td>
                  <td className="px-4 py-3">
                    <span className={`px-2 py-1 text-xs rounded-full ${
                      user.status === 'Active' 
                        ? 'bg-green-100 text-green-800' 
                        : 'bg-red-100 text-red-800'
                    }`}>
                      {user.status}
                    </span>
                  </td>
                  <td className="px-4 py-3">
                    <button className="text-blue-600 hover:text-blue-800 text-sm">Edit</button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>

      {/* Permission Matrix */}
      <div>
        <h4 className="font-medium text-gray-900 mb-3">Permission Matrix</h4>
        <div className="border border-gray-200 rounded-lg overflow-hidden">
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-4 py-3 text-left text-sm font-medium text-gray-900 sticky left-0 bg-gray-50">Permission</th>
                  {roles.slice(0, 5).map((role) => (
                    <th key={role.id} className="px-4 py-3 text-center text-sm font-medium text-gray-900 min-w-[120px]">
                      {role.name}
                    </th>
                  ))}
                </tr>
              </thead>
              <tbody className="divide-y divide-gray-200">
                {Object.entries(getPermissionsByCategory()).map(([category, categoryPermissions]) => (
                  <React.Fragment key={category}>
                    <tr className="bg-gray-25">
                      <td colSpan={6} className="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-25">
                        {category}
                      </td>
                    </tr>
                    {categoryPermissions.map((permission) => (
                      <tr key={permission.id}>
                        <td className="px-4 py-3 text-sm text-gray-900 sticky left-0 bg-white">
                          {permission.name}
                        </td>
                        {roles.slice(0, 5).map((role) => (
                          <td key={role.id} className="px-4 py-3 text-center">
                            <input
                              type="checkbox"
                              checked={role.permissions.includes('ALL') || role.permissions.includes(permission.id)}
                              onChange={() => {}}
                              className="h-4 w-4 text-blue-600 rounded border-gray-300"
                            />
                          </td>
                        ))}
                      </tr>
                    ))}
                  </React.Fragment>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      </div>

      {/* Add User Modal */}
      {showAddUser && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-md">
            <h3 className="text-lg font-medium text-gray-900 mb-4">Add New User</h3>
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Full Name</label>
                <input
                  type="text"
                  value={newUserData.name}
                  onChange={(e) => setNewUserData(prev => ({ ...prev, name: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Email</label>
                <input
                  type="email"
                  value={newUserData.email}
                  onChange={(e) => setNewUserData(prev => ({ ...prev, email: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Role</label>
                <select
                  value={newUserData.role}
                  onChange={(e) => setNewUserData(prev => ({ ...prev, role: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                >
                  <option value="">Select Role</option>
                  {roles.map((role) => (
                    <option key={role.id} value={role.name}>{role.name}</option>
                  ))}
                </select>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Department</label>
                <select
                  value={newUserData.department}
                  onChange={(e) => setNewUserData(prev => ({ ...prev, department: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                >
                  <option value="">Select Department</option>
                  <option value="Public Works">Public Works</option>
                  <option value="Health">Health</option>
                  <option value="Education">Education</option>
                  <option value="Transport">Transport</option>
                  <option value="IT">IT</option>
                </select>
              </div>
            </div>
            <div className="flex space-x-3 mt-6">
              <button
                onClick={handleAddUser}
                className="flex-1 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
              >
                Add User
              </button>
              <button
                onClick={() => setShowAddUser(false)}
                className="flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
              >
                Cancel
              </button>
            </div>
          </div>
        </div>
      )}

      <div className="flex justify-end">
        <button
          onClick={onSave}
          className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
        >
          Save Changes
        </button>
      </div>
    </div>
  );
};

export default SettingsPage;




