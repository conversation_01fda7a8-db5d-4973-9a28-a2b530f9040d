---
type: "manual"
---

# React Native Mobile Development Rules for RT46-2026

## React Native Architecture Principles

### Mobile-First Design Philosophy
- **Cross-Platform Consistency** - Shared business logic between iOS and Android
- **Platform-Specific Adaptations** - Respect platform conventions and guidelines
- **Performance-First Approach** - Optimize for mobile hardware constraints
- **Offline-First Architecture** - Support offline operations for field workers
- **Native Integration** - Use native modules for platform-specific features
- **Responsive Layouts** - Adapt to different screen sizes and orientations

### Component Architecture
- **Shared Component Library** - Consistent components across screens
- **Screen-Based Navigation** - Clear screen hierarchy and navigation flows
- **Modular Feature Structure** - Self-contained feature modules
- **Service Layer Abstraction** - Abstract API calls and data management
- **State Management** - Redux for complex state, local state for UI concerns
- **Error Boundary Implementation** - Graceful error handling at screen level

### Project Structure
```
src/
├── components/           # Shared mobile components
│   ├── forms/           # Form components (inputs, pickers, validators)
│   ├── navigation/      # Navigation components and config
│   ├── ui/              # Basic UI components (buttons, cards, lists)
│   └── layout/          # Layout components (headers, containers)
├── screens/             # Screen components
│   ├── auth/            # Authentication screens
│   ├── vehicles/        # Vehicle management screens
│   ├── inspections/     # Vehicle inspection screens
│   ├── workorders/      # Work order screens
│   └── dashboard/       # Dashboard and overview screens
├── services/            # API services and business logic
├── store/               # Redux store and slices
├── navigation/          # Navigation configuration
├── hooks/               # Custom React Native hooks
├── utils/               # Utility functions
├── assets/              # Images, icons, fonts
└── constants/           # App constants and configuration
```

## Platform-Specific Considerations

### iOS Development Guidelines
- **Human Interface Guidelines** - Follow Apple's design principles
- **Safe Area Handling** - Proper layout for notched devices
- **iOS Navigation Patterns** - Use iOS-appropriate navigation styles
- **Permission Handling** - Implement iOS permission requests properly
- **Background App Refresh** - Handle app state changes gracefully
- **Push Notifications** - Configure iOS push notification certificates

### Android Development Guidelines
- **Material Design Guidelines** - Follow Google's design principles
- **Status Bar Handling** - Proper status bar configuration
- **Android Navigation Patterns** - Hardware back button support
- **Permission Model** - Handle Android 6+ runtime permissions
- **Background Processing** - Implement background tasks properly
- **Deep Linking** - Support Android intent handling

### Cross-Platform Optimization
- **Shared Business Logic** - Common data layer and business rules
- **Platform Detection** - Use Platform.OS for platform-specific code
- **Conditional Rendering** - Platform-specific UI components when needed
- **Native Module Integration** - Use native modules for performance-critical features
- **Bundle Size Optimization** - Minimize app size for faster downloads
- **Performance Monitoring** - Cross-platform performance tracking

## Navigation and User Experience

### React Navigation Implementation
- **Stack Navigation** - Primary navigation pattern for mobile
- **Tab Navigation** - Bottom tabs for main app sections
- **Drawer Navigation** - Side menu for secondary navigation
- **Modal Navigation** - Overlay screens for forms and details
- **Deep Linking** - URL-based navigation for web-like behavior
- **Navigation Guards** - Authentication and permission-based routing

### Mobile UX Patterns
- **Touch-Friendly Design** - Minimum 44pt touch targets
- **Gesture Support** - Swipe, pull-to-refresh, long-press interactions
- **Loading States** - Clear loading indicators and skeleton screens
- **Error Handling** - User-friendly error messages and recovery actions
- **Form Optimization** - Mobile-optimized form inputs and validation
- **Accessibility Support** - Screen reader and accessibility features

### Offline Capabilities
- **Local Data Storage** - SQLite or Realm for offline data
- **Sync Mechanisms** - Background sync when connection is restored
- **Offline Indicators** - Clear visual feedback for offline state
- **Cached Content** - Cache critical data and images locally
- **Queue Management** - Queue actions for execution when online
- **Conflict Resolution** - Handle data conflicts during sync

## Data Management and Persistence

### Local Storage Strategy
- **AsyncStorage** - Simple key-value storage for preferences
- **SQLite** - Relational database for complex offline data
- **Redux Persist** - Persist Redux state across app restarts
- **Image Caching** - Cache frequently accessed images locally
- **Document Storage** - Local file system for documents and photos
- **Encryption** - Encrypt sensitive data stored locally

### API Integration Patterns
- **HTTP Client Configuration** - Axios with interceptors and retry logic
- **Request/Response Transformation** - Normalize API data formats
- **Error Handling** - Consistent error handling across all API calls
- **Token Management** - Automatic token refresh and storage
- **Network State Handling** - Detect and handle network connectivity
- **Background Sync** - Sync data when app returns to foreground

### State Management
- **Redux Store Configuration** - Optimized for mobile performance
- **Action Creators** - Async actions for API calls and side effects
- **Selectors** - Memoized selectors for efficient data access
- **Middleware** - Custom middleware for logging and analytics
- **Persistence** - Selective state persistence for offline support
- **DevTools Integration** - Flipper integration for debugging

## Native Features Integration

### Device Hardware Access
- **Camera Integration** - Photo capture for vehicle inspections
- **GPS/Location Services** - Real-time location tracking and mapping
- **QR Code Scanning** - Vehicle identification and work order scanning
- **Biometric Authentication** - Fingerprint and face ID for security
- **Push Notifications** - Real-time alerts and updates
- **Background Location** - Track vehicle location when app is backgrounded

### File and Media Handling
- **Photo Capture and Processing** - Vehicle inspection photos
- **Document Management** - PDF viewing and document storage
- **File Upload/Download** - Sync documents with server
- **Image Compression** - Optimize images before upload
- **Gallery Integration** - Access device photo gallery
- **File Sharing** - Share reports and documents with other apps

### Security Features
- **Secure Storage** - Use Keychain (iOS) or Keystore (Android)
- **Certificate Pinning** - Prevent man-in-the-middle attacks
- **Root/Jailbreak Detection** - Detect compromised devices
- **App Transport Security** - Enforce HTTPS connections
- **Code Obfuscation** - Protect app code from reverse engineering
- **Runtime Application Self-Protection** - Detect and respond to threats

## Performance Optimization

### Rendering Performance
- **FlatList Optimization** - Efficient rendering of large lists
- **Image Optimization** - Resize and compress images appropriately
- **Memory Management** - Prevent memory leaks and excessive usage
- **Bridge Communication** - Minimize React Native bridge calls
- **Native Module Performance** - Use native modules for intensive operations
- **Animation Performance** - Use native animations where possible

### Bundle and Startup Optimization
- **Bundle Splitting** - Code splitting for faster startup
- **Lazy Loading** - Load screens and components on demand
- **Asset Optimization** - Optimize images and other assets
- **Startup Time** - Minimize time to interactive
- **Binary Size** - Keep app size under platform limits
- **Update Mechanisms** - Support over-the-air updates

### Network and Data Optimization
- **Request Batching** - Combine multiple API requests
- **Data Prefetching** - Load anticipated data in background
- **Compression** - Use gzip compression for API responses
- **Caching Strategies** - Implement appropriate caching levels
- **Background Sync** - Efficient background data synchronization
- **Bandwidth Optimization** - Optimize for poor network conditions

## Testing Strategy

### Unit Testing
- **Component Testing** - Test React Native components in isolation
- **Hook Testing** - Test custom hooks with React Native Testing Library
- **Service Testing** - Test API services and business logic
- **Utility Testing** - Test helper functions and utilities
- **Redux Testing** - Test actions, reducers, and selectors
- **Mock Implementation** - Mock native modules and dependencies

### Integration Testing
- **Screen Flow Testing** - Test complete user workflows
- **Navigation Testing** - Test screen transitions and deep linking
- **API Integration Testing** - Test API calls and error handling
- **Storage Testing** - Test local data persistence and retrieval
- **Permission Testing** - Test device permission flows
- **Background Testing** - Test app behavior in background states

### End-to-End Testing
- **Detox Framework** - Automated E2E testing for both platforms
- **Device Testing** - Test on real devices and simulators
- **Platform-Specific Testing** - Test iOS and Android separately
- **Performance Testing** - Test app performance under load
- **Memory Testing** - Test for memory leaks and excessive usage
- **Network Testing** - Test offline scenarios and poor connectivity

## Development and Debugging

### Development Environment
- **Metro Configuration** - Optimize bundler for development
- **Flipper Integration** - Debugging and performance profiling
- **Hot Reloading** - Fast development iteration
- **Remote Debugging** - Debug JavaScript in Chrome DevTools
- **Native Debugging** - Xcode and Android Studio integration
- **Simulator Management** - Efficient simulator/emulator usage

### Error Monitoring and Analytics
- **Crash Reporting** - Automatic crash detection and reporting
- **Error Boundaries** - Graceful error handling with recovery
- **Performance Monitoring** - Track app performance metrics
- **User Analytics** - Track user behavior and feature usage
- **Custom Logging** - Structured logging for debugging
- **Remote Debugging** - Debug production issues remotely

### Build and Deployment
- **Continuous Integration** - Automated builds for both platforms
- **Code Signing** - iOS and Android app signing automation
- **Beta Distribution** - TestFlight and Google Play Console beta
- **Release Management** - Coordinate releases across platforms
- **Monitoring** - Post-release monitoring and rollback procedures
- **Update Distribution** - Over-the-air updates and feature flags

## Security and Compliance

### Mobile Security Best Practices
- **Data Encryption** - Encrypt all sensitive data at rest
- **Network Security** - Use HTTPS and certificate pinning
- **Authentication Security** - Secure token storage and biometric auth
- **Session Management** - Proper session handling and timeout
- **Input Validation** - Validate all user inputs and API responses
- **Code Protection** - Obfuscation and anti-tampering measures

### Government Compliance
- **POPIA Compliance** - Personal data protection in mobile context
- **Device Management** - Support for enterprise device management
- **Audit Logging** - Comprehensive logging for compliance
- **Data Residency** - Ensure data stays within required boundaries
- **Access Controls** - Role-based access control implementation
- **Privacy Controls** - User control over data collection and usage

## Common Anti-Patterns to Avoid

### Performance Anti-Patterns
- **Over-rendering** - Unnecessary component re-renders
- **Memory Leaks** - Unmanaged subscriptions and listeners
- **Bridge Overuse** - Excessive JavaScript-to-native communication
- **Blocking Operations** - Synchronous operations on main thread
- **Excessive Bundle Size** - Including unnecessary dependencies
- **Poor List Performance** - Inefficient list rendering

### Architecture Anti-Patterns
- **Platform Coupling** - Tight coupling to specific platform features
- **State Management Chaos** - Inconsistent state management patterns
- **Navigation Complexity** - Overly complex navigation hierarchies
- **Tight Coupling** - Components tightly coupled to data sources
- **Synchronous Storage** - Blocking storage operations
- **Global State Overuse** - Everything in Redux instead of local state

### Security Anti-Patterns
- **Insecure Storage** - Storing sensitive data in plain text
- **Weak Authentication** - Poor token management and session handling
- **Network Vulnerabilities** - Unencrypted or improperly validated requests
- **Code Exposure** - Sensitive logic exposed in JavaScript bundle
- **Permission Overreach** - Requesting unnecessary device permissions
- **Insufficient Validation** - Trusting client-side validation only

