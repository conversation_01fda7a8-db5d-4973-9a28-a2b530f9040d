export interface WorkOrder {
  id: string;
  workOrderNumber: string;
  vehicleId: string;
  vehicleRegistration: string;
  title: string;
  description: string;
  type: 'maintenance' | 'repair' | 'inspection' | 'accident';
  priority: 'low' | 'medium' | 'high' | 'urgent';
  status: 'pending' | 'approved' | 'in_progress' | 'completed' | 'cancelled';
  requestedBy: string;
  assignedTo?: string;
  vendorId?: string;
  vendorName?: string;
  estimatedCost?: number;
  actualCost?: number;
  estimatedDuration?: number; // in hours
  scheduledDate?: string;
  completedDate?: string;
  notes?: string;
  attachments?: string[];
  createdAt: string;
  updatedAt: string;
}

export interface WorkOrderFormData {
  vehicleId: string;
  title: string;
  description: string;
  type: WorkOrder['type'];
  priority: WorkOrder['priority'];
  estimatedCost?: number;
  estimatedDuration?: number;
  scheduledDate?: string;
  notes?: string;
}

export interface WorkOrderFilters {
  status?: WorkOrder['status'];
  type?: WorkOrder['type'];
  priority?: WorkOrder['priority'];
  vehicleId?: string;
  vendorId?: string;
  search?: string;
}