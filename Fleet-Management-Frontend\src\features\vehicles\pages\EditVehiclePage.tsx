import React from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { ArrowLeft } from 'lucide-react';
import { VehicleForm, type VehicleFormData } from '../components/VehicleForm';
import { useGetVehicleQuery, useUpdateVehicleMutation } from '@/store/api/vehicleApi';
import toast from 'react-hot-toast';

export const EditVehiclePage: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();

  const {
    data: vehicle,
    isLoading: fetchLoading,
    error
  } = useGetVehicleQuery(id!, {
    skip: !id
  });

  const [updateVehicle, { isLoading: updateLoading }] = useUpdateVehicleMutation();

  const handleSubmit = async (data: VehicleFormData) => {
    if (!id) return;

    try {
      // Transform frontend data to backend format
      const vehicleRequest = {
        registration_number: data.registrationNumber,
        make: data.make,
        model: data.model,
        year: data.year,
        vin: data.vin,
        engine_number: data.engineNumber,
        fuel_type: data.fuelType,
        vehicle_type: data.category,
        department: data.department,
        current_mileage: data.odometerReading,
        purchase_date: data.purchaseDate,
        purchase_price: data.purchasePrice,
        insurance_expiry: data.insuranceExpiry,
        license_expiry: data.licenseExpiry,
        assigned_driver_id: data.assignedDriver || undefined,
        location: data.location || '',
      };

      await updateVehicle({ id, data: vehicleRequest }).unwrap();
      toast.success('Vehicle updated successfully');
      navigate(`/vehicles/${id}`);
    } catch (error: any) {
      toast.error(error.data?.message || 'Failed to update vehicle');
    }
  };

  const handleCancel = () => {
    navigate(`/vehicles/${id}`);
  };

  if (fetchLoading) {
    return <div className="container mx-auto px-4 py-6">Loading vehicle...</div>;
  }

  if (error || !vehicle) {
    return <div className="container mx-auto px-4 py-6">Vehicle not found</div>;
  }

  // Transform backend data to frontend format
  const initialData: Partial<VehicleFormData> = {
    registrationNumber: vehicle.registration_number,
    make: vehicle.make,
    model: vehicle.model,
    year: vehicle.year,
    vin: vehicle.vin || '',
    engineNumber: vehicle.engine_number || '',
    fuelType: vehicle.fuel_type as VehicleFormData['fuelType'],
    category: vehicle.vehicle_type as VehicleFormData['category'],
    department: vehicle.department,
    odometerReading: vehicle.current_mileage || 0,
    insuranceExpiry: vehicle.insurance_expiry || '',
    licenseExpiry: vehicle.license_expiry || '',
    purchaseDate: vehicle.purchase_date || '',
    purchasePrice: vehicle.purchase_price || 0,
    assignedDriver: vehicle.assigned_driver_id || '',
    location: vehicle.location || '',
  };

  return (
    <div className="container mx-auto px-4 py-6">
      {/* Header */}
      <div className="mb-6">
        <Button
          variant="ghost"
          onClick={() => navigate(`/vehicles/${id}`)}
          className="mb-4"
        >
          <ArrowLeft className="h-4 w-4 mr-2" />
          Back to Vehicle Details
        </Button>
        
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Edit Vehicle</h1>
          <p className="text-gray-600">
            Update information for {vehicle.registration_number}
          </p>
        </div>
      </div>

      {/* Form */}
      <Card className="w-full">
        <CardHeader>
          <CardTitle>Vehicle Information</CardTitle>
        </CardHeader>
        <CardContent>
          <VehicleForm
            initialData={initialData}
            onSubmit={handleSubmit}
            onCancel={handleCancel}
            isLoading={updateLoading}
            submitLabel="Update Vehicle"
          />
        </CardContent>
      </Card>
    </div>
  );
};














