import React, { useState, useEffect } from 'react';
import { useAppSelector } from '@/hooks/redux';
import { 
  Car, 
  Calendar, 
  Fuel, 
  MapPin, 
  Clock, 
  AlertTriangle,
  CheckCircle,
  Navigation,
  Phone,
  Settings
} from 'lucide-react';
import { useNotifications } from '@/hooks/useNotifications';
import { useNavigate } from 'react-router-dom';

interface AssignedVehicle {
  id: string;
  registrationNumber: string;
  make: string;
  model: string;
  fuelLevel: number;
  mileage: number;
  status: 'available' | 'in_use' | 'maintenance';
  lastInspection: string;
}

interface UpcomingTrip {
  id: string;
  destination: string;
  departureTime: string;
  estimatedDuration: string;
  purpose: string;
  status: 'scheduled' | 'in_progress' | 'completed';
}

const DriverDashboard: React.FC = () => {
  const { user } = useAppSelector((state) => state.auth);
  const { showSuccess, showWarning, notifications } = useNotifications();
  const navigate = useNavigate();
  const [currentTime, setCurrentTime] = useState(new Date());
  
  // Mock data - replace with actual API calls
  const [assignedVehicle] = useState<AssignedVehicle>({
    id: 'VH001',
    registrationNumber: 'GP 123 ABC',
    make: 'Toyota',
    model: 'Hilux',
    fuelLevel: 75,
    mileage: 45230,
    status: 'available',
    lastInspection: '2025-01-10'
  });

  const [upcomingTrips] = useState<UpcomingTrip[]>([
    {
      id: 'T001',
      destination: 'Johannesburg Hospital',
      departureTime: '2025-01-15T09:00:00',
      estimatedDuration: '2h 30m',
      purpose: 'Medical Supply Delivery',
      status: 'scheduled'
    },
    {
      id: 'T002',
      destination: 'Pretoria Office',
      departureTime: '2025-01-15T14:00:00',
      estimatedDuration: '1h 15m',
      purpose: 'Document Collection',
      status: 'scheduled'
    }
  ]);

  useEffect(() => {
    const timer = setInterval(() => setCurrentTime(new Date()), 60000);
    return () => clearInterval(timer);
  }, []);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'available': return 'text-green-600 bg-green-100';
      case 'in_use': return 'text-blue-600 bg-blue-100';
      case 'maintenance': return 'text-red-600 bg-red-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  const getFuelLevelColor = (level: number) => {
    if (level > 50) return 'text-green-600';
    if (level > 25) return 'text-yellow-600';
    return 'text-red-600';
  };

  const formatTime = (dateString: string) => {
    return new Date(dateString).toLocaleTimeString('en-ZA', {
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  // Show notifications on mount
  useEffect(() => {
    // Check for low fuel
    if (assignedVehicle.fuelLevel < 25) {
      showWarning('Low Fuel Alert', `${assignedVehicle.registrationNumber} fuel level is ${assignedVehicle.fuelLevel}%`);
    }

    // Check for upcoming trips
    const nextTrip = upcomingTrips.find(trip => trip.status === 'scheduled');
    if (nextTrip) {
      const departureTime = new Date(nextTrip.departureTime);
      const now = new Date();
      const timeDiff = departureTime.getTime() - now.getTime();
      const minutesUntil = Math.floor(timeDiff / (1000 * 60));
      
      if (minutesUntil <= 30 && minutesUntil > 0) {
        notifications.bookingApproved(assignedVehicle.registrationNumber, formatTime(nextTrip.departureTime));
      }
    }
  }, []);

  const handleQuickAction = (action: string) => {
    switch (action) {
      case 'pre-trip':
        navigate('/driver/pre-trip-check');
        break;
      case 'report-issue':
        navigate('/driver/report-issue');
        break;
      case 'fuel-log':
        navigate('/driver/fuel-log');
        break;
      case 'book-vehicle':
        navigate('/driver/book-vehicle');
        break;
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 pb-16 sm:pb-20">
      {/* Header */}
      <div className="bg-blue-600 text-white p-3 sm:p-4">
        <div className="flex items-center justify-between">
          <div className="min-w-0 flex-1">
            <h1 className="text-lg sm:text-xl font-bold truncate">Good morning, {user?.firstName || 'Driver'}</h1>
            <p className="text-blue-100 text-xs sm:text-sm">
              {currentTime.toLocaleDateString('en-ZA', {
                weekday: 'long',
                year: 'numeric',
                month: 'long',
                day: 'numeric'
              })}
            </p>
          </div>
          <button className="p-2 rounded-full bg-blue-700 hover:bg-blue-800 flex-shrink-0">
            <Settings className="h-4 w-4 sm:h-5 sm:w-5" />
          </button>
        </div>
      </div>

      {/* Quick Stats */}
      <div className="p-3 sm:p-4 grid grid-cols-2 gap-3 sm:gap-4">
        <div className="bg-white rounded-lg p-3 sm:p-4 shadow-sm">
          <div className="flex items-center space-x-2 mb-2">
            <Calendar className="h-4 w-4 sm:h-5 sm:w-5 text-blue-600 flex-shrink-0" />
            <span className="text-xs sm:text-sm text-gray-600">Today's Trips</span>
          </div>
          <p className="text-xl sm:text-2xl font-bold text-gray-900">{upcomingTrips.length}</p>
        </div>
        <div className="bg-white rounded-lg p-3 sm:p-4 shadow-sm">
          <div className="flex items-center space-x-2 mb-2">
            <Clock className="h-4 w-4 sm:h-5 sm:w-5 text-green-600 flex-shrink-0" />
            <span className="text-xs sm:text-sm text-gray-600">Next Trip</span>
          </div>
          <p className="text-base sm:text-lg font-semibold text-gray-900">
            {upcomingTrips.length > 0 ? formatTime(upcomingTrips[0].departureTime) : 'None'}
          </p>
        </div>
      </div>

      {/* Assigned Vehicle */}
      <div className="mx-3 sm:mx-4 mb-3 sm:mb-4">
        <h2 className="text-base sm:text-lg font-semibold text-gray-900 mb-2 sm:mb-3">Your Vehicle</h2>
        <div className="bg-white rounded-lg p-3 sm:p-4 shadow-sm">
          <div className="flex items-start justify-between mb-3 gap-2">
            <div className="flex items-center space-x-2 sm:space-x-3 min-w-0 flex-1">
              <div className="p-1.5 sm:p-2 bg-blue-100 rounded-lg flex-shrink-0">
                <Car className="h-5 w-5 sm:h-6 sm:w-6 text-blue-600" />
              </div>
              <div className="min-w-0 flex-1">
                <h3 className="font-semibold text-gray-900 text-sm sm:text-base truncate">{assignedVehicle.registrationNumber}</h3>
                <p className="text-xs sm:text-sm text-gray-600 truncate">{assignedVehicle.make} {assignedVehicle.model}</p>
              </div>
            </div>
            <span className={`px-2 py-1 rounded-full text-xs font-medium flex-shrink-0 ${getStatusColor(assignedVehicle.status)}`}>
              {assignedVehicle.status.replace('_', ' ').toUpperCase()}
            </span>
          </div>

          <div className="grid grid-cols-2 gap-3 sm:gap-4">
            <div className="flex items-center space-x-2">
              <Fuel className={`h-3 w-3 sm:h-4 sm:w-4 flex-shrink-0 ${getFuelLevelColor(assignedVehicle.fuelLevel)}`} />
              <span className="text-xs sm:text-sm text-gray-600">Fuel: {assignedVehicle.fuelLevel}%</span>
            </div>
            <div className="flex items-center space-x-2">
              <Navigation className="h-3 w-3 sm:h-4 sm:w-4 text-gray-500 flex-shrink-0" />
              <span className="text-xs sm:text-sm text-gray-600">{assignedVehicle.mileage.toLocaleString()} km</span>
            </div>
          </div>

          <div className="mt-3 sm:mt-4 pt-3 border-t border-gray-100">
            <div className="flex items-center justify-between">
              <span className="text-xs text-gray-500">Last inspection: {assignedVehicle.lastInspection}</span>
              <CheckCircle className="h-3 w-3 sm:h-4 sm:w-4 text-green-500 flex-shrink-0" />
            </div>
          </div>
        </div>
      </div>

      {/* Upcoming Trips */}
      <div className="mx-3 sm:mx-4 mb-3 sm:mb-4">
        <h2 className="text-base sm:text-lg font-semibold text-gray-900 mb-2 sm:mb-3">Today's Schedule</h2>
        <div className="space-y-2 sm:space-y-3">
          {upcomingTrips.map((trip) => (
            <div key={trip.id} className="bg-white rounded-lg p-3 sm:p-4 shadow-sm">
              <div className="flex items-start justify-between mb-2 gap-2">
                <div className="flex-1 min-w-0">
                  <h3 className="font-medium text-gray-900 text-sm sm:text-base truncate">{trip.destination}</h3>
                  <p className="text-xs sm:text-sm text-gray-600 truncate">{trip.purpose}</p>
                </div>
                <span className="text-xs sm:text-sm font-medium text-blue-600 flex-shrink-0">
                  {formatTime(trip.departureTime)}
                </span>
              </div>
              <div className="flex items-center justify-between text-xs sm:text-sm text-gray-500">
                <div className="flex items-center space-x-1">
                  <Clock className="h-3 w-3 flex-shrink-0" />
                  <span>{trip.estimatedDuration}</span>
                </div>
                <div className="flex items-center space-x-1">
                  <MapPin className="h-3 w-3 flex-shrink-0" />
                  <span>Route available</span>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Quick Actions */}
      <div className="mx-3 sm:mx-4 mb-4 sm:mb-6">
        <h2 className="text-base sm:text-lg font-semibold text-gray-900 mb-2 sm:mb-3">Quick Actions</h2>
        <div className="grid grid-cols-2 gap-2 sm:gap-3">
          <button
            onClick={() => handleQuickAction('pre-trip')}
            className="bg-white rounded-lg p-3 sm:p-4 shadow-sm flex flex-col items-center space-y-1 sm:space-y-2 hover:bg-gray-50 transition-colors"
          >
            <CheckCircle className="h-5 w-5 sm:h-6 sm:w-6 text-green-600" />
            <span className="text-xs sm:text-sm font-medium text-gray-900 text-center">Pre-Trip Check</span>
          </button>
          <button
            onClick={() => handleQuickAction('report-issue')}
            className="bg-white rounded-lg p-3 sm:p-4 shadow-sm flex flex-col items-center space-y-1 sm:space-y-2 hover:bg-gray-50 transition-colors"
          >
            <AlertTriangle className="h-5 w-5 sm:h-6 sm:w-6 text-orange-600" />
            <span className="text-xs sm:text-sm font-medium text-gray-900 text-center">Report Issue</span>
          </button>
          <button
            onClick={() => handleQuickAction('fuel-log')}
            className="bg-white rounded-lg p-3 sm:p-4 shadow-sm flex flex-col items-center space-y-1 sm:space-y-2 hover:bg-gray-50 transition-colors"
          >
            <Fuel className="h-5 w-5 sm:h-6 sm:w-6 text-blue-600" />
            <span className="text-xs sm:text-sm font-medium text-gray-900 text-center">Log Fuel</span>
          </button>
          <button
            onClick={() => handleQuickAction('book-vehicle')}
            className="bg-white rounded-lg p-3 sm:p-4 shadow-sm flex flex-col items-center space-y-1 sm:space-y-2 hover:bg-gray-50 transition-colors"
          >
            <Calendar className="h-5 w-5 sm:h-6 sm:w-6 text-purple-600" />
            <span className="text-xs sm:text-sm font-medium text-gray-900 text-center">Book Vehicle</span>
          </button>
        </div>
      </div>

      {/* Emergency Contact */}
      <div className="mx-3 sm:mx-4 mb-3 sm:mb-4">
        <div className="bg-red-50 border border-red-200 rounded-lg p-3 sm:p-4">
          <div className="flex items-center space-x-2 mb-2">
            <Phone className="h-4 w-4 sm:h-5 sm:w-5 text-red-600 flex-shrink-0" />
            <span className="font-medium text-red-800 text-sm sm:text-base">Emergency Contact</span>
          </div>
          <p className="text-xs sm:text-sm text-red-700">Fleet Control: +27 11 123 4567</p>
          <p className="text-xs sm:text-sm text-red-700">Breakdown Assistance: +27 11 765 4321</p>
        </div>
      </div>
    </div>
  );
};

export default DriverDashboard;
