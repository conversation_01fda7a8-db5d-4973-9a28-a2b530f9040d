// Driver-related type definitions for the Fleet Management System

export interface Driver {
  id: string;
  employee_id: string;
  first_name: string;
  last_name: string;
  email: string;
  phone: string;
  license_number: string;
  license_expiry: string;
  license_class: string;
  department: string;
  status: 'active' | 'inactive' | 'suspended' | 'probationary';
  assigned_vehicles: string[];
  primary_vehicle_id?: string;
  total_trips: number;
  total_distance: number;
  safety_score: number;
  
  // License & Qualifications
  license_endorsements?: string[];
  license_restrictions?: string[];
  certifications?: string[];
  
  // Permissions
  vehicle_type_restrictions?: string[];
  geofencing_enabled?: boolean;
  after_hours_driving_enabled?: boolean;
  max_booking_duration_hours?: number;
  
  // System Access
  app_access_enabled?: boolean;
  aarto_enrolled?: boolean;
  
  created_at: string;
  updated_at: string;
}

export interface DriverStats {
  totalDrivers: number;
  activeDrivers: number;
  inactiveDrivers: number;
  probationaryDrivers: number;
  suspendedDrivers: number;
  licenseExpiringThisMonth: number;
  licenseExpiringNextMonth: number;
  averageSafetyScore: number;
}

export interface LicenseClass {
  code: string;
  name: string;
  description: string;
  vehicleTypes: string[];
}

export interface Department {
  id: string;
  name: string;
  code: string;
  manager?: string;
}

export interface VehicleAssignment {
  vehicleId: string;
  registrationNumber: string;
  make: string;
  model: string;
  isPrimary: boolean;
  assignedDate: string;
}

export interface DriverIncident {
  id: string;
  driver_id: string;
  incident_type: 'accident' | 'violation' | 'complaint' | 'maintenance';
  description: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  date: string;
  resolved: boolean;
  resolution_notes?: string;
}

export interface DriverTrip {
  id: string;
  driver_id: string;
  vehicle_id: string;
  vehicle_registration: string;
  start_location: string;
  end_location: string;
  start_time: string;
  end_time?: string;
  distance: number;
  fuel_consumed?: number;
  status: 'in_progress' | 'completed' | 'cancelled';
  purpose: string;
  odometer_start: number;
  odometer_end?: number;
  safety_score?: number;
  created_at: string;
}

// Form validation schemas and types
export interface DriverFormStep1 {
  firstName: string;
  lastName: string;
  employeeId: string;
  department: string;
  email: string;
  phone: string;
  status: 'active' | 'inactive' | 'probationary';
}

export interface DriverFormStep2 {
  licenseNumber: string;
  licenseClass: string;
  licenseExpiry: string;
  licenseEndorsements: string[];
  licenseRestrictions: string[];
  licenseDocument?: File;
  certifications: string[];
  certificationDocuments?: File[];
}

export interface DriverFormStep3 {
  assignedVehicles: string[];
  primaryVehicleId?: string;
  vehicleTypeRestrictions: string[];
  geofencingEnabled: boolean;
  afterHoursDrivingEnabled: boolean;
  maxBookingDurationHours: number;
}

export interface DriverFormStep4 {
  appAccessEnabled: boolean;
  sendWelcomeEmail: boolean;
  aartoEnrolled: boolean;
}

export type DriverFormData = DriverFormStep1 & DriverFormStep2 & DriverFormStep3 & DriverFormStep4;

// API request/response types
export interface DriverRequest {
  employee_id: string;
  first_name: string;
  last_name: string;
  email: string;
  phone: string;
  license_number: string;
  license_expiry: string;
  license_class: string;
  department: string;
  status: 'active' | 'inactive' | 'probationary';
  assigned_vehicles?: string[];
  primary_vehicle_id?: string;
  license_endorsements?: string[];
  license_restrictions?: string[];
  certifications?: string[];
  vehicle_type_restrictions?: string[];
  geofencing_enabled?: boolean;
  after_hours_driving_enabled?: boolean;
  max_booking_duration_hours?: number;
  app_access_enabled?: boolean;
  aarto_enrolled?: boolean;
}

export interface DriverFilters {
  page?: number;
  limit?: number;
  department?: string;
  status?: string;
  search?: string;
  licenseExpiryBefore?: string;
  safetyScoreMin?: number;
}

// Constants
export const DRIVER_STATUSES = [
  { value: 'active', label: 'Active', color: 'bg-green-100 text-green-800' },
  { value: 'inactive', label: 'Inactive', color: 'bg-gray-100 text-gray-800' },
  { value: 'probationary', label: 'Probationary', color: 'bg-yellow-100 text-yellow-800' },
  { value: 'suspended', label: 'Suspended', color: 'bg-red-100 text-red-800' },
] as const;

export const LICENSE_CLASSES = [
  { code: 'A', name: 'Code A', description: 'Motorcycles', vehicleTypes: ['motorcycle'] },
  { code: 'A1', name: 'Code A1', description: 'Light motorcycles', vehicleTypes: ['motorcycle'] },
  { code: 'B', name: 'Code B', description: 'Light motor vehicles', vehicleTypes: ['sedan', 'suv'] },
  { code: 'C', name: 'Code C', description: 'Heavy motor vehicles', vehicleTypes: ['truck'] },
  { code: 'C1', name: 'Code C1', description: 'Medium heavy vehicles', vehicleTypes: ['truck'] },
  { code: 'EB', name: 'Code EB', description: 'Light motor vehicle with trailer', vehicleTypes: ['sedan', 'suv'] },
  { code: 'EC', name: 'Code EC', description: 'Heavy motor vehicle with trailer', vehicleTypes: ['truck'] },
  { code: 'EC1', name: 'Code EC1', description: 'Medium heavy vehicle with trailer', vehicleTypes: ['truck'] },
] as const;

export const VEHICLE_TYPE_RESTRICTIONS = [
  'sedan',
  'suv', 
  'truck',
  'van',
  'motorcycle',
  'bus',
  'trailer',
] as const;
