import React from 'react';
import { useState, useEffect } from 'react';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { ErrorBoundary } from '@/components/ui/ErrorBoundary';
import {
  Sheet,
  SheetContent,
  SheetHeader,
  SheetTitle,
  SheetTrigger,
} from "@/components/ui/sheet";
import MapWrapper from '@/components/maps/MapWrapper';
import { 
  Search, 
  Filter, 
  RefreshCw, 
  MapPin, 
  Car,
  Clock,
  User,
} from 'lucide-react';

interface Vehicle {
  id: string;
  registrationNumber: string;
  lat: number;
  lng: number;
  status: 'moving' | 'idling' | 'stopped';
  speed: number;
  driver: string;
  lastUpdate: string;
  department: string;
}

interface FilterOptions {
  status?: 'moving' | 'idling' | 'stopped';
  department?: string;
  speedAbove?: number;
}

const LiveTrackingMap: React.FC = () => {
  const [vehicles, setVehicles] = useState<Vehicle[]>([]);
  const [selectedVehicle, setSelectedVehicle] = useState<Vehicle | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [isLoading, setIsLoading] = useState(true);
  const [filters, setFilters] = useState<FilterOptions>({});

  // Mock data - replace with actual API call
  useEffect(() => {
    const mockVehicles: Vehicle[] = [
      {
        id: '1',
        registrationNumber: 'GP123ABC',
        lat: -26.2041,
        lng: 28.0473,
        status: 'moving',
        speed: 45,
        driver: 'John Doe',
        lastUpdate: new Date().toISOString(),
        department: 'Transport'
      },
      {
        id: '2',
        registrationNumber: 'GP456DEF',
        lat: -26.1951,
        lng: 28.0567,
        status: 'idling',
        speed: 0,
        driver: 'Jane Smith',
        lastUpdate: new Date().toISOString(),
        department: 'Health'
      },
      {
        id: '3',
        registrationNumber: 'GP789GHI',
        lat: -26.2141,
        lng: 28.0373,
        status: 'stopped',
        speed: 0,
        driver: 'Mike Johnson',
        lastUpdate: new Date().toISOString(),
        department: 'Education'
      }
    ];

    setTimeout(() => {
      setVehicles(mockVehicles);
      setIsLoading(false);
    }, 1000);
  }, []);

  const filteredVehicles = vehicles.filter(vehicle => {
    // Search term filter
    const matchesSearch = 
      vehicle.registrationNumber.toLowerCase().includes(searchTerm.toLowerCase()) ||
      vehicle.driver.toLowerCase().includes(searchTerm.toLowerCase()) ||
      vehicle.department.toLowerCase().includes(searchTerm.toLowerCase());

    // Status filter
    const matchesStatus = !filters.status || vehicle.status === filters.status;

    // Department filter
    const matchesDepartment = !filters.department || vehicle.department === filters.department;

    // Speed filter
    const matchesSpeed = !filters.speedAbove || vehicle.speed > filters.speedAbove;

    return matchesSearch && matchesStatus && matchesDepartment && matchesSpeed;
  });

  const handleVehicleClick = (vehicle: Vehicle) => {
    setSelectedVehicle(vehicle);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'moving': return 'bg-green-500';
      case 'idling': return 'bg-blue-500';
      case 'stopped': return 'bg-red-500';
      default: return 'bg-gray-500';
    }
  };

  const refreshData = () => {
    setIsLoading(true);
    // Simulate API refresh
    setTimeout(() => {
      setIsLoading(false);
    }, 1000);
  };

  return (
    <div className="h-screen flex flex-col">
      {/* Header */}
      <div className="bg-white border-b p-3 sm:p-4">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3">
          <div>
            <h1 className="text-xl sm:text-2xl font-bold text-gray-900">Live Fleet Tracking</h1>
            <p className="text-xs sm:text-sm text-gray-600">
              Real-time vehicle monitoring and location tracking
            </p>
          </div>
          <div className="flex items-center gap-2 sm:gap-3">
            <div className="relative flex-1 sm:flex-none">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <Input
                placeholder="Search vehicles, drivers, departments..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10 w-full sm:w-80 h-10 sm:h-9 text-base sm:text-sm"
              />
            </div>
            <Sheet>
              <SheetTrigger asChild>
                <Button variant="outline" size="sm" className="h-10 sm:h-9 px-3 sm:px-4">
                  <Filter className="h-4 w-4 mr-1 sm:mr-2" />
                  <span className="hidden sm:inline">Filter</span>
                  <span className="sm:hidden">Filter</span>
                </Button>
              </SheetTrigger>
              <SheetContent side="right" className="w-full sm:w-80">
                <SheetHeader>
                  <SheetTitle>Filter Vehicles</SheetTitle>
                </SheetHeader>
                <div className="space-y-4 py-4">
                  <div className="space-y-2">
                    <label className="text-sm font-medium">Status</label>
                    <Select
                      value={filters.status}
                      onValueChange={(value) => 
                        setFilters(prev => ({ ...prev, status: value as any }))
                      }
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="All statuses" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="">All</SelectItem>
                        <SelectItem value="moving">Moving</SelectItem>
                        <SelectItem value="idling">Idling</SelectItem>
                        <SelectItem value="stopped">Stopped</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-2">
                    <label className="text-sm font-medium">Department</label>
                    <Select
                      value={filters.department}
                      onValueChange={(value) =>
                        setFilters(prev => ({ ...prev, department: value }))
                      }
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="All departments" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="">All</SelectItem>
                        <SelectItem value="Transport">Transport</SelectItem>
                        <SelectItem value="Health">Health</SelectItem>
                        <SelectItem value="Education">Education</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-2">
                    <label className="text-sm font-medium">Minimum Speed (km/h)</label>
                    <Input
                      type="number"
                      min="0"
                      value={filters.speedAbove || ''}
                      onChange={(e) =>
                        setFilters(prev => ({ 
                          ...prev, 
                          speedAbove: e.target.value ? Number(e.target.value) : undefined 
                        }))
                      }
                      placeholder="Enter minimum speed"
                    />
                  </div>

                  <div className="pt-4">
                    <Button
                      variant="outline"
                      className="w-full"
                      onClick={() => setFilters({})}
                    >
                      Reset Filters
                    </Button>
                  </div>
                </div>
              </SheetContent>
            </Sheet>
            <Button variant="outline" size="sm" onClick={refreshData} disabled={isLoading}>
              <RefreshCw className={`h-4 w-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} />
              Refresh
            </Button>
          </div>
        </div>
      </div>

      <div className="flex-1 flex">
        {/* Vehicle List Sidebar */}
        <div className="w-80 bg-white border-r overflow-y-auto">
          <div className="p-4 border-b">
            <h3 className="font-semibold text-gray-900">
              Active Vehicles ({filteredVehicles.length})
            </h3>
          </div>
          
          <div className="space-y-2 p-4">
            {filteredVehicles.map((vehicle) => (
              <Card 
                key={vehicle.id}
                className={`cursor-pointer transition-colors hover:bg-gray-50 ${
                  selectedVehicle?.id === vehicle.id ? 'ring-2 ring-blue-500' : ''
                }`}
                onClick={() => handleVehicleClick(vehicle)}
              >
                <CardContent className="p-4">
                  <div className="flex items-center justify-between mb-2">
                    <div className="flex items-center gap-2">
                      <Car className="h-4 w-4 text-gray-600" />
                      <span className="font-medium">{vehicle.registrationNumber}</span>
                    </div>
                    <Badge className={`${getStatusColor(vehicle.status)} text-white`}>
                      {vehicle.status}
                    </Badge>
                  </div>
                  
                  <div className="space-y-1 text-sm text-gray-600">
                    <div className="flex items-center gap-2">
                      <User className="h-3 w-3" />
                      <span>{vehicle.driver}</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <MapPin className="h-3 w-3" />
                      <span>{vehicle.department}</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <Clock className="h-3 w-3" />
                      <span>Speed: {vehicle.speed} km/h</span>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>

        {/* Map Area */}
        <div className="flex-1 relative">
          <ErrorBoundary
            fallback={
              <div className="h-full w-full flex items-center justify-center bg-gray-50">
                <div className="text-center p-4">
                  <MapPin className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-semibold text-gray-900 mb-2">Map Loading Error</h3>
                  <p className="text-gray-600 mb-4">There was a problem loading the map. Please try refreshing the page.</p>
                  <Button onClick={() => window.location.reload()} variant="outline">
                    <RefreshCw className="h-4 w-4 mr-2" />
                    Refresh Page
                  </Button>
                </div>
              </div>
            }
          >
            <MapWrapper
              center={{ lat: -26.2041, lng: 28.0473 }}
              zoom={12}
              vehicles={filteredVehicles}
              onVehicleClick={handleVehicleClick}
            />
          </ErrorBoundary>
          
          {/* Vehicle Details Panel */}
          {selectedVehicle && (
            <div className="absolute top-4 right-4 w-80">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Car className="h-5 w-5" />
                    {selectedVehicle.registrationNumber}
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-600">Status:</span>
                    <Badge className={`${getStatusColor(selectedVehicle.status)} text-white`}>
                      {selectedVehicle.status}
                    </Badge>
                  </div>
                  
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-600">Driver:</span>
                    <span className="text-sm font-medium">{selectedVehicle.driver}</span>
                  </div>
                  
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-600">Department:</span>
                    <span className="text-sm font-medium">{selectedVehicle.department}</span>
                  </div>
                  
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-600">Speed:</span>
                    <span className="text-sm font-medium">{selectedVehicle.speed} km/h</span>
                  </div>
                  
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-600">Location:</span>
                    <span className="text-sm font-medium">
                      {selectedVehicle.lat.toFixed(4)}, {selectedVehicle.lng.toFixed(4)}
                    </span>
                  </div>
                  
                  <div className="pt-3 border-t">
                    <Button className="w-full" size="sm">
                      View Vehicle Details
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default LiveTrackingMap;