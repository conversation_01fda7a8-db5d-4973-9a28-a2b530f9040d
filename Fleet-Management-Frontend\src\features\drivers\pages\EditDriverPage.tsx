import React from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { ArrowLeft } from 'lucide-react';

const EditDriverPage: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();

  return (
    <div className="container mx-auto px-3 sm:px-4 py-4 sm:py-6">
      {/* Header */}
      <div className="mb-4 sm:mb-6">
        <Button
          variant="ghost"
          onClick={() => navigate('/drivers')}
          className="mb-3 sm:mb-4"
        >
          <ArrowLeft className="h-4 w-4 mr-2" />
          <span className="hidden sm:inline">Back to Drivers</span>
          <span className="sm:hidden">Back</span>
        </Button>

        <div>
          <h1 className="text-xl sm:text-2xl font-bold text-gray-900">Edit Driver</h1>
          <p className="text-sm sm:text-base text-gray-600">Update driver information</p>
        </div>
      </div>

      {/* Placeholder Content */}
      <Card className="w-full">
        <CardHeader className="p-4 sm:p-6">
          <CardTitle className="text-lg sm:text-xl">Edit Driver Information</CardTitle>
        </CardHeader>
        <CardContent className="p-4 sm:p-6">
          <div className="text-center py-12">
            <p className="text-gray-600 mb-4">Driver edit form will be implemented here.</p>
            <p className="text-sm text-gray-500">Driver ID: {id}</p>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default EditDriverPage;
