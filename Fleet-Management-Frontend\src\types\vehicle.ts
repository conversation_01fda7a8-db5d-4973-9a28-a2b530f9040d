export interface Vehicle {
  id: string;
  registrationNumber: string;
  make: string;
  model: string;
  year: number;
  vin: string;
  engineNumber: string;
  fuelType: Vehicle['fuelType'];
  category: Vehicle['category'];
  department: string;
  status: 'active' | 'maintenance' | 'retired' | 'accident';
  mileage: number;
  lastServiceDate?: string;
  nextServiceDate?: string;
  insuranceExpiry: string;
  licenseExpiry: string;
  purchaseDate: string;
  purchasePrice: number;
  currentValue?: number;
  assignedDriver?: string;
  location: string;
  createdAt: string;
  updatedAt: string;
}

export interface VehicleFormData {
  // Basic Information
  registrationNumber: string;
  make: string;
  model: string;
  year: number;
  vin: string;
  engineNumber: string;
  fuelType: 'petrol' | 'diesel' | 'electric' | 'hybrid';
  category: 'sedan' | 'suv' | 'truck' | 'van' | 'motorcycle' | 'bus';
  
  // Status and Registration
  status: 'active' | 'maintenance' | 'retired' | 'accident';
  dateOfRegistration: string;
  color: string;
  
  // Operational Details
  odometerReading: number;
  department: string;
  assignedDriver?: string;
  location: string;
  
  // Financial Information
  purchaseDate: string;
  purchasePrice: number;
  insuranceExpiry: string;
  licenseExpiry: string;
  
  // Service History
  serviceHistory?: Array<{
    date: string;
    type: string;
    mileage: number;
    description: string;
    cost: number;
    serviceProvider: string;
  }>;
  
  // Parts and Serial Numbers
  parts?: Array<{
    partName: string;
    serialNumber: string;
    manufacturer: string;
    installationDate: string;
    partType: 'battery' | 'engine' | 'transmission' | 'brakes' | 'tires' | 'electronics' | 'other';
  }>;
  
  // Part Warranties
  warranties?: Array<{
    partName: string;
    warrantyProvider: string;
    startDate: string;
    endDate: string;
    warrantyType: 'manufacturer' | 'extended' | 'service_provider';
    coverageDetails: string;
  }>;
  
  // Maintenance Plans
  maintenancePlan: {
    planType: 'basic' | 'comprehensive' | 'premium' | 'custom';
    serviceInterval: number;
    intervalType: 'kilometers' | 'months';
    nextServiceDate: string;
    nextServiceMileage: number;
    serviceProvider: string;
    estimatedCost: number;
  };
  
  // Maintenance Schedule
  maintenanceSchedule?: Array<{
    taskName: string;
    frequency: number;
    frequencyType: 'kilometers' | 'months';
    lastCompleted?: string;
    nextDue: string;
    priority: 'low' | 'medium' | 'high' | 'critical';
    estimatedDuration: number;
    estimatedCost: number;
  }>;
}

export interface VehicleFilters {
  status?: Vehicle['status'];
  department?: string;
  fuelType?: Vehicle['fuelType'];
  category?: Vehicle['category'];
  search?: string;
  page?: number;
  limit?: number;
}

// API Request/Response types for backend communication
export interface VehicleRequest {
  registration_number: string;
  make: string;
  model: string;
  year: number;
  department: string;
  vin?: string;
  engine_number?: string;
  fuel_type?: Vehicle['fuelType'];
  vehicle_type?: Vehicle['category'];
  purchase_date?: string;
  purchase_price?: number;
  current_mileage?: number;
  insurance_expiry?: string;
  license_expiry?: string;
  assigned_driver_id?: string;
  location?: string;
}

export interface VehicleResponse {
  id: string;
  registration_number: string;
  make: string;
  model: string;
  year: number;
  status: Vehicle['status'];
  department: string;
  vin?: string;
  engine_number?: string;
  fuel_type?: Vehicle['fuelType'];
  vehicle_type?: Vehicle['category'];
  current_mileage?: number;
  last_service_date?: string;
  next_service_date?: string;
  insurance_expiry?: string;
  license_expiry?: string;
  purchase_date?: string;
  purchase_price?: number;
  current_value?: number;
  assigned_driver_id?: string;
  location?: string;
  created_at: string;
  updated_at: string;
}


