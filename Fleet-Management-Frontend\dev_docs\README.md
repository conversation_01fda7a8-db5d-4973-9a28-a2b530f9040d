# RT46-2026 Fleet Management System - Developer Guide

## Overview

Welcome to the RT46-2026 Vehicle Fleet Management System development team! This guide will help you understand the project structure, development resources, and especially how to leverage our comprehensive Cursor Rules for AI-assisted development.

## 📁 Project Structure

```
RT46-2026-Fleet-Management/
├── .cursor/
│   └── rules/                    # AI Development Rules (10 comprehensive files)
├── business_docs/
│   ├── PRD.md                   # Product Requirements Document
│   └── *.pdf                   # Contract and guidance documents
├── dev_docs/
│   ├── README.md               # This developer guide
│   └── *.csv                   # Technical specifications
|   └── design_specification.md                    # Technical Design Specification
|   └── rules.md                    # Development Rules and Best Practices
|   └── mvp_plan.md                    # 15-day sprint plan for rapid prototyping
|   └── plan.md                    # 12-month implementation roadmap
|   └── Technical_specifications.md                    # Detailed Technical specifications


```

# RT46-2026 Fleet Management System Architecture

An interactive version of this diagram is available on [Mermaid Chart](https://www.mermaidchart.com/app/projects/6241f6e2-8771-4cb3-b5f0-e24fc5adff68/diagrams/d11cb81a-f601-4a26-b252-0496abc1a138/version/v0.1/edit).

```mermaid
flowchart TB
 subgraph subGraph0["Presentation Layer"]
    direction LR
        WebPortal["Web Portal <br>(React.js)"]
        MobileApp["Mobile App <br>(React Native)"]
        AdminUI["Admin UI <br>(Directus on GKE)"]
  end
 subgraph subGraph1["API Gateway & Security"]
    direction TB
        Apigee["Apigee <br>(API Gateway)"]
        Authelia["Authelia <br>(OIDC / RBAC / MFA)"]
  end
 subgraph subGraph2["Core Microservices <br>(Python · FastAPI · Cloud Run)"]
    direction TB
        VehicleService["Vehicle & Merchant Registry"]
        WorkOrderService["Work Order Service"]
        MROService["Maintenance &amp; Repair Orders <br>(Tryton on GKE)"]
        DocumentService["Document Mgmt Service"]
        AllocationEngine["Work Allocation Engine <br>(Cloud Workflows / Airflow)"]
        WorkflowEngine["Workflow Orchestration <br>(Conductor)"]
        PaymentService["Payment Service"]
        AuditService["Audit Logging Service"]
  end
 subgraph subGraph3["AI, Analytics & Data Intelligence"]
    direction TB
        FraudAI["Fraud &amp; Anomaly Detection <br>(Vertex AI · BigQuery ML · Wazuh · pyod)"]
        SearchAI["Search &amp; Geo-Matching <br>(Vertex AI Matching Engine · Meilisearch · PostGIS)"]
        DocAI["Document &amp; Image AI <br>(Document AI · Vision AI · Tesseract)"]
        Analytics["Analytics &amp; Reporting <br>(Looker · BigQuery BI Engine)"]
        AuditPipeline["Audit Pipeline <br>(Cloud Logging → Pub/Sub → BigQuery)"]
  end
 subgraph subGraph4["Messaging &amp; Eventing"]
    direction TB
        RabbitMQ["RabbitMQ"]
        PubSub["Google Pub/Sub"]
  end
 subgraph subGraph5["Data Layer"]
    direction TB
        CloudSQL[("Cloud SQL <br>(PostgreSQL + PostGIS)")]
        FirestoreDB[("Firestore")]
        BigQueryDB[("BigQuery")]
        RedisCache[("Redis / Memorystore")]
        CloudStorage[("Cloud Storage")]
  end
 subgraph subGraph6["External Systems"]
    direction TB
        RTMC["RTMC APIs"]
        SARS["SARS APIs"]
        Banking["Banking Systems"]
        Telematics["Telematics Providers"]
        FuelNetwork["Fuel Network"]
        Insurance["Insurance Providers"]
  end
 subgraph subGraph7["Observability & Monitoring"]
    direction TB
        CloudMonitoring["Cloud Monitoring"]
        Prometheus["Prometheus"]
        Grafana["Grafana"]
        CloudLogging["Cloud Logging"]
  end
 subgraph subGraph8["CI/CD & Infrastructure"]
    direction TB
        GitHub["GitHub Actions"]
        CloudDeploy["Cloud Deploy"]
        Terraform["Terraform"]
        GKE["Google Kubernetes Engine"]
        CloudRun["Cloud Run"]
  end
    WebPortal --> Apigee
    MobileApp --> Apigee
    AdminUI --> Apigee
    Apigee --> Authelia
    Authelia --> VehicleService & WorkOrderService & DocumentService & PaymentService
    WorkOrderService --> AllocationEngine & CloudSQL & RabbitMQ & RedisCache & FuelNetwork
    AllocationEngine --> WorkflowEngine
    VehicleService --> CloudSQL & RabbitMQ & RedisCache & Telematics & RTMC & SARS
    MROService --> CloudSQL
    DocumentService --> CloudStorage & FirestoreDB
    PaymentService --> Banking & Banking
    WorkflowEngine --> RabbitMQ
    RabbitMQ --> FraudAI & Analytics
    PubSub --> AuditPipeline
    FraudAI --> BigQueryDB & CloudStorage
    SearchAI --> CloudSQL & BigQueryDB
    DocAI --> CloudStorage
    Analytics --> BigQueryDB
    AuditService --> AuditPipeline
    AuditPipeline --> BigQueryDB
    Insurance --> PaymentService
    CloudMonitoring -.-> VehicleService & WorkOrderService
    Prometheus -.-> GKE
    Grafana -.-> Prometheus
    CloudLogging -.-> VehicleService & WorkOrderService
    GitHub --> CloudDeploy & CloudDeploy
    CloudDeploy --> GKE & CloudRun
    Terraform --> GKE & CloudRun

``` 

---

## 🤖 Cursor Rules for AI-Assisted Development

We've created a comprehensive set of **Cursor Rules** that provide AI coding assistants (like Cursor, GitHub Copilot, or other agentic tools) with detailed context about our project. These rules ensure consistent, high-quality code generation that follows our architecture and standards.

### 📋 Complete Rules Overview

#### 🌐 **Always Applied Rules** (Every AI request)
1. **`project-context.mdc`** - Core project info, technology stack, and constraints
2. **`database-constraints.mdc`** - Critical database safety rules (NEVER reset databases!)
3. **`development-methodology.mdc`** - Development approach and tool usage guidelines  
4. **`security-compliance.mdc`** - POPIA compliance and South African government security

#### 🎯 **File-Specific Rules** (Applied to specific file types)
5. **`python-practices.mdc`** - Python development with Zen principles and modular design
   - **Applies to**: `*.py`, `requirements*.txt`, `Dockerfile`, `docker-compose*.yml`
   
6. **`database-development.mdc`** - PostgreSQL and SQLAlchemy Core best practices
   - **Applies to**: `*.py`, `*.sql`, `migrations/*.py`, `alembic/*.py`, `database/*.py`
   
7. **`frontend-react.mdc`** - React.js web application development standards
   - **Applies to**: `*.tsx`, `*.ts`, `*.jsx`, `*.js`, `*.css`, `*.scss`, `package.json`, `tsconfig.json`, `vite.config.ts`
   
8. **`frontend-mobile.mdc`** - React Native mobile development guidelines
   - **Applies to**: `*.tsx`, `*.ts`, `*.jsx`, `*.js`, `android/**/*`, `ios/**/*`, `metro.config.js`, `babel.config.js`
   
9. **`documentation-patterns.mdc`** - Documentation and planning standards
   - **Applies to**: `*.md`, `*.mdc`

#### 🔗 **Manual Application Rules** (Use by referencing)
10. **`api-integration.mdc`** - API design and external system integration patterns

---

## 🚀 How to Use Cursor Rules with AI Coding

### For Cursor IDE Users

The rules are automatically applied based on file patterns. When you're working on:

- **Python files**: Gets `python-practices.mdc` + `database-development.mdc` + always-applied rules
- **React files**: Gets `frontend-react.mdc` + always-applied rules  
- **React Native files**: Gets `frontend-mobile.mdc` + always-applied rules
- **Documentation**: Gets `documentation-patterns.mdc` + always-applied rules
- **Any file**: Always gets the 4 core context rules

### For Other AI Tools (GitHub Copilot, Claude, ChatGPT, etc.)

Copy relevant rule content into your AI prompts:

```
I'm working on the RT46-2026 Fleet Management System. Please follow these guidelines:

[Copy content from relevant .cursor/rules/*.mdc files]

Now help me with: [your specific request]
```

### Example AI Prompts

#### For Python Development:
```
I'm working on RT46-2026 Fleet Management (South African government project). 
Key constraints:
- Backend: Python 3.11+ with FastAPI  
- Database: SQLAlchemy Core (NOT Prisma) for fine-grained SQL control
- NEVER reset any database - this is critical government data
- Follow Python Zen principles and modular design patterns
- POPIA compliance required

Help me create a vehicle registration service...
```

#### For React Development:
```
I'm building the RT46-2026 Fleet Management web app.
Requirements:
- React 18+ with TypeScript and Material-UI
- Redux Toolkit for state management
- 80%+ test coverage required
- Government accessibility compliance (WCAG 2.1 AA)
- Mobile-responsive design

Help me create a vehicle dashboard component...
```

---

## 📚 Key Documentation Files

### 🏢 Business Documentation
- **`business_docs/PRD.md`** - Complete Product Requirements Document
  - User personas, functional requirements, success criteria
  - Reference this for business logic and user stories

### 🏗️ Technical Documentation  
- **`design_specification.md`** - Technical Design Specification
  - System architecture, security requirements, performance targets
  - Written in natural language (no code snippets) - perfect for AI context

- **`rules.md`** - Development Rules and Best Practices
  - Coding standards, security guidelines, performance requirements
  - Complements the Cursor Rules with additional detail

### 📋 Planning Documents
- **`mvp_plan.md`** - 15-day MVP sprint plan (if exists)
- **`plan.md`** - 12-month implementation roadmap (if exists)

---

## 🛡️ Critical Constraints for AI Coding

### ❌ **ABSOLUTE PROHIBITIONS**
- **NEVER reset any database** - Government data protection
- **NEVER use Prisma ORM** - Must use SQLAlchemy Core
- **NEVER install dependencies globally** - Always use virtual environments
- **NEVER expose sensitive data** - POPIA compliance required

### ✅ **Required Technology Stack**
- **Backend**: Python 3.11+ + FastAPI + SQLAlchemy Core + Pydantic
- **Frontend**: React.js 18+ + TypeScript + Material-UI + Redux Toolkit
- **Mobile**: React Native 0.72+ + TypeScript
- **Database**: PostgreSQL 15 + Firestore + Redis
- **Cloud**: Google Cloud Platform (GCP) + Kubernetes

### 🔒 **Security Requirements**
- POPIA (Protection of Personal Information Act) compliance
- Multi-factor authentication for admin accounts
- End-to-end encryption for sensitive data
- Comprehensive audit trails
- Role-based access control

---

## 🎯 Development Workflow with AI

### 1. **Start with Context**
Always provide AI tools with:
- Relevant Cursor Rule content
- Business requirements from PRD.md
- Technical constraints from design_specification.md/technical_specifications.md

### 2. **Specify Your Domain**
Tell the AI which part of the system you're working on:
- Vehicle Management
- Work Order Processing  
- Vendor Management
- User Authentication
- Dashboard/Analytics

### 3. **Reference Architecture Patterns**
Point AI to specific architectural patterns:
- Microservices with domain-driven design
- Repository pattern for data access
- Service layer for business logic
- Controller layer for API endpoints

### 4. **Validate Against Rules**
After AI generates code, verify it follows:
- Technology stack requirements
- Security and compliance rules
- Performance optimization guidelines
- Testing and documentation standards

---

## 📖 Quick Reference for AI Prompts

### Python/Backend Development
```
Context: RT46-2026 Fleet Management System
Stack: Python 3.11+ + FastAPI + SQLAlchemy Core + PostgreSQL
Rules: [Reference python-practices.mdc and database-development.mdc]
Domain: [Vehicle/WorkOrder/Vendor/User management]
Request: [Your specific need]
```

### React/Frontend Development  
```
Context: RT46-2026 Fleet Management Web App
Stack: React 18+ + TypeScript + Material-UI + Redux Toolkit
Rules: [Reference frontend-react.mdc]
Feature: [Dashboard/Forms/Tables/Charts]
Request: [Your specific need]
```

### API/Integration Development
```
Context: RT46-2026 Fleet Management System APIs
Integration: [RTMC/SARS/Banking/Telematics/Fuel networks]
Rules: [Reference api-integration.mdc]
Pattern: [REST/GraphQL/Webhook/Message Queue]
Request: [Your specific need]
```

---

## 🔄 Continuous Learning

### Rule Updates
- Cursor Rules are living documents that evolve with the project
- When you discover better patterns, contribute back to the rules
- Regular rule reviews ensure they stay current and useful

### AI Tool Evolution
- New AI coding tools emerge regularly
- These rules provide a portable knowledge base
- Adapt rule content for new tools as they become available

### Team Knowledge Sharing
- Share successful AI prompting patterns with the team
- Document common AI-generated solutions
- Build a library of effective AI interactions

---

## Troubleshooting AI Development

### Common Issues

#### **AI Suggests Wrong Technology**
- **Problem**: AI suggests Node.js instead of Python
- **Solution**: Emphasize in prompt: "Backend MUST be Python 3.11+ with FastAPI"

#### **AI Violates Database Rules**
- **Problem**: AI suggests database reset or Prisma usage
- **Solution**: Start prompt with: "NEVER reset database, use SQLAlchemy Core only"

#### **AI Ignores Security Requirements**
- **Problem**: AI generates code without proper validation
- **Solution**: Include: "POPIA compliance required, implement proper validation"

#### **AI Creates Over-Complex Solutions**
- **Problem**: AI generates unnecessarily complex code
- **Solution**: Reference Python Zen: "Simple is better than complex"

### Getting Better Results

1. **Be Specific**: Include exact technology requirements
2. **Provide Context**: Reference relevant documentation sections
3. **Set Constraints**: Clearly state what NOT to do
4. **Request Explanations**: Ask AI to explain its architectural decisions
5. **Iterate**: Refine prompts based on results

---

## 📞 Support and Resources

### Documentation Questions
- Refer to `design_specification.md` for architecture decisions
- Check `business_docs/PRD.md` for business requirements
- Review `rules.md` for detailed development standards

### Rule Questions  
- Cursor Rules are in `.cursor/rules/` directory
- Each rule file has specific focus area and application scope
- Rules complement each other - use multiple for complex scenarios

### Project Questions
- Tech Lead: Antony Ngigge
- Product Owner: Antonio London
- Project Manager: Jeremy Burns
- DevOps Team: Collins Simiyu

---

**Happy Coding! 🚀**

*Remember: The goal is to build a world-class fleet management system for the South African government. Every line of code should reflect the quality and security standards expected of government systems.* 

Authored by  @mrrobotke