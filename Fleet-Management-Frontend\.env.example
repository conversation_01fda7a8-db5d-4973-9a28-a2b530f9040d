# API Configuration
VITE_API_URL=https://api-rt46-fleet.example.gov.za/v1
VITE_API_TIMEOUT=15000

# Fill in these values with your actual production configuration
# DO NOT commit the actual .env.production file

# Feature Flags
VITE_USE_MOCK_DATA=false

# Authentication
VITE_AUTH_DOMAIN=auth.example.gov.za
VITE_AUTH_CLIENT_ID=your-client-id-here
VITE_AUTH_AUDIENCE=your-audience-here

# Maps and Location Services
VITE_GOOGLE_MAPS_API_KEY=your-maps-api-key-here

# Monitoring and Analytics
VITE_SENTRY_DSN=your-sentry-dsn-here
VITE_ANALYTICS_ID=your-analytics-id-here

# Do not commit .env.production - use this file as a template
# Copy this file to .env.production and fill in the actual values