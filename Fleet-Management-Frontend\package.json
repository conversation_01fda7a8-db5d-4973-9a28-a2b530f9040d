{"name": "rt46-fleet-management-frontend", "private": true, "version": "0.0.1", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage"}, "dependencies": {"@emotion/react": "^11.11.1", "@emotion/styled": "^11.11.0", "@googlemaps/react-wrapper": "^1.1.35", "@hookform/resolvers": "^3.3.2", "@mui/icons-material": "^5.14.19", "@mui/material": "^5.14.20", "@radix-ui/react-accordion": "^1.1.2", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@reduxjs/toolkit": "^2.0.1", "@types/google.maps": "^3.54.10", "axios": "^1.6.2", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "lucide-react": "^0.294.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-hook-form": "^7.48.2", "react-hot-toast": "^2.5.2", "react-redux": "^9.0.4", "react-router-dom": "^6.20.1", "tailwind-merge": "^2.6.0", "zod": "^3.22.4"}, "devDependencies": {"@types/node": "^24.0.14", "@types/react": "^18.2.43", "@types/react-dom": "^18.2.17", "@typescript-eslint/eslint-plugin": "^6.14.0", "@typescript-eslint/parser": "^6.14.0", "@vitejs/plugin-react": "^4.2.1", "autoprefixer": "^10.4.21", "eslint": "^8.55.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.5", "postcss": "^8.5.6", "tailwindcss": "^3.4.17", "typescript": "^5.2.2", "vite": "^5.0.8"}}