import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { ArrowLeft, CheckCircle, XCircle, AlertTriangle, Camera } from 'lucide-react';
import { useNotifications } from '@/hooks/useNotifications';
import { useAppDispatch } from '@/hooks/redux';
import { openModal } from '@/store/slices/uiSlice';

interface ChecklistItem {
  id: string;
  category: string;
  item: string;
  status: 'pending' | 'pass' | 'fail' | 'na';
  required: boolean;
}

const PreTripCheckPage: React.FC = () => {
  const navigate = useNavigate();
  const dispatch = useAppDispatch();
  const { showSuccess, showError, showWarning } = useNotifications();
  const [currentStep, setCurrentStep] = useState(0);

  const checklistCategories = [
    {
      name: 'Exterior',
      items: [
        { id: 'ext_1', item: 'Vehicle body condition (dents, scratches)', required: true },
        { id: 'ext_2', item: 'All lights working (headlights, taillights, indicators)', required: true },
        { id: 'ext_3', item: 'Tire condition and pressure', required: true },
        { id: 'ext_4', item: 'License plate visible and secure', required: true },
        { id: 'ext_5', item: 'Mirrors clean and properly adjusted', required: true }
      ]
    },
    {
      name: 'Interior',
      items: [
        { id: 'int_1', item: 'Seat belts functioning', required: true },
        { id: 'int_2', item: 'Dashboard warning lights', required: true },
        { id: 'int_3', item: 'Steering wheel and controls', required: true },
        { id: 'int_4', item: 'Air conditioning/heating', required: false },
        { id: 'int_5', item: 'Radio/communication equipment', required: false }
      ]
    },
    {
      name: 'Engine & Fluids',
      items: [
        { id: 'eng_1', item: 'Engine oil level', required: true },
        { id: 'eng_2', item: 'Coolant level', required: true },
        { id: 'eng_3', item: 'Brake fluid level', required: true },
        { id: 'eng_4', item: 'Windshield washer fluid', required: false },
        { id: 'eng_5', item: 'Fuel level adequate for trip', required: true }
      ]
    },
    {
      name: 'Safety Equipment',
      items: [
        { id: 'saf_1', item: 'First aid kit present', required: true },
        { id: 'saf_2', item: 'Fire extinguisher present', required: true },
        { id: 'saf_3', item: 'Warning triangles present', required: true },
        { id: 'saf_4', item: 'Spare tire and tools', required: true },
        { id: 'saf_5', item: 'Emergency contact numbers available', required: true }
      ]
    }
  ];

  const [checklist, setChecklist] = useState<ChecklistItem[]>(
    checklistCategories.flatMap(category =>
      category.items.map(item => ({
        ...item,
        category: category.name,
        status: 'pending' as const
      }))
    )
  );

  const updateItemStatus = (id: string, status: 'pass' | 'fail' | 'na') => {
    setChecklist(prev =>
      prev.map(item =>
        item.id === id ? { ...item, status } : item
      )
    );

    // Show immediate feedback for failed items
    if (status === 'fail') {
      const item = checklist.find(i => i.id === id);
      if (item?.required) {
        showWarning('Item Failed', `${item.item} requires attention before trip`);
      }
    }
  };

  const getCurrentCategoryItems = () => {
    const category = checklistCategories[currentStep];
    return checklist.filter(item => item.category === category.name);
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'pass': return <CheckCircle className="h-6 w-6 text-green-600" />;
      case 'fail': return <XCircle className="h-6 w-6 text-red-600" />;
      case 'na': return <AlertTriangle className="h-6 w-6 text-gray-400" />;
      default: return <div className="h-6 w-6 border-2 border-gray-300 rounded-full" />;
    }
  };

  const canProceed = () => {
    const currentItems = getCurrentCategoryItems();
    return currentItems.every(item => item.status !== 'pending');
  };

  const handleNext = () => {
    if (currentStep < checklistCategories.length - 1) {
      setCurrentStep(currentStep + 1);
    } else {
      handleSubmit();
    }
  };

  const handleSubmit = () => {
    const failedItems = checklist.filter(item => item.status === 'fail' && item.required);
    const totalRequired = checklist.filter(item => item.required).length;
    
    if (failedItems.length > 0) {
      dispatch(openModal({
        id: 'failed-inspection',
        type: 'confirm',
        props: {
          title: 'Inspection Failed',
          message: `${failedItems.length} required items failed inspection. You cannot proceed with the trip. Contact maintenance immediately.`,
          confirmText: 'Contact Maintenance',
          cancelText: 'Review Items',
          onConfirm: () => {
            showError('Trip Blocked', 'Vehicle inspection failed. Maintenance has been notified.');
            navigate('/driver/dashboard');
          }
        }
      }));
      return;
    }

    // Success case
    dispatch(openModal({
      id: 'inspection-complete',
      type: 'confirm',
      props: {
        title: 'Inspection Complete',
        message: `All ${totalRequired} required items passed inspection. Vehicle is ready for trip.`,
        confirmText: 'Start Trip',
        cancelText: 'Return to Dashboard',
        onConfirm: () => {
          showSuccess('Inspection Passed', 'Vehicle is cleared for operation. Have a safe trip!');
          navigate('/driver/dashboard');
        }
      }
    }));
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-blue-600 text-white p-3 sm:p-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2 sm:space-x-3 min-w-0 flex-1">
            <button onClick={() => navigate(-1)} className="p-1 flex-shrink-0">
              <ArrowLeft className="h-5 w-5 sm:h-6 sm:w-6" />
            </button>
            <h1 className="text-lg sm:text-xl font-bold truncate">Pre-Trip Inspection</h1>
          </div>
          <span className="text-xs sm:text-sm flex-shrink-0">
            {currentStep + 1} of {checklistCategories.length}
          </span>
        </div>
      </div>

      {/* Progress Bar */}
      <div className="bg-white p-4 shadow-sm">
        <div className="flex justify-between mb-2">
          {checklistCategories.map((category, index) => (
            <span
              key={category.name}
              className={`text-xs font-medium ${
                index <= currentStep ? 'text-blue-600' : 'text-gray-400'
              }`}
            >
              {category.name}
            </span>
          ))}
        </div>
        <div className="w-full bg-gray-200 rounded-full h-2">
          <div
            className="bg-blue-600 h-2 rounded-full transition-all duration-300"
            style={{ width: `${((currentStep + 1) / checklistCategories.length) * 100}%` }}
          />
        </div>
      </div>

      {/* Current Category */}
      <div className="p-4">
        <h2 className="text-lg font-semibold text-gray-900 mb-4">
          {checklistCategories[currentStep].name}
        </h2>

        <div className="space-y-4">
          {getCurrentCategoryItems().map((item) => (
            <div key={item.id} className="bg-white rounded-lg p-4 shadow-sm">
              <div className="flex items-start justify-between mb-3">
                <div className="flex-1">
                  <p className="font-medium text-gray-900">{item.item}</p>
                  {item.required && (
                    <span className="text-xs text-red-600 font-medium">Required</span>
                  )}
                </div>
                {getStatusIcon(item.status)}
              </div>

              <div className="flex space-x-2">
                <button
                  onClick={() => updateItemStatus(item.id, 'pass')}
                  className={`flex-1 py-2 px-3 rounded-lg text-sm font-medium ${
                    item.status === 'pass'
                      ? 'bg-green-100 text-green-800 border-2 border-green-300'
                      : 'bg-gray-100 text-gray-700 border border-gray-300'
                  }`}
                >
                  Pass
                </button>
                <button
                  onClick={() => updateItemStatus(item.id, 'fail')}
                  className={`flex-1 py-2 px-3 rounded-lg text-sm font-medium ${
                    item.status === 'fail'
                      ? 'bg-red-100 text-red-800 border-2 border-red-300'
                      : 'bg-gray-100 text-gray-700 border border-gray-300'
                  }`}
                >
                  Fail
                </button>
                <button
                  onClick={() => updateItemStatus(item.id, 'na')}
                  className={`flex-1 py-2 px-3 rounded-lg text-sm font-medium ${
                    item.status === 'na'
                      ? 'bg-gray-200 text-gray-800 border-2 border-gray-400'
                      : 'bg-gray-100 text-gray-700 border border-gray-300'
                  }`}
                >
                  N/A
                </button>
              </div>

              {item.status === 'fail' && (
                <div className="mt-3 p-3 bg-red-50 rounded-lg">
                  <p className="text-sm text-red-800 font-medium mb-2">Issue Details:</p>
                  <textarea
                    placeholder="Describe the issue..."
                    className="w-full p-2 text-sm border border-red-300 rounded focus:ring-2 focus:ring-red-500 focus:border-transparent"
                    rows={2}
                  />
                  <button className="mt-2 flex items-center space-x-1 text-sm text-red-600">
                    <Camera className="h-4 w-4" />
                    <span>Add Photo</span>
                  </button>
                </div>
              )}
            </div>
          ))}
        </div>

        {/* Navigation */}
        <div className="mt-6 flex space-x-3">
          {currentStep > 0 && (
            <button
              onClick={() => setCurrentStep(currentStep - 1)}
              className="flex-1 py-3 px-4 border border-gray-300 rounded-lg text-gray-700 font-medium hover:bg-gray-50"
            >
              Previous
            </button>
          )}
          <button
            onClick={handleNext}
            disabled={!canProceed()}
            className={`flex-1 py-3 px-4 rounded-lg font-medium ${
              canProceed()
                ? 'bg-blue-600 text-white hover:bg-blue-700'
                : 'bg-gray-300 text-gray-500 cursor-not-allowed'
            }`}
          >
            {currentStep === checklistCategories.length - 1 ? 'Complete' : 'Next'}
          </button>
        </div>
      </div>
    </div>
  );
};

export default PreTripCheckPage;

