import React from 'react';
import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  X, 
  Edit, 
  Trash2, 
  Calendar, 
  Clock, 
  MapPin, 
  User, 
  Phone, 
  Car, 
  Building,
  AlertTriangle,
  CheckCircle,
  Play,
  Pause
} from 'lucide-react';

interface ScheduledInspection {
  id: string;
  type: 'vehicle' | 'merchant';
  subject: {
    name: string;
    registration?: string;
    contactPerson?: string;
  };
  date: string;
  time: string;
  duration: number;
  location: string;
  inspector: string;
  priority: 'Low' | 'Medium' | 'High' | 'Critical';
  status: 'Scheduled' | 'Confirmed' | 'In Progress' | 'Completed' | 'Cancelled' | 'Overdue';
  reason: string;
  phone: string;
}

interface InspectionDetailViewProps {
  inspection: ScheduledInspection;
  onClose: () => void;
  onEdit: () => void;
  onDelete: () => void;
  onStatusChange: (status: ScheduledInspection['status']) => void;
}

const InspectionDetailView: React.FC<InspectionDetailViewProps> = ({
  inspection,
  onClose,
  onEdit,
  onDelete,
  onStatusChange
}) => {
  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'Critical': return 'bg-red-100 text-red-800';
      case 'High': return 'bg-orange-100 text-orange-800';
      case 'Medium': return 'bg-yellow-100 text-yellow-800';
      case 'Low': return 'bg-green-100 text-green-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'Completed': return 'bg-green-100 text-green-800';
      case 'In Progress': return 'bg-blue-100 text-blue-800';
      case 'Confirmed': return 'bg-purple-100 text-purple-800';
      case 'Scheduled': return 'bg-gray-100 text-gray-800';
      case 'Cancelled': return 'bg-red-100 text-red-800';
      case 'Overdue': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const canStart = inspection.status === 'Scheduled' || inspection.status === 'Confirmed';
  const canComplete = inspection.status === 'In Progress';
  const canCancel = inspection.status !== 'Completed' && inspection.status !== 'Cancelled';

  return (
    <div className="space-y-6">
      {/* Subject Information */}
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            {inspection.type === 'vehicle' ? (
              <Car className="h-5 w-5 text-blue-600" />
            ) : (
              <Building className="h-5 w-5 text-purple-600" />
            )}
            <h3 className="text-lg font-semibold">{inspection.subject.name}</h3>
          </div>
          <div className="flex space-x-2">
            <Badge className={getPriorityColor(inspection.priority)}>
              {inspection.priority}
            </Badge>
            <Badge className={getStatusColor(inspection.status)}>
              {inspection.status}
            </Badge>
          </div>
        </div>
        
        {inspection.subject.registration && (
          <div className="flex items-center space-x-2">
            <span className="text-sm font-medium text-gray-600">Registration:</span>
            <Badge variant="outline">{inspection.subject.registration}</Badge>
          </div>
        )}
        
        {inspection.subject.contactPerson && (
          <div className="flex items-center space-x-2">
            <span className="text-sm font-medium text-gray-600">Contact Person:</span>
            <span className="text-sm">{inspection.subject.contactPerson}</span>
          </div>
        )}
      </div>

      {/* Schedule Information */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div className="space-y-3">
          <div className="flex items-center space-x-2">
            <Calendar className="h-4 w-4 text-gray-400" />
            <span className="text-sm font-medium">Date:</span>
            <span className="text-sm">{new Date(inspection.date).toLocaleDateString()}</span>
          </div>
          
          <div className="flex items-center space-x-2">
            <Clock className="h-4 w-4 text-gray-400" />
            <span className="text-sm font-medium">Time:</span>
            <span className="text-sm">{inspection.time} ({inspection.duration} min)</span>
          </div>
          
          <div className="flex items-center space-x-2">
            <User className="h-4 w-4 text-gray-400" />
            <span className="text-sm font-medium">Inspector:</span>
            <span className="text-sm">{inspection.inspector}</span>
          </div>
        </div>
        
        <div className="space-y-3">
          <div className="flex items-center space-x-2">
            <MapPin className="h-4 w-4 text-gray-400" />
            <span className="text-sm font-medium">Location:</span>
            <span className="text-sm">{inspection.location}</span>
          </div>
          
          <div className="flex items-center space-x-2">
            <Phone className="h-4 w-4 text-gray-400" />
            <span className="text-sm font-medium">Phone:</span>
            <span className="text-sm">{inspection.phone}</span>
          </div>
        </div>
      </div>

      {/* Reason */}
      <div className="space-y-2">
        <span className="text-sm font-medium text-gray-600">Inspection Reason:</span>
        <div className="p-3 bg-gray-50 rounded-lg text-sm">
          {inspection.reason}
        </div>
      </div>

      {/* Status Actions */}
      <div className="space-y-3">
        <span className="text-sm font-medium text-gray-600">Quick Actions:</span>
        <div className="flex flex-wrap gap-2">
          {canStart && (
            <Button 
              size="sm" 
              onClick={() => onStatusChange('In Progress')}
              className="bg-blue-600 hover:bg-blue-700"
            >
              <Play className="h-4 w-4 mr-2" />
              Start Inspection
            </Button>
          )}
          
          {canComplete && (
            <Button 
              size="sm" 
              onClick={() => onStatusChange('Completed')}
              className="bg-green-600 hover:bg-green-700"
            >
              <CheckCircle className="h-4 w-4 mr-2" />
              Mark Complete
            </Button>
          )}
          
          {inspection.status === 'In Progress' && (
            <Button 
              size="sm" 
              variant="outline"
              onClick={() => onStatusChange('Confirmed')}
            >
              <Pause className="h-4 w-4 mr-2" />
              Pause
            </Button>
          )}
          
          {canCancel && (
            <Button 
              size="sm" 
              variant="outline"
              onClick={() => onStatusChange('Cancelled')}
              className="text-red-600 border-red-200 hover:bg-red-50"
            >
              Cancel
            </Button>
          )}
        </div>
      </div>

      {/* Main Actions */}
      <div className="flex space-x-3 pt-4 border-t">
        <Button onClick={onEdit} className="flex-1">
          <Edit className="h-4 w-4 mr-2" />
          Edit Inspection
        </Button>
        <Button 
          variant="destructive" 
          onClick={onDelete}
          className="flex-1"
        >
          <Trash2 className="h-4 w-4 mr-2" />
          Delete
        </Button>
      </div>
    </div>
  );
};

export default InspectionDetailView;



