import React, { useState } from 'react';
import { useParams } from 'react-router-dom';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  Download, 
  Printer,
  CheckCircle, 
  Car,
  MapPin,
  User,
  Phone,
  Mail,
  Shield,
  AlertTriangle,
  Wrench
} from 'lucide-react';

interface JobCard {
  id: string;
  jobCardNumber: string;
  workOrderId: string;
  status: 'Active' | 'Completed' | 'On Hold';
  vehicle: {
    registration: string;
    make: string;
    model: string;
    year: number;
    vin: string;
  };
  customer: {
    department: string;
    contactPerson: string;
    phone: string;
    email: string;
    address: string;
  };
  serviceDetails: {
    type: string;
    description: string;
    priority: string;
    estimatedHours: number;
    startDate: string;
    dueDate: string;
  };
  approvedWork: Array<{
    id: string;
    description: string;
    laborHours: number;
    laborRate: number;
    parts: Array<{
      name: string;
      quantity: number;
      unitPrice: number;
    }>;
    total: number;
  }>;
  totalApproved: number;
}

const JobCardView: React.FC = () => {
  const { id } = useParams<{ id: string }>();

  // Mock data
  const jobCard: JobCard = {
    id: id || 'JC-2025-001',
    jobCardNumber: 'JC-2025-001',
    workOrderId: 'WO-2025-001',
    status: 'Active',
    vehicle: {
      registration: 'GP123ABC',
      make: 'Toyota',
      model: 'Hilux',
      year: 2020,
      vin: '1HGBH41JXMN109186'
    },
    customer: {
      department: 'Department of Health',
      contactPerson: 'Dr. Sarah Johnson',
      phone: '************',
      email: '<EMAIL>',
      address: '123 Government Ave, Pretoria Central, 0001'
    },
    serviceDetails: {
      type: 'Brake Service',
      description: 'Replace brake pads and check brake fluid levels',
      priority: 'High',
      estimatedHours: 4,
      startDate: '2025-01-15',
      dueDate: '2025-01-16'
    },
    approvedWork: [
      {
        id: '1',
        description: 'Replace front brake pads',
        laborHours: 2,
        laborRate: 450,
        parts: [
          { name: 'Front brake pads set', quantity: 1, unitPrice: 850 }
        ],
        total: 1750
      },
      {
        id: '2',
        description: 'Check and top up brake fluid',
        laborHours: 0.5,
        laborRate: 450,
        parts: [
          { name: 'Brake fluid (1L)', quantity: 1, unitPrice: 120 }
        ],
        total: 345
      }
    ],
    totalApproved: 2095
  };

  const handlePrint = () => {
    window.print();
  };

  const handleDownload = () => {
    console.log('Downloading job card PDF');
  };

  const handleStartWork = () => {
    console.log('Starting work on job card');
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'Active': return 'bg-green-100 text-green-800';
      case 'Completed': return 'bg-blue-100 text-blue-800';
      case 'On Hold': return 'bg-yellow-100 text-yellow-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Job Card: {jobCard.jobCardNumber}</h1>
          <p className="text-gray-600">Official work authorization and service details</p>
        </div>
        <div className="flex space-x-2">
          <Button variant="outline" onClick={handleDownload}>
            <Download className="h-4 w-4 mr-2" />
            Download PDF
          </Button>
          <Button variant="outline" onClick={handlePrint}>
            <Printer className="h-4 w-4 mr-2" />
            Print Job Card
          </Button>
          {jobCard.status === 'Active' && (
            <Button onClick={handleStartWork}>
              <Wrench className="h-4 w-4 mr-2" />
              Start Work
            </Button>
          )}
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Job Card Details */}
        <div className="lg:col-span-2 space-y-6">
          {/* Basic Info */}
          <Card>
            <CardHeader>
              <div className="flex justify-between items-center">
                <CardTitle>Job Card Information</CardTitle>
                <Badge className={getStatusColor(jobCard.status)}>
                  {jobCard.status}
                </Badge>
              </div>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="text-sm font-medium text-gray-600">Job Card Number</label>
                  <p className="text-lg font-semibold">{jobCard.jobCardNumber}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-600">Work Order ID</label>
                  <p className="text-lg font-semibold">{jobCard.workOrderId}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-600">Service Type</label>
                  <p>{jobCard.serviceDetails.type}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-600">Priority</label>
                  <p>{jobCard.serviceDetails.priority}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-600">Start Date</label>
                  <p>{jobCard.serviceDetails.startDate}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-600">Due Date</label>
                  <p>{jobCard.serviceDetails.dueDate}</p>
                </div>
              </div>
              <div>
                <label className="text-sm font-medium text-gray-600">Description</label>
                <p className="mt-1">{jobCard.serviceDetails.description}</p>
              </div>
            </CardContent>
          </Card>

          {/* Vehicle Information */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Car className="h-5 w-5 mr-2" />
                Vehicle Information
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="text-sm font-medium text-gray-600">Registration</label>
                  <p className="text-lg font-semibold">{jobCard.vehicle.registration}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-600">Make & Model</label>
                  <p>{jobCard.vehicle.make} {jobCard.vehicle.model}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-600">Year</label>
                  <p>{jobCard.vehicle.year}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-600">VIN</label>
                  <p className="text-sm font-mono">{jobCard.vehicle.vin}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Approved Work */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <CheckCircle className="h-5 w-5 mr-2" />
                Approved Work
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {jobCard.approvedWork.map((work) => (
                  <div key={work.id} className="border rounded-lg p-4">
                    <h4 className="font-semibold mb-2">{work.description}</h4>
                    <div className="grid grid-cols-2 gap-4 text-sm">
                      <div>
                        <span className="text-gray-600">Labor:</span> {work.laborHours}h @ R{work.laborRate}/h
                      </div>
                      <div>
                        <span className="text-gray-600">Labor Cost:</span> R{(work.laborHours * work.laborRate).toFixed(2)}
                      </div>
                    </div>
                    {work.parts.length > 0 && (
                      <div className="mt-2">
                        <p className="text-sm font-medium text-gray-600 mb-1">Parts:</p>
                        {work.parts.map((part, index) => (
                          <div key={index} className="text-sm text-gray-600">
                            {part.name} - Qty: {part.quantity} @ R{part.unitPrice} = R{(part.quantity * part.unitPrice).toFixed(2)}
                          </div>
                        ))}
                      </div>
                    )}
                    <div className="mt-2 pt-2 border-t">
                      <span className="font-semibold">Total: R{work.total.toFixed(2)}</span>
                    </div>
                  </div>
                ))}
                <div className="border-t pt-4">
                  <div className="flex justify-between items-center text-lg font-bold">
                    <span>Total Approved Amount:</span>
                    <span>R{jobCard.totalApproved.toFixed(2)}</span>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Customer Information */}
        <div className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <User className="h-5 w-5 mr-2" />
                Customer Information
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <label className="text-sm font-medium text-gray-600">Department</label>
                <p className="font-semibold">{jobCard.customer.department}</p>
              </div>
              <div>
                <label className="text-sm font-medium text-gray-600">Contact Person</label>
                <p>{jobCard.customer.contactPerson}</p>
              </div>
              <div>
                <label className="text-sm font-medium text-gray-600">Phone</label>
                <div className="flex items-center">
                  <Phone className="h-4 w-4 mr-2 text-gray-400" />
                  <p>{jobCard.customer.phone}</p>
                </div>
              </div>
              <div>
                <label className="text-sm font-medium text-gray-600">Email</label>
                <div className="flex items-center">
                  <Mail className="h-4 w-4 mr-2 text-gray-400" />
                  <p className="text-sm">{jobCard.customer.email}</p>
                </div>
              </div>
              <div>
                <label className="text-sm font-medium text-gray-600">Address</label>
                <div className="flex items-start">
                  <MapPin className="h-4 w-4 mr-2 text-gray-400 mt-1" />
                  <p className="text-sm">{jobCard.customer.address}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Important Notes */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <AlertTriangle className="h-5 w-5 mr-2 text-yellow-600" />
                Important Notes
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2 text-sm">
                <p>• Work must be completed within approved scope</p>
                <p>• Any additional work requires separate approval</p>
                <p>• Use only approved parts and materials</p>
                <p>• Document all work performed</p>
                <p>• Contact customer for any delays</p>
              </div>
            </CardContent>
          </Card>

          {/* Authorization */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Shield className="h-5 w-5 mr-2 text-green-600" />
                Authorization
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-sm space-y-2">
                <p><strong>Authorized by:</strong> Fleet Manager</p>
                <p><strong>Date:</strong> {jobCard.serviceDetails.startDate}</p>
                <p><strong>Max Amount:</strong> R{jobCard.totalApproved.toFixed(2)}</p>
                <div className="mt-4 p-3 bg-green-50 rounded-lg">
                  <p className="text-green-800 font-medium">✓ Work Authorized</p>
                  <p className="text-green-600 text-xs">Proceed with approved work only</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
};

export default JobCardView;


