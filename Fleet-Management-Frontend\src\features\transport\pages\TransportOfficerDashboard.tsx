import React from 'react';
import { 
  <PERSON>, 
  Calendar, 
  Alert<PERSON>riangle, 
  Clock, 
  Check<PERSON>ircle, 
  <PERSON>ch,
  MapPin,
  Users,
  TrendingUp,
  Plus
} from 'lucide-react';
import { Link } from 'react-router-dom';

interface VehicleAvailability {
  id: string;
  registrationNumber: string;
  type: string;
  status: 'available' | 'in-use' | 'maintenance' | 'booked';
  currentLocation?: string;
  nextService?: string;
  driver?: string;
}

interface UpcomingService {
  id: string;
  vehicleId: string;
  registrationNumber: string;
  serviceType: string;
  scheduledDate: string;
  vendor: string;
  status: 'scheduled' | 'confirmed' | 'in-progress';
}

const TransportOfficerDashboard: React.FC = () => {
  const vehicleAvailability: VehicleAvailability[] = [
    {
      id: '1',
      registrationNumber: 'GP-123-ABC',
      type: 'Sedan',
      status: 'available',
      currentLocation: 'Main Depot',
      nextService: '2024-02-15'
    },
    {
      id: '2',
      registrationNumber: 'GP-456-DEF',
      type: 'SUV',
      status: 'in-use',
      driver: '<PERSON>',
      currentLocation: 'City Hall',
      nextService: '2024-02-20'
    },
    {
      id: '3',
      registrationNumber: 'GP-789-GHI',
      type: 'Truck',
      status: 'maintenance',
      currentLocation: 'AutoFix Pro',
      nextService: '2024-03-01'
    }
  ];

  const upcomingServices: UpcomingService[] = [
    {
      id: '1',
      vehicleId: '1',
      registrationNumber: 'GP-123-ABC',
      serviceType: 'Regular Service',
      scheduledDate: '2024-02-15',
      vendor: 'Fleet Services SA',
      status: 'scheduled'
    },
    {
      id: '2',
      vehicleId: '4',
      registrationNumber: 'GP-321-JKL',
      serviceType: 'Brake Inspection',
      scheduledDate: '2024-02-16',
      vendor: 'AutoFix Pro',
      status: 'confirmed'
    }
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'available': return 'bg-green-100 text-green-800';
      case 'in-use': return 'bg-blue-100 text-blue-800';
      case 'maintenance': return 'bg-red-100 text-red-800';
      case 'booked': return 'bg-yellow-100 text-yellow-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getServiceStatusColor = (status: string) => {
    switch (status) {
      case 'scheduled': return 'bg-yellow-100 text-yellow-800';
      case 'confirmed': return 'bg-blue-100 text-blue-800';
      case 'in-progress': return 'bg-orange-100 text-orange-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const stats = [
    {
      title: 'Available Vehicles',
      value: vehicleAvailability.filter(v => v.status === 'available').length,
      total: vehicleAvailability.length,
      icon: <Car className="h-6 w-6 text-green-600" />,
      color: 'green'
    },
    {
      title: 'Vehicles In Use',
      value: vehicleAvailability.filter(v => v.status === 'in-use').length,
      total: vehicleAvailability.length,
      icon: <Users className="h-6 w-6 text-blue-600" />,
      color: 'blue'
    },
    {
      title: 'In Maintenance',
      value: vehicleAvailability.filter(v => v.status === 'maintenance').length,
      total: vehicleAvailability.length,
      icon: <Wrench className="h-6 w-6 text-red-600" />,
      color: 'red'
    },
    {
      title: 'Upcoming Services',
      value: upcomingServices.length,
      total: null,
      icon: <Calendar className="h-6 w-6 text-purple-600" />,
      color: 'purple'
    }
  ];

  return (
    <div className="container mx-auto px-3 sm:px-4 lg:px-6 py-4 sm:py-6 lg:py-8 space-y-4 sm:space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <h1 className="text-xl sm:text-2xl font-bold text-gray-900">Transport Officer Dashboard</h1>
          <p className="text-sm sm:text-base text-gray-600">Daily vehicle availability and upcoming services</p>
        </div>
        <div className="flex flex-col sm:flex-row gap-2 sm:gap-3">
          <Link
            to="/transport/maintenance/schedule"
            className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg flex items-center justify-center space-x-2 text-sm sm:text-base h-10 sm:h-auto"
          >
            <Plus className="h-4 w-4" />
            <span className="hidden sm:inline">Schedule Service</span>
            <span className="sm:hidden">Schedule</span>
          </Link>
          <Link
            to="/transport/maintenance/request"
            className="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg flex items-center justify-center space-x-2 text-sm sm:text-base h-10 sm:h-auto"
          >
            <Plus className="h-4 w-4" />
            <span className="hidden sm:inline">New Request</span>
            <span className="sm:hidden">Request</span>
          </Link>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-3 sm:gap-4 lg:gap-6">
        {stats.map((stat, index) => (
          <div key={index} className="bg-white rounded-lg shadow-sm border border-gray-200 p-4 sm:p-6">
            <div className="flex items-center justify-between">
              <div className="min-w-0 flex-1">
                <p className="text-xs sm:text-sm font-medium text-gray-600">{stat.title}</p>
                <div className="flex items-baseline space-x-2">
                  <p className="text-lg sm:text-xl lg:text-2xl font-bold text-gray-900">{stat.value}</p>
                  {stat.total && (
                    <p className="text-xs sm:text-sm text-gray-500">/ {stat.total}</p>
                  )}
                </div>
              </div>
              <div className="flex-shrink-0">
                {stat.icon}
              </div>
            </div>
          </div>
        ))}
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Vehicle Availability */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200">
          <div className="p-6 border-b border-gray-200">
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-semibold text-gray-900">Today's Vehicle Availability</h3>
              <Link 
                to="/transport/booking-calendar"
                className="text-blue-600 hover:text-blue-700 text-sm font-medium"
              >
                View Calendar
              </Link>
            </div>
          </div>
          <div className="p-6">
            <div className="space-y-4">
              {vehicleAvailability.map((vehicle) => (
                <div key={vehicle.id} className="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
                  <div className="flex items-center space-x-3">
                    <Car className="h-5 w-5 text-gray-400" />
                    <div>
                      <p className="font-medium text-gray-900">{vehicle.registrationNumber}</p>
                      <p className="text-sm text-gray-500">{vehicle.type}</p>
                      {vehicle.driver && (
                        <p className="text-sm text-blue-600">Driver: {vehicle.driver}</p>
                      )}
                    </div>
                  </div>
                  <div className="text-right">
                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(vehicle.status)}`}>
                      {vehicle.status.replace('-', ' ').toUpperCase()}
                    </span>
                    <p className="text-xs text-gray-500 mt-1">
                      <MapPin className="h-3 w-3 inline mr-1" />
                      {vehicle.currentLocation}
                    </p>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Upcoming Services */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200">
          <div className="p-6 border-b border-gray-200">
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-semibold text-gray-900">Upcoming Services</h3>
              <Link 
                to="/transport/maintenance/schedule"
                className="text-blue-600 hover:text-blue-700 text-sm font-medium"
              >
                Schedule More
              </Link>
            </div>
          </div>
          <div className="p-6">
            <div className="space-y-4">
              {upcomingServices.map((service) => (
                <div key={service.id} className="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
                  <div className="flex items-center space-x-3">
                    <Calendar className="h-5 w-5 text-gray-400" />
                    <div>
                      <p className="font-medium text-gray-900">{service.registrationNumber}</p>
                      <p className="text-sm text-gray-500">{service.serviceType}</p>
                      <p className="text-sm text-blue-600">{service.vendor}</p>
                    </div>
                  </div>
                  <div className="text-right">
                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${getServiceStatusColor(service.status)}`}>
                      {service.status.replace('-', ' ').toUpperCase()}
                    </span>
                    <p className="text-xs text-gray-500 mt-1">
                      <Clock className="h-3 w-3 inline mr-1" />
                      {service.scheduledDate}
                    </p>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* Quick Actions */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Quick Actions</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <Link
            to="/transport/trip-logs"
            className="flex items-center space-x-3 p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"
          >
            <TrendingUp className="h-5 w-5 text-blue-600" />
            <span className="font-medium text-gray-900">View Trip Logs</span>
          </Link>
          <Link
            to="/transport/booking-calendar"
            className="flex items-center space-x-3 p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"
          >
            <Calendar className="h-5 w-5 text-green-600" />
            <span className="font-medium text-gray-900">Booking Calendar</span>
          </Link>
          <Link
            to="/transport/maintenance/schedule"
            className="flex items-center space-x-3 p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"
          >
            <Wrench className="h-5 w-5 text-purple-600" />
            <span className="font-medium text-gray-900">Schedule Maintenance</span>
          </Link>
          <Link
            to="/transport/maintenance/request"
            className="flex items-center space-x-3 p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"
          >
            <AlertTriangle className="h-5 w-5 text-red-600" />
            <span className="font-medium text-gray-900">Report Issue</span>
          </Link>
        </div>
      </div>
    </div>
  );
};

export default TransportOfficerDashboard;