import { fetchBaseQuery, retry } from '@reduxjs/toolkit/query/react';
import type { BaseQueryFn, FetchArgs, FetchBaseQueryError } from '@reduxjs/toolkit/query';
import { addError, setNetworkStatus } from '../slices/errorSlice';
import { logout } from '../slices/authSlice';
import { RootState } from '@/store';

// Add Vite environment types
/// <reference types="vite/client" />

const baseQuery = fetchBaseQuery({
  baseUrl: import.meta.env.VITE_API_URL || 'http://localhost:8000/api/v1',
  credentials: 'include',
  prepareHeaders: (headers, { getState }) => {
    const token = (getState() as RootState).auth.token;
    if (token) {
      headers.set('authorization', `Bearer ${token}`);
    }
    headers.set('Content-Type', 'application/json');
    return headers;
  },
});

const baseQueryWithRetry = retry(baseQuery, {
  maxRetries: 3,
  retryCondition: (error: FetchBaseQueryError, _args: string | FetchArgs) => {
    // Retry on network errors and 5xx server errors
    return (
      error.status === 'FETCH_ERROR' ||
      error.status === 'TIMEOUT_ERROR' ||
      (typeof error.status === 'number' && error.status >= 500)
    );
  },
});

export const baseQueryWithErrorHandling: BaseQueryFn<
  string | FetchArgs,
  unknown,
  FetchBaseQueryError
> = async (args, api, extraOptions) => {
  // Network status monitoring
  const startTime = Date.now();
  
  const result = await baseQueryWithRetry(args, api, extraOptions);
  
  const endTime = Date.now();
  const responseTime = endTime - startTime;
  
  // Update network status based on response time
  if (responseTime > 5000) {
    api.dispatch(setNetworkStatus('slow'));
  } else if (navigator.onLine) {
    api.dispatch(setNetworkStatus('online'));
  }

  if (result.error) {
    const error = result.error;
    
    // Handle authentication errors
    if (error.status === 401) {
      api.dispatch(logout());
      api.dispatch(addError({
        message: 'Session expired. Please log in again.',
        code: 'AUTH_EXPIRED',
        status: 401,
        retryable: false
      }));
      return result;
    }
    
    // Handle different error types
    let errorMessage = 'An unexpected error occurred';
    let retryable = false;
    
    if (error.status === 'FETCH_ERROR') {
      errorMessage = 'Network connection failed. Please check your internet connection.';
      retryable = true;
      api.dispatch(setNetworkStatus('offline'));
    } else if (error.status === 'TIMEOUT_ERROR') {
      errorMessage = 'Request timed out. Please try again.';
      retryable = true;
    } else if (typeof error.status === 'number') {
      if (error.status >= 500) {
        errorMessage = 'Server error. Our team has been notified.';
        retryable = true;
      } else if (error.status === 403) {
        errorMessage = 'You do not have permission to perform this action.';
      } else if (error.status === 404) {
        errorMessage = 'The requested resource was not found.';
      } else if (error.status === 422) {
        // Validation errors - extract from response
        const data = error.data as any;
        if (data?.detail) {
          errorMessage = Array.isArray(data.detail) 
            ? data.detail.map((err: any) => err.msg).join(', ')
            : data.detail;
        } else {
          errorMessage = 'Invalid data provided. Please check your input.';
        }
      }
    }
    
    // Add error to global state
    api.dispatch(addError({
      message: errorMessage,
      code: error.status?.toString(),
      status: typeof error.status === 'number' ? error.status : undefined,
      endpoint: typeof args === 'string' ? args : args.url,
      retryable,
      details: error.data
    }));
  }
  
  return result;
};


