import { api } from '../api';
import type { UserResponse, PaginatedResponse } from '../../types/api';

export interface UserFilters {
  page?: number;
  limit?: number;
  department?: string;
  role?: string;
  is_active?: boolean;
  search?: string;
}

export interface CreateUserRequest {
  email: string;
  first_name: string;
  last_name: string;
  role: string;
  department: string;
  phone?: string;
  password: string;
}

export interface UpdateUserRequest {
  first_name?: string;
  last_name?: string;
  role?: string;
  department?: string;
  phone?: string;
  is_active?: boolean;
}

export const userApi = api.injectEndpoints({
  endpoints: (builder) => ({
    // Get users with filtering
    getUsers: builder.query<PaginatedResponse<UserResponse>, UserFilters>({
      query: (filters = {}) => {
        const params = new URLSearchParams();
        
        params.append('page', String(filters.page || 1));
        params.append('limit', String(filters.limit || 20));
        
        if (filters.department) params.append('department', filters.department);
        if (filters.role) params.append('role', filters.role);
        if (filters.is_active !== undefined) params.append('is_active', String(filters.is_active));
        if (filters.search) params.append('search', filters.search);
        
        return {
          url: '/users',
          params: Object.fromEntries(params),
        };
      },
      providesTags: (result) =>
        result
          ? [
              ...result.data.map(({ id }) => ({ type: 'User' as const, id })),
              { type: 'User', id: 'LIST' },
            ]
          : [{ type: 'User', id: 'LIST' }],
    }),

    // Get single user
    getUser: builder.query<UserResponse, string>({
      query: (id) => `/users/${id}`,
      providesTags: (result, error, id) => [{ type: 'User', id }],
    }),

    // Create user
    createUser: builder.mutation<UserResponse, CreateUserRequest>({
      query: (userData) => ({
        url: '/users',
        method: 'POST',
        body: userData,
      }),
      invalidatesTags: [{ type: 'User', id: 'LIST' }],
    }),

    // Update user
    updateUser: builder.mutation<UserResponse, { id: string; data: UpdateUserRequest }>({
      query: ({ id, data }) => ({
        url: `/users/${id}`,
        method: 'PUT',
        body: data,
      }),
      invalidatesTags: (result, error, { id }) => [
        { type: 'User', id },
        { type: 'User', id: 'LIST' },
      ],
    }),

    // Deactivate user
    deactivateUser: builder.mutation<void, string>({
      query: (id) => ({
        url: `/users/${id}/deactivate`,
        method: 'POST',
      }),
      invalidatesTags: (result, error, id) => [
        { type: 'User', id },
        { type: 'User', id: 'LIST' },
      ],
    }),

    // Get user roles
    getUserRoles: builder.query<string[], void>({
      query: () => '/users/roles',
    }),

    // Get departments
    getDepartments: builder.query<string[], void>({
      query: () => '/users/departments',
    }),
  }),
});

export const {
  useGetUsersQuery,
  useGetUserQuery,
  useCreateUserMutation,
  useUpdateUserMutation,
  useDeactivateUserMutation,
  useGetUserRolesQuery,
  useGetDepartmentsQuery,
} = userApi;