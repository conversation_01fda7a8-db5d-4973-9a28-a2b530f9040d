import { api } from '../api';
import type { PaginatedResponse } from '../../types/api';

export interface Driver {
  id: string;
  employee_id: string;
  first_name: string;
  last_name: string;
  email: string;
  phone: string;
  license_number: string;
  license_expiry: string;
  license_class: string;
  department: string;
  status: 'active' | 'inactive' | 'suspended';
  assigned_vehicles: string[];
  total_trips: number;
  total_distance: number;
  safety_score: number;
  created_at: string;
  updated_at: string;
}

export interface Trip {
  id: string;
  driver_id: string;
  vehicle_id: string;
  start_location: string;
  end_location: string;
  start_time: string;
  end_time?: string;
  distance: number;
  fuel_consumed?: number;
  status: 'in_progress' | 'completed' | 'cancelled';
  purpose: string;
  odometer_start: number;
  odometer_end?: number;
  created_at: string;
}

export interface DriverFilters {
  page?: number;
  limit?: number;
  department?: string;
  status?: string;
  search?: string;
}

// Driver creation and update types
export interface DriverRequest {
  employee_id: string;
  first_name: string;
  last_name: string;
  email: string;
  phone: string;
  license_number: string;
  license_expiry: string;
  license_class: string;
  department: string;
  status: 'active' | 'inactive' | 'probationary';
  assigned_vehicles?: string[];
  // License & Qualifications
  license_endorsements?: string[];
  license_restrictions?: string[];
  certifications?: string[];
  // Vehicle Assignment & Permissions
  primary_vehicle_id?: string;
  vehicle_type_restrictions?: string[];
  geofencing_enabled?: boolean;
  after_hours_driving_enabled?: boolean;
  max_booking_duration_hours?: number;
  // System Access
  app_access_enabled?: boolean;
  aarto_enrolled?: boolean;
}

export interface DriverFormData {
  // Step 1: Basic Information
  firstName: string;
  lastName: string;
  employeeId: string;
  department: string;
  email: string;
  phone: string;
  status: 'active' | 'inactive' | 'probationary';

  // Step 2: License & Qualifications
  licenseNumber: string;
  licenseClass: string;
  licenseExpiry: string;
  licenseEndorsements: string[];
  licenseRestrictions: string[];
  licenseDocument?: File;
  certifications: string[];
  certificationDocuments?: File[];

  // Step 3: Vehicle Assignment & Permissions
  assignedVehicles: string[];
  primaryVehicleId?: string;
  vehicleTypeRestrictions: string[];
  geofencingEnabled: boolean;
  afterHoursDrivingEnabled: boolean;
  maxBookingDurationHours: number;

  // Step 4: System Access & Review
  appAccessEnabled: boolean;
  sendWelcomeEmail: boolean;
  aartoEnrolled: boolean;
}

export const driverApi = api.injectEndpoints({
  endpoints: (builder) => ({
    // Get drivers
    getDrivers: builder.query<PaginatedResponse<Driver>, DriverFilters>({
      query: (filters = {}) => {
        const params = new URLSearchParams();
        
        params.append('page', String(filters.page || 1));
        params.append('limit', String(filters.limit || 20));
        
        if (filters.department) params.append('department', filters.department);
        if (filters.status) params.append('status', filters.status);
        if (filters.search) params.append('search', filters.search);
        
        return {
          url: '/drivers',
          params: Object.fromEntries(params),
        };
      },
      providesTags: (result) =>
        result
          ? [
              ...result.data.map(({ id }) => ({ type: 'Driver' as const, id })),
              { type: 'Driver', id: 'LIST' },
            ]
          : [{ type: 'Driver', id: 'LIST' }],
    }),

    // Get driver trips
    getDriverTrips: builder.query<PaginatedResponse<Trip>, { driver_id: string; page?: number; limit?: number }>({
      query: ({ driver_id, page = 1, limit = 20 }) => ({
        url: `/drivers/${driver_id}/trips`,
        params: { page, limit },
      }),
      providesTags: (result, error, { driver_id }) => [
        { type: 'Driver', id: `${driver_id}_TRIPS` },
      ],
    }),

    // Start trip
    startTrip: builder.mutation<Trip, {
      driver_id: string;
      vehicle_id: string;
      start_location: string;
      purpose: string;
      odometer_start: number;
    }>({
      query: (tripData) => ({
        url: '/trips/start',
        method: 'POST',
        body: tripData,
      }),
      invalidatesTags: (result, error, { driver_id }) => [
        { type: 'Driver', id: `${driver_id}_TRIPS` },
      ],
    }),

    // End trip
    endTrip: builder.mutation<Trip, {
      trip_id: string;
      end_location: string;
      odometer_end: number;
      fuel_consumed?: number;
    }>({
      query: ({ trip_id, ...data }) => ({
        url: `/trips/${trip_id}/end`,
        method: 'POST',
        body: data,
      }),
      invalidatesTags: [{ type: 'Driver', id: 'LIST' }],
    }),

    // Get single driver
    getDriver: builder.query<Driver, string>({
      query: (id) => `/drivers/${id}`,
      providesTags: (result, error, id) => [{ type: 'Driver', id }],
    }),

    // Create driver
    createDriver: builder.mutation<Driver, DriverRequest>({
      query: (driverData) => ({
        url: '/drivers',
        method: 'POST',
        body: driverData,
      }),
      invalidatesTags: [{ type: 'Driver', id: 'LIST' }],
    }),

    // Update driver
    updateDriver: builder.mutation<Driver, { id: string; data: Partial<DriverRequest> }>({
      query: ({ id, data }) => ({
        url: `/drivers/${id}`,
        method: 'PUT',
        body: data,
      }),
      invalidatesTags: (result, error, { id }) => [
        { type: 'Driver', id },
        { type: 'Driver', id: 'LIST' },
      ],
    }),

    // Delete driver
    deleteDriver: builder.mutation<void, string>({
      query: (id) => ({
        url: `/drivers/${id}`,
        method: 'DELETE',
      }),
      invalidatesTags: (result, error, id) => [
        { type: 'Driver', id },
        { type: 'Driver', id: 'LIST' },
      ],
    }),

    // Assign vehicles to driver
    assignVehiclesToDriver: builder.mutation<Driver, {
      driver_id: string;
      vehicle_ids: string[];
      primary_vehicle_id?: string;
    }>({
      query: ({ driver_id, ...data }) => ({
        url: `/drivers/${driver_id}/assign-vehicles`,
        method: 'POST',
        body: data,
      }),
      invalidatesTags: (result, error, { driver_id }) => [
        { type: 'Driver', id: driver_id },
        { type: 'Driver', id: 'LIST' },
      ],
    }),

    // Verify driver license
    verifyDriverLicense: builder.mutation<{ verified: boolean; details?: any }, {
      driver_id: string;
      license_number: string;
    }>({
      query: ({ driver_id, license_number }) => ({
        url: `/drivers/${driver_id}/verify-license`,
        method: 'POST',
        body: { license_number },
      }),
    }),
  }),
});

export const {
  useGetDriversQuery,
  useGetDriverQuery,
  useGetDriverTripsQuery,
  useStartTripMutation,
  useEndTripMutation,
  useCreateDriverMutation,
  useUpdateDriverMutation,
  useDeleteDriverMutation,
  useAssignVehiclesToDriverMutation,
  useVerifyDriverLicenseMutation,
} = driverApi;