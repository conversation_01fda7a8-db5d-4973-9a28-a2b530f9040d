import React from 'react';
import { useNavigate } from 'react-router-dom';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { 
  Bell, 
  TrendingUp, 
  DollarSign, 
  Clock,
  Wrench
} from 'lucide-react';

const MerchantDashboard: React.FC = () => {
  const navigate = useNavigate();

  // Mock data - replace with API calls
  const stats = {
    totalJobs: 156,
    avgRating: 4.8,
    outstandingInvoices: 12,
    pendingJobs: 8,
    completedThisMonth: 23,
    revenue: 125000
  };

  const newJobNotifications = [
    {
      id: 'WO-2025-001',
      vehicle: 'Toyota Hilux - GP123ABC',
      type: 'Brake Service',
      priority: 'High',
      dueDate: '2025-01-15',
      location: 'Pretoria Central'
    },
    {
      id: 'WO-2025-002', 
      vehicle: 'Ford Ranger - GP456DEF',
      type: 'Engine Diagnostics',
      priority: 'Medium',
      dueDate: '2025-01-18',
      location: 'Centurion'
    }
  ];

  return (
    <div className="container mx-auto px-3 sm:px-4 lg:px-6 py-4 sm:py-6 lg:py-8 space-y-4 sm:space-y-6 max-w-7xl">
      {/* Header */}
      <div className="text-center sm:text-left">
        <h1 className="text-xl sm:text-2xl lg:text-3xl font-bold text-gray-900">Merchant Dashboard</h1>
        <p className="text-sm sm:text-base text-gray-600">Manage your service operations and performance</p>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-2 sm:grid-cols-4 gap-3 sm:gap-4 lg:gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Jobs</CardTitle>
            <Wrench className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.totalJobs}</div>
            <p className="text-xs text-muted-foreground">
              +12% from last month
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Average Rating</CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.avgRating}/5</div>
            <p className="text-xs text-muted-foreground">
              +0.2 from last month
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Outstanding Invoices</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.outstandingInvoices}</div>
            <p className="text-xs text-muted-foreground">
              R{stats.revenue.toLocaleString()} pending
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Pending Jobs</CardTitle>
            <Clock className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.pendingJobs}</div>
            <p className="text-xs text-muted-foreground">
              Requires attention
            </p>
          </CardContent>
        </Card>
      </div>

      {/* New Job Notifications */}
      <Card>
        <CardHeader>
          <CardTitle>New Job Notifications</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {newJobNotifications.map((job) => (
              <div key={job.id} className="flex items-center justify-between p-4 border rounded-lg">
                <div>
                  <h4 className="font-semibold">{job.type}</h4>
                  <p className="text-sm text-gray-600">{job.vehicle}</p>
                  <div className="flex items-center space-x-4 text-xs text-gray-500 mt-1">
                    <span className={`px-2 py-1 rounded ${job.priority === 'High' ? 'bg-red-100 text-red-800' : 'bg-yellow-100 text-yellow-800'}`}>
                      {job.priority}
                    </span>
                    <span>{job.location}</span>
                    <div className="flex items-center">
                      <Clock className="h-3 w-3 mr-1" />
                      Due: {job.dueDate}
                    </div>
                  </div>
                </div>
                <div className="flex space-x-2">
                  <Button variant="outline" size="sm" onClick={() => navigate(`/merchant/job-cards/${job.id}`)}>
                    View Details
                  </Button>
                  <Button size="sm">
                    Accept Job
                  </Button>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Quick Actions */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Work Orders</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <div className="flex justify-between">
                <span className="text-sm">Pending</span>
                <span className="font-semibold">{stats.pendingJobs}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm">In Progress</span>
                <span className="font-semibold">5</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm">Completed</span>
                <span className="font-semibold">{stats.completedThisMonth}</span>
              </div>
            </div>
            <Button 
              className="w-full mt-4" 
              variant="outline"
              onClick={() => navigate('/merchant/work-orders')}
            >
              View Work Queue
            </Button>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Invoices</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <div className="flex justify-between">
                <span className="text-sm">Draft</span>
                <span className="font-semibold">3</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm">Submitted</span>
                <span className="font-semibold">7</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm">Paid</span>
                <span className="font-semibold">15</span>
              </div>
            </div>
            <Button 
              className="w-full mt-4" 
              variant="outline"
              onClick={() => navigate('/merchant/invoices')}
            >
              Manage Invoices
            </Button>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Performance</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <div className="flex justify-between">
                <span className="text-sm">On-time Delivery</span>
                <span className="font-semibold">94%</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm">Quality Score</span>
                <span className="font-semibold">4.8/5</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm">Response Time</span>
                <span className="font-semibold">2.5h</span>
              </div>
            </div>
            <Button 
              className="w-full mt-4" 
              variant="outline"
              onClick={() => navigate('/merchant/payments')}
            >
              View Payment Status
            </Button>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default MerchantDashboard;


