import { api } from '../api';
import type { PaginatedResponse } from '../../types/api';

export interface VehicleBooking {
  id: string;
  vehicle_id: string;
  vehicle: {
    registration_number: string;
    make: string;
    model: string;
  };
  driver_id: string;
  driver_name: string;
  start_date: string;
  end_date: string;
  purpose: string;
  destination: string;
  status: 'pending' | 'approved' | 'active' | 'completed' | 'cancelled';
  approved_by?: string;
  mileage_start?: number;
  mileage_end?: number;
  fuel_start?: number;
  fuel_end?: number;
  created_at: string;
}

export interface TripLog {
  id: string;
  booking_id: string;
  vehicle_id: string;
  driver_id: string;
  start_location: string;
  end_location: string;
  start_time: string;
  end_time?: string;
  distance: number;
  fuel_consumed: number;
  purpose: string;
  passengers: number;
  route_taken?: string;
  notes?: string;
  status: 'in_progress' | 'completed';
}

export interface MaintenanceScheduleItem {
  id: string;
  vehicle_id: string;
  vehicle: {
    registration_number: string;
    make: string;
    model: string;
  };
  service_type: 'routine' | 'major' | 'annual' | 'roadworthy';
  due_date: string;
  due_mileage: number;
  current_mileage: number;
  status: 'upcoming' | 'due' | 'overdue' | 'scheduled' | 'completed';
  vendor_id?: string;
  vendor_name?: string;
  estimated_cost?: number;
  priority: 'low' | 'medium' | 'high';
}

export const transportApi = api.injectEndpoints({
  endpoints: (builder) => ({
    // Vehicle Bookings
    getVehicleBookings: builder.query<PaginatedResponse<VehicleBooking>, {
      page?: number;
      limit?: number;
      status?: string;
      driver_id?: string;
      vehicle_id?: string;
      date_from?: string;
      date_to?: string;
    }>({
      query: (filters = {}) => {
        const params = new URLSearchParams();
        Object.entries(filters).forEach(([key, value]) => {
          if (value) params.append(key, String(value));
        });
        return {
          url: '/transport/bookings',
          params: Object.fromEntries(params),
        };
      },
      providesTags: [{ type: 'Driver', id: 'BOOKINGS' }],
    }),

    // Create booking
    createVehicleBooking: builder.mutation<VehicleBooking, {
      vehicle_id: string;
      driver_id: string;
      start_date: string;
      end_date: string;
      purpose: string;
      destination: string;
    }>({
      query: (data) => ({
        url: '/transport/bookings',
        method: 'POST',
        body: data,
      }),
      invalidatesTags: [{ type: 'Driver', id: 'BOOKINGS' }],
    }),

    // Approve booking
    approveBooking: builder.mutation<VehicleBooking, { id: string; notes?: string }>({
      query: ({ id, notes }) => ({
        url: `/transport/bookings/${id}/approve`,
        method: 'POST',
        body: { notes },
      }),
      invalidatesTags: [{ type: 'Driver', id: 'BOOKINGS' }],
    }),

    // Get available vehicles
    getAvailableVehicles: builder.query<Array<{
      id: string;
      registration_number: string;
      make: string;
      model: string;
      fuel_level: number;
      mileage: number;
      status: string;
    }>, { start_date: string; end_date: string }>({
      query: (params) => ({
        url: '/transport/vehicles/available',
        params,
      }),
    }),

    // Trip Logs
    getTripLogs: builder.query<PaginatedResponse<TripLog>, {
      page?: number;
      limit?: number;
      driver_id?: string;
      vehicle_id?: string;
      date_from?: string;
      date_to?: string;
    }>({
      query: (filters = {}) => {
        const params = new URLSearchParams();
        Object.entries(filters).forEach(([key, value]) => {
          if (value) params.append(key, String(value));
        });
        return {
          url: '/transport/trip-logs',
          params: Object.fromEntries(params),
        };
      },
      providesTags: [{ type: 'Driver', id: 'TRIP_LOGS' }],
    }),

    // Start trip
    startTrip: builder.mutation<TripLog, {
      booking_id: string;
      start_location: string;
      passengers: number;
      fuel_start: number;
      mileage_start: number;
    }>({
      query: (data) => ({
        url: '/transport/trip-logs/start',
        method: 'POST',
        body: data,
      }),
      invalidatesTags: [{ type: 'Driver', id: 'TRIP_LOGS' }],
    }),

    // End trip
    endTrip: builder.mutation<TripLog, {
      trip_id: string;
      end_location: string;
      fuel_end: number;
      mileage_end: number;
      notes?: string;
    }>({
      query: ({ trip_id, ...data }) => ({
        url: `/transport/trip-logs/${trip_id}/end`,
        method: 'POST',
        body: data,
      }),
      invalidatesTags: [{ type: 'Driver', id: 'TRIP_LOGS' }],
    }),

    // Maintenance Scheduling
    getMaintenanceSchedule: builder.query<PaginatedResponse<MaintenanceScheduleItem>, {
      page?: number;
      limit?: number;
      status?: string;
      priority?: string;
      overdue_only?: boolean;
    }>({
      query: (filters = {}) => {
        const params = new URLSearchParams();
        Object.entries(filters).forEach(([key, value]) => {
          if (value !== undefined) params.append(key, String(value));
        });
        return {
          url: '/transport/maintenance/schedule',
          params: Object.fromEntries(params),
        };
      },
      providesTags: [{ type: 'WorkOrder', id: 'MAINTENANCE_SCHEDULE' }],
    }),

    // Schedule maintenance
    scheduleMaintenanceFromSchedule: builder.mutation<void, {
      schedule_id: string;
      vendor_id: string;
      scheduled_date: string;
      notes?: string;
    }>({
      query: ({ schedule_id, ...data }) => ({
        url: `/transport/maintenance/schedule/${schedule_id}/book`,
        method: 'POST',
        body: data,
      }),
      invalidatesTags: [{ type: 'WorkOrder', id: 'MAINTENANCE_SCHEDULE' }],
    }),

    // Get transport dashboard stats
    getTransportDashboardStats: builder.query<{
      total_vehicles: number;
      available_vehicles: number;
      active_bookings: number;
      pending_approvals: number;
      overdue_maintenance: number;
      fuel_efficiency: number;
    }, void>({
      query: () => '/transport/dashboard/stats',
      providesTags: [{ type: 'Driver', id: 'TRANSPORT_STATS' }],
    }),

    // Get calendar events
    getCalendarEvents: builder.query<Array<{
      id: string;
      title: string;
      start: string;
      end: string;
      type: 'booking' | 'maintenance' | 'inspection';
      vehicle_registration: string;
      status: string;
    }>, { start_date: string; end_date: string }>({
      query: (params) => ({
        url: '/transport/calendar/events',
        params,
      }),
    }),
  }),
});

export const {
  useGetVehicleBookingsQuery,
  useCreateVehicleBookingMutation,
  useApproveBookingMutation,
  useGetAvailableVehiclesQuery,
  useGetTripLogsQuery,
  useStartTripMutation,
  useEndTripMutation,
  useGetMaintenanceScheduleQuery,
  useScheduleMaintenanceFromScheduleMutation,
  useGetTransportDashboardStatsQuery,
  useGetCalendarEventsQuery,
} = transportApi;