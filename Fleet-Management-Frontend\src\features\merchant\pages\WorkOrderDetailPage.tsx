import React, { useState } from 'react';
import { use<PERSON>ara<PERSON>, useNavigate } from 'react-router-dom';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { 
  ArrowLeft,
  Car,
  MapPin,
  Calendar,
  Clock,
  User,
  Phone,
  Mail,
  FileText,
  AlertTriangle,
  CheckCircle,
  X,
  MessageSquare,
  Download,
  Paperclip
} from 'lucide-react';
import { useNotifications } from '@/hooks/useNotifications';

interface WorkOrderDetail {
  id: string;
  workOrderNumber: string;
  title: string;
  description: string;
  status: 'New' | 'Accepted' | 'In Progress' | 'Completed' | 'Declined';
  priority: 'Low' | 'Medium' | 'High' | 'Urgent';
  vehicle: {
    registration: string;
    make: string;
    model: string;
    year: number;
    vin: string;
    currentMileage: number;
  };
  serviceType: string;
  location: string;
  estimatedHours: number;
  dueDate: string;
  createdDate: string;
  customer: {
    department: string;
    contactPerson: string;
    phone: string;
    email: string;
    address: string;
  };
  requestedBy: {
    name: string;
    email: string;
    phone: string;
  };
  slaHours: number;
  timeRemaining: number;
  attachments: Array<{
    id: string;
    name: string;
    type: string;
    size: number;
    uploadedDate: string;
  }>;
  timeline: Array<{
    id: string;
    action: string;
    description: string;
    timestamp: string;
    performedBy: string;
  }>;
}

const WorkOrderDetailPage: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const { showSuccess, showError } = useNotifications();
  const [comment, setComment] = useState('');
  const [isAccepting, setIsAccepting] = useState(false);
  const [isDeclining, setIsDeclining] = useState(false);

  // Mock data
  const workOrder: WorkOrderDetail = {
    id: id || 'WO-2025-001',
    workOrderNumber: 'WO-2025-001',
    title: 'Brake Pad Replacement',
    description: 'Vehicle requires immediate brake pad replacement due to excessive wear. Driver reported squealing noise and reduced braking efficiency. Please inspect brake system thoroughly and replace pads as needed.',
    status: 'New',
    priority: 'High',
    vehicle: {
      registration: 'GP123ABC',
      make: 'Toyota',
      model: 'Hilux',
      year: 2020,
      vin: '1HGBH41JXMN109186',
      currentMileage: 85000
    },
    serviceType: 'Brake Service',
    location: 'Pretoria Central',
    estimatedHours: 4,
    dueDate: '2025-01-15T17:00:00Z',
    createdDate: '2025-01-13T09:00:00Z',
    customer: {
      department: 'Department of Health',
      contactPerson: 'Dr. Sarah Johnson',
      phone: '************',
      email: '<EMAIL>',
      address: '123 Government Ave, Pretoria Central, 0001'
    },
    requestedBy: {
      name: 'John Smith',
      email: '<EMAIL>',
      phone: '************'
    },
    slaHours: 48,
    timeRemaining: 36,
    attachments: [
      {
        id: '1',
        name: 'brake_inspection_photos.pdf',
        type: 'application/pdf',
        size: 2048576,
        uploadedDate: '2025-01-13T09:30:00Z'
      },
      {
        id: '2',
        name: 'vehicle_service_history.xlsx',
        type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        size: 1024000,
        uploadedDate: '2025-01-13T10:00:00Z'
      }
    ],
    timeline: [
      {
        id: '1',
        action: 'Work Order Created',
        description: 'Work order created by Transport Officer',
        timestamp: '2025-01-13T09:00:00Z',
        performedBy: 'John Smith'
      },
      {
        id: '2',
        action: 'Assigned to Vendor',
        description: 'Work order assigned to AutoCare Services',
        timestamp: '2025-01-13T09:15:00Z',
        performedBy: 'Fleet Manager'
      }
    ]
  };

  const handleAcceptJob = async () => {
    setIsAccepting(true);
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      showSuccess('Work order accepted successfully!');
      navigate('/merchant/work-orders');
    } catch (error) {
      showError('Failed to accept work order. Please try again.');
    } finally {
      setIsAccepting(false);
    }
  };

  const handleDeclineJob = async () => {
    if (!comment.trim()) {
      showError('Please provide a reason for declining this work order.');
      return;
    }
    
    setIsDeclining(true);
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      showSuccess('Work order declined successfully.');
      navigate('/merchant/work-orders');
    } catch (error) {
      showError('Failed to decline work order. Please try again.');
    } finally {
      setIsDeclining(false);
    }
  };

  const handleSubmitQuote = () => {
    navigate(`/merchant/quotes?workOrderId=${workOrder.id}`);
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'Urgent': return 'bg-red-100 text-red-800';
      case 'High': return 'bg-orange-100 text-orange-800';
      case 'Medium': return 'bg-yellow-100 text-yellow-800';
      case 'Low': return 'bg-green-100 text-green-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'New': return 'bg-blue-100 text-blue-800';
      case 'Accepted': return 'bg-green-100 text-green-800';
      case 'In Progress': return 'bg-yellow-100 text-yellow-800';
      case 'Completed': return 'bg-gray-100 text-gray-800';
      case 'Declined': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const formatTimeAgo = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60));
    
    if (diffInHours < 1) return 'Just now';
    if (diffInHours < 24) return `${diffInHours}h ago`;
    const diffInDays = Math.floor(diffInHours / 24);
    return `${diffInDays}d ago`;
  };

  return (
    <div className="container mx-auto px-6 py-8 space-y-6 max-w-4xl">
      {/* Header */}
      <div className="flex items-center justify-between mb-8">
        <div className="flex items-center space-x-4">
          <Button
            variant="outline"
            onClick={() => navigate('/merchant/work-orders')}
            className="flex items-center space-x-2"
          >
            <ArrowLeft className="h-4 w-4" />
            <span>Back to Queue</span>
          </Button>
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Work Order Details</h1>
            <p className="text-gray-600">Review and manage work order</p>
          </div>
        </div>
        <div className="flex items-center space-x-2">
          <Badge className={getPriorityColor(workOrder.priority)}>
            {workOrder.priority}
          </Badge>
          <Badge className={getStatusColor(workOrder.status)}>
            {workOrder.status}
          </Badge>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Main Content */}
        <div className="lg:col-span-2 space-y-6">
          {/* Work Order Details */}
          <Card>
            <CardHeader>
              <CardTitle>Work Order Details</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <h4 className="font-semibold text-gray-900 mb-2">Description</h4>
                <p className="text-gray-700">{workOrder.description}</p>
              </div>
              
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="text-sm font-medium text-gray-600">Service Type</label>
                  <p className="font-semibold">{workOrder.serviceType}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-600">Estimated Hours</label>
                  <p className="font-semibold">{workOrder.estimatedHours} hours</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-600">Location</label>
                  <p className="font-semibold">{workOrder.location}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-600">Due Date</label>
                  <p className="font-semibold">{new Date(workOrder.dueDate).toLocaleDateString()}</p>
                </div>
              </div>

              <div className="flex items-center space-x-4 text-sm">
                <div className="flex items-center text-orange-600">
                  <Clock className="h-4 w-4 mr-1" />
                  {workOrder.timeRemaining}h remaining
                </div>
                <div className="flex items-center text-blue-600">
                  <AlertTriangle className="h-4 w-4 mr-1" />
                  SLA: {workOrder.slaHours}h
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Vehicle Information */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Car className="h-5 w-5 mr-2" />
                Vehicle Information
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="text-sm font-medium text-gray-600">Registration</label>
                  <p className="text-lg font-semibold">{workOrder.vehicle.registration}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-600">Make & Model</label>
                  <p>{workOrder.vehicle.make} {workOrder.vehicle.model}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-600">Year</label>
                  <p>{workOrder.vehicle.year}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-600">Current Mileage</label>
                  <p>{workOrder.vehicle.currentMileage.toLocaleString()} km</p>
                </div>
                <div className="col-span-2">
                  <label className="text-sm font-medium text-gray-600">VIN</label>
                  <p className="text-sm font-mono">{workOrder.vehicle.vin}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Attachments */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Paperclip className="h-5 w-5 mr-2" />
                Attachments ({workOrder.attachments.length})
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {workOrder.attachments.map((attachment) => (
                  <div key={attachment.id} className="flex items-center justify-between p-3 border rounded-lg">
                    <div className="flex items-center space-x-3">
                      <FileText className="h-5 w-5 text-blue-600" />
                      <div>
                        <p className="font-medium">{attachment.name}</p>
                        <p className="text-sm text-gray-600">
                          {formatFileSize(attachment.size)} • {formatTimeAgo(attachment.uploadedDate)}
                        </p>
                      </div>
                    </div>
                    <Button variant="outline" size="sm">
                      <Download className="h-4 w-4 mr-2" />
                      Download
                    </Button>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Timeline */}
          <Card>
            <CardHeader>
              <CardTitle>Timeline</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {workOrder.timeline.map((event) => (
                  <div key={event.id} className="flex items-start space-x-3">
                    <div className="p-2 bg-blue-100 rounded-full">
                      <Clock className="h-4 w-4 text-blue-600" />
                    </div>
                    <div className="flex-1">
                      <div className="flex items-center justify-between">
                        <h5 className="font-medium">{event.action}</h5>
                        <span className="text-sm text-gray-500">{formatTimeAgo(event.timestamp)}</span>
                      </div>
                      <p className="text-sm text-gray-600 mt-1">{event.description}</p>
                      <p className="text-xs text-gray-500 mt-1">by {event.performedBy}</p>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          {/* Customer Information */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <User className="h-5 w-5 mr-2" />
                Customer Information
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <label className="text-sm font-medium text-gray-600">Department</label>
                <p className="font-semibold">{workOrder.customer.department}</p>
              </div>
              <div>
                <label className="text-sm font-medium text-gray-600">Contact Person</label>
                <p>{workOrder.customer.contactPerson}</p>
              </div>
              <div>
                <label className="text-sm font-medium text-gray-600">Phone</label>
                <div className="flex items-center">
                  <Phone className="h-4 w-4 mr-2 text-gray-400" />
                  <p>{workOrder.customer.phone}</p>
                </div>
              </div>
              <div>
                <label className="text-sm font-medium text-gray-600">Email</label>
                <div className="flex items-center">
                  <Mail className="h-4 w-4 mr-2 text-gray-400" />
                  <p className="text-sm">{workOrder.customer.email}</p>
                </div>
              </div>
              <div>
                <label className="text-sm font-medium text-gray-600">Address</label>
                <div className="flex items-start">
                  <MapPin className="h-4 w-4 mr-2 text-gray-400 mt-1" />
                  <p className="text-sm">{workOrder.customer.address}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Actions */}
          {workOrder.status === 'New' && (
            <Card>
              <CardHeader>
                <CardTitle>Actions</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <Button 
                  className="w-full" 
                  onClick={handleAcceptJob}
                  disabled={isAccepting}
                >
                  <CheckCircle className="h-4 w-4 mr-2" />
                  {isAccepting ? 'Accepting...' : 'Accept Work Order'}
                </Button>
                
                <div className="space-y-2">
                  <label className="text-sm font-medium text-gray-600">
                    Reason for declining (if applicable)
                  </label>
                  <Textarea
                    placeholder="Please provide a reason if declining this work order..."
                    value={comment}
                    onChange={(e) => setComment(e.target.value)}
                    rows={3}
                  />
                </div>
                
                <Button 
                  variant="outline" 
                  className="w-full" 
                  onClick={handleDeclineJob}
                  disabled={isDeclining}
                >
                  <X className="h-4 w-4 mr-2" />
                  {isDeclining ? 'Declining...' : 'Decline Work Order'}
                </Button>
              </CardContent>
            </Card>
          )}

          {workOrder.status === 'Accepted' && (
            <Card>
              <CardHeader>
                <CardTitle>Next Steps</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <Button className="w-full" onClick={handleSubmitQuote}>
                  <FileText className="h-4 w-4 mr-2" />
                  Submit Quote
                </Button>
                <Button variant="outline" className="w-full">
                  <MessageSquare className="h-4 w-4 mr-2" />
                  Contact Customer
                </Button>
              </CardContent>
            </Card>
          )}

          {/* Requested By */}
          <Card>
            <CardHeader>
              <CardTitle>Requested By</CardTitle>
            </CardHeader>
            <CardContent className="space-y-2">
              <p className="font-semibold">{workOrder.requestedBy.name}</p>
              <div className="flex items-center text-sm text-gray-600">
                <Mail className="h-4 w-4 mr-2" />
                {workOrder.requestedBy.email}
              </div>
              <div className="flex items-center text-sm text-gray-600">
                <Phone className="h-4 w-4 mr-2" />
                {workOrder.requestedBy.phone}
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
};

export default WorkOrderDetailPage;
