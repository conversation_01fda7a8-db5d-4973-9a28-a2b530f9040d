import React from 'react';
import { useNavigate } from 'react-router-dom';
import {
  DollarSign,
  TrendingUp,
  TrendingDown,
  AlertCircle,
  FileText,
  Clock,
  CheckCircle,
  XCircle,
  Calendar,
  Target
} from 'lucide-react';

interface FinanceMetric {
  title: string;
  value: string;
  change: string;
  changeType: 'increase' | 'decrease' | 'neutral';
  icon: React.ElementType;
}

interface InvoiceStatus {
  status: string;
  count: number;
  amount: string;
  color: string;
}

const FinanceDashboard: React.FC = () => {
  const navigate = useNavigate();

  const metrics: FinanceMetric[] = [
    {
      title: 'Total Pending Invoices',
      value: 'R 2,450,000',
      change: '+12%',
      changeType: 'increase',
      icon: FileText
    },
    {
      title: 'Monthly Budget Utilization',
      value: '78%',
      change: '+5%',
      changeType: 'increase',
      icon: Target
    },
    {
      title: 'Approved Payments',
      value: 'R 1,890,000',
      change: '+8%',
      changeType: 'increase',
      icon: CheckCircle
    },
    {
      title: 'Overdue Payments',
      value: 'R 340,000',
      change: '-15%',
      changeType: 'decrease',
      icon: AlertCircle
    }
  ];

  const invoiceStatuses: InvoiceStatus[] = [
    { status: 'Pending Approval', count: 23, amount: 'R 890,000', color: 'bg-yellow-500' },
    { status: 'Approved', count: 45, amount: 'R 1,200,000', color: 'bg-green-500' },
    { status: 'Paid', count: 156, amount: 'R 3,400,000', color: 'bg-blue-500' },
    { status: 'Rejected', count: 8, amount: 'R 120,000', color: 'bg-red-500' }
  ];

  const recentInvoices = [
    { id: 'INV-2025-001', vendor: 'AutoFix Garage', amount: 'R 45,000', status: 'Pending', date: '2025-01-15' },
    { id: 'INV-2025-002', vendor: 'Fleet Services Ltd', amount: 'R 78,500', status: 'Approved', date: '2025-01-14' },
    { id: 'INV-2025-003', vendor: 'Tire World', amount: 'R 23,400', status: 'Paid', date: '2025-01-13' },
    { id: 'INV-2025-004', vendor: 'Quick Repairs', amount: 'R 12,800', status: 'Pending', date: '2025-01-12' }
  ];

  const budgetAlerts = [
    { department: 'Transport Dept A', utilization: 95, budget: 'R 500,000', spent: 'R 475,000' },
    { department: 'Transport Dept B', utilization: 87, budget: 'R 300,000', spent: 'R 261,000' },
    { department: 'Emergency Fleet', utilization: 78, budget: 'R 200,000', spent: 'R 156,000' }
  ];

  return (
    <div className="p-3 sm:p-4 lg:p-6 space-y-4 sm:space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-4">
        <div>
          <h1 className="text-xl sm:text-2xl font-bold text-gray-900">Finance Dashboard</h1>
          <p className="text-sm sm:text-base text-gray-600">Financial overview and invoice management</p>
        </div>
        <div className="flex flex-col sm:flex-row gap-2 sm:gap-3">
          <button
            onClick={() => navigate('/finance/invoices')}
            className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 text-sm sm:text-base h-10 sm:h-auto"
          >
            <span className="hidden sm:inline">View All Invoices</span>
            <span className="sm:hidden">Invoices</span>
          </button>
          <button
            onClick={() => navigate('/finance/budget')}
            className="px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 text-sm sm:text-base h-10 sm:h-auto"
          >
            <span className="hidden sm:inline">Manage Budgets</span>
            <span className="sm:hidden">Budgets</span>
          </button>
        </div>
      </div>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-3 sm:gap-4 lg:gap-6">
        {metrics.map((metric, index) => {
          const IconComponent = metric.icon;
          return (
            <div key={index} className="bg-white p-3 sm:p-4 lg:p-6 rounded-lg shadow-sm border border-gray-200">
              <div className="flex items-center justify-between">
                <div className="min-w-0 flex-1">
                  <p className="text-xs sm:text-sm font-medium text-gray-600">{metric.title}</p>
                  <p className="text-lg sm:text-xl lg:text-2xl font-bold text-gray-900 mt-1">{metric.value}</p>
                  <div className="flex items-center mt-1 sm:mt-2">
                    {metric.changeType === 'increase' ? (
                      <TrendingUp className="h-3 w-3 sm:h-4 sm:w-4 text-green-500 mr-1 flex-shrink-0" />
                    ) : metric.changeType === 'decrease' ? (
                      <TrendingDown className="h-3 w-3 sm:h-4 sm:w-4 text-red-500 mr-1 flex-shrink-0" />
                    ) : null}
                    <span className={`text-xs sm:text-sm ${
                      metric.changeType === 'increase' ? 'text-green-600' :
                      metric.changeType === 'decrease' ? 'text-red-600' : 'text-gray-600'
                    }`}>
                      {metric.change} from last month
                    </span>
                  </div>
                </div>
                <div className="p-2 sm:p-3 bg-blue-50 rounded-lg flex-shrink-0">
                  <IconComponent className="h-5 w-5 sm:h-6 sm:w-6 text-blue-600" />
                </div>
              </div>
            </div>
          );
        })}
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Invoice Status Overview */}
        <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Invoice Status Overview</h3>
          <div className="space-y-4">
            {invoiceStatuses.map((status, index) => (
              <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <div className="flex items-center">
                  <div className={`w-3 h-3 rounded-full ${status.color} mr-3`}></div>
                  <div>
                    <p className="font-medium text-gray-900">{status.status}</p>
                    <p className="text-sm text-gray-600">{status.count} invoices</p>
                  </div>
                </div>
                <div className="text-right">
                  <p className="font-semibold text-gray-900">{status.amount}</p>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Budget Alerts */}
        <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Budget Alerts</h3>
          <div className="space-y-4">
            {budgetAlerts.map((alert, index) => (
              <div key={index} className="p-4 border border-gray-200 rounded-lg">
                <div className="flex justify-between items-start mb-2">
                  <h4 className="font-medium text-gray-900">{alert.department}</h4>
                  <span className={`px-2 py-1 text-xs rounded-full ${
                    alert.utilization >= 90 ? 'bg-red-100 text-red-800' :
                    alert.utilization >= 80 ? 'bg-yellow-100 text-yellow-800' :
                    'bg-green-100 text-green-800'
                  }`}>
                    {alert.utilization}% utilized
                  </span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2 mb-2">
                  <div 
                    className={`h-2 rounded-full ${
                      alert.utilization >= 90 ? 'bg-red-500' :
                      alert.utilization >= 80 ? 'bg-yellow-500' :
                      'bg-green-500'
                    }`}
                    style={{ width: `${alert.utilization}%` }}
                  ></div>
                </div>
                <div className="flex justify-between text-sm text-gray-600">
                  <span>Spent: {alert.spent}</span>
                  <span>Budget: {alert.budget}</span>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Recent Invoices */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200">
        <div className="p-6 border-b border-gray-200">
          <div className="flex justify-between items-center">
            <h3 className="text-lg font-semibold text-gray-900">Recent Invoices</h3>
            <button
              onClick={() => navigate('/finance/invoices')}
              className="text-blue-600 hover:text-blue-700 text-sm font-medium"
            >
              View All →
            </button>
          </div>
        </div>
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Invoice ID
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Vendor
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Amount
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Status
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Date
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {recentInvoices.map((invoice) => (
                <tr key={invoice.id} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-blue-600">
                    {invoice.id}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {invoice.vendor}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {invoice.amount}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`px-2 py-1 text-xs rounded-full ${
                      invoice.status === 'Paid' ? 'bg-green-100 text-green-800' :
                      invoice.status === 'Approved' ? 'bg-blue-100 text-blue-800' :
                      invoice.status === 'Pending' ? 'bg-yellow-100 text-yellow-800' :
                      'bg-red-100 text-red-800'
                    }`}>
                      {invoice.status}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {invoice.date}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
};

export default FinanceDashboard;