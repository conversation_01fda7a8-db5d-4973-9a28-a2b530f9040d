import { createApi } from '@reduxjs/toolkit/query/react';
import { baseQueryWithErrorHandling } from '../store/api/baseQuery';

// Common tag types for cache invalidation
export const API_TAGS = {
  Vehicle: 'Vehicle',
  WorkOrder: 'WorkOrder',
  Driver: 'Driver',
  Vendor: 'Vendor',
  Invoice: 'Invoice',
  User: 'User',
  Maintenance: 'Maintenance',
  Inspector: 'Inspector',
  Transport: 'Transport',
  Finance: 'Finance',
  Document: 'Document',
  Report: 'Report',
  Settings: 'Settings'
} as const;

export type ApiTag = typeof API_TAGS[keyof typeof API_TAGS];

// Base API configuration
export const apiService = createApi({
  reducerPath: 'api',
  baseQuery: baseQueryWithErrorHandling,
  tagTypes: Object.values(API_TAGS),
  keepUnusedDataFor: 300, // 5 minutes
  refetchOnMountOrArgChange: 30, // 30 seconds
  refetchOnFocus: true,
  refetchOnReconnect: true,
  endpoints: () => ({}),
});

// Cache management utilities
export const cacheUtils = {
  // Invalidate all data for a specific entity
  invalidateEntity: (entityType: ApiTag) => ({
    type: entityType,
    id: 'LIST'
  }),
  
  // Invalidate specific item
  invalidateItem: (entityType: ApiTag, id: string | number) => ({
    type: entityType,
    id
  }),
  
  // Provide tags for list endpoints
  providesList: (entityType: ApiTag, results?: { id: string | number }[]) => [
    { type: entityType, id: 'LIST' },
    ...(results?.map(({ id }) => ({ type: entityType, id })) || [])
  ],
  
  // Provide tags for single item endpoints
  providesItem: (entityType: ApiTag, id: string | number) => [
    { type: entityType, id }
  ]
};

// Common query parameters interface
export interface BaseQueryParams {
  page?: number;
  limit?: number;
  search?: string;
  sort_by?: string;
  sort_order?: 'asc' | 'desc';
  filters?: Record<string, any>;
}

// Standard pagination response
export interface PaginatedResponse<T> {
  items: T[];
  total: number;
  page: number;
  limit: number;
  pages: number;
  has_next: boolean;
  has_prev: boolean;
}

// File upload utilities
export const createFormData = (data: Record<string, any>, files?: File[]): FormData => {
  const formData = new FormData();
  
  // Add regular fields
  Object.entries(data).forEach(([key, value]) => {
    if (value !== null && value !== undefined) {
      formData.append(key, typeof value === 'object' ? JSON.stringify(value) : value);
    }
  });
  
  // Add files
  files?.forEach((file, index) => {
    formData.append(`file_${index}`, file);
  });
  
  return formData;
};