import React, { useState } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { Building2, Star, MapPin, Clock, CheckCircle, Send, ArrowLeft } from 'lucide-react';

interface Merchant {
  id: string;
  name: string;
  rating: number;
  location: string;
  distance: number;
  specializations: string[];
  capacity: {
    current_workload: number;
    max_capacity: number;
    availability: 'immediate' | 'within_week' | 'within_month';
  };
  response_time: string;
  previous_work_count: number;
}

const MerchantSelectionPage: React.FC = () => {
  const { requestId } = useParams();
  const navigate = useNavigate();
  const [selectedMerchants, setSelectedMerchants] = useState<string[]>([]);

  // Mock data for merchants
  const merchants: Merchant[] = [
    {
      id: 'M-001',
      name: 'AutoCare Services',
      rating: 4.8,
      location: 'Pretoria Central',
      distance: 5.2,
      specializations: ['Engine Repair', 'Brake Service', 'General Maintenance'],
      capacity: { current_workload: 3, max_capacity: 8, availability: 'immediate' },
      response_time: '2 hours',
      previous_work_count: 12
    },
    {
      id: 'M-002',
      name: 'Premium Motors',
      rating: 4.6,
      location: 'Sandton',
      distance: 15.8,
      specializations: ['Luxury Vehicles', 'Warranty Service', 'Diagnostics'],
      capacity: { current_workload: 5, max_capacity: 10, availability: 'within_week' },
      response_time: '1.5 hours',
      previous_work_count: 8
    },
    {
      id: 'M-003',
      name: 'Budget Auto Repair',
      rating: 4.2,
      location: 'Johannesburg South',
      distance: 25.3,
      specializations: ['Basic Maintenance', 'Tire Service', 'Oil Changes'],
      capacity: { current_workload: 7, max_capacity: 8, availability: 'within_month' },
      response_time: '4 hours',
      previous_work_count: 15
    },
    {
      id: 'M-004',
      name: 'Truck & Fleet Specialists',
      rating: 4.9,
      location: 'Kempton Park',
      distance: 18.7,
      specializations: ['Heavy Vehicles', 'Fleet Maintenance', 'Emergency Repairs'],
      capacity: { current_workload: 2, max_capacity: 6, availability: 'immediate' },
      response_time: '1.8 hours',
      previous_work_count: 6
    }
  ];

  const handleMerchantToggle = (merchantId: string) => {
    setSelectedMerchants(prev => 
      prev.includes(merchantId) 
        ? prev.filter(id => id !== merchantId)
        : [...prev, merchantId]
    );
  };

  const handleRequestQuotations = async () => {
    if (selectedMerchants.length === 0) return;
    
    try {
      console.log('Requesting quotations from merchants:', selectedMerchants);
      
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Navigate to quotation comparison page
      navigate(`/maintenance/requests/${requestId}/quotations`);
    } catch (error) {
      console.error('Failed to request quotations:', error);
    }
  };

  const getAvailabilityColor = (availability: string) => {
    switch (availability) {
      case 'immediate': return 'bg-green-100 text-green-800';
      case 'within_week': return 'bg-yellow-100 text-yellow-800';
      case 'within_month': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="container mx-auto px-3 sm:px-4 py-4 sm:py-6 space-y-4 sm:space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-start sm:justify-between gap-4">
        <div className="flex-1">
          <div className="flex items-center space-x-2 sm:space-x-3 mb-2">
            <button
              onClick={() => navigate(-1)}
              className="p-2 hover:bg-gray-100 rounded-lg flex-shrink-0"
            >
              <ArrowLeft className="h-4 w-4 sm:h-5 sm:w-5 text-gray-600" />
            </button>
            <h1 className="text-lg sm:text-xl lg:text-2xl font-bold text-gray-900">Select Merchants</h1>
          </div>
          <p className="text-sm sm:text-base text-gray-600">Request ID: {requestId}</p>
          <p className="text-xs sm:text-sm text-gray-500">Choose merchants to request quotations from based on their capacity and specialization</p>
        </div>
        <div className="text-left sm:text-right flex-shrink-0">
          <p className="text-xs sm:text-sm text-gray-500">Selected: {selectedMerchants.length}</p>
        </div>
      </div>

      {/* Merchant Cards */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6">
        {merchants.map((merchant) => (
          <div
            key={merchant.id}
            className={`bg-white rounded-lg border-2 p-6 cursor-pointer transition-all hover:shadow-md ${
              selectedMerchants.includes(merchant.id)
                ? 'border-blue-500 bg-blue-50'
                : 'border-gray-200 hover:border-gray-300'
            }`}
            onClick={() => handleMerchantToggle(merchant.id)}
          >
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center space-x-2">
                <Building2 className="h-5 w-5 text-gray-600" />
                <h3 className="text-lg font-semibold text-gray-900">{merchant.name}</h3>
              </div>
              {selectedMerchants.includes(merchant.id) && (
                <CheckCircle className="h-6 w-6 text-blue-600" />
              )}
            </div>

            <div className="space-y-3">
              <div className="flex items-center space-x-2">
                <Star className="h-4 w-4 text-yellow-500" />
                <span className="text-sm text-gray-600">{merchant.rating} rating ({merchant.previous_work_count} jobs)</span>
              </div>

              <div className="flex items-center space-x-2">
                <MapPin className="h-4 w-4 text-gray-400" />
                <span className="text-sm text-gray-600">{merchant.location} ({merchant.distance}km away)</span>
              </div>

              <div className="flex items-center space-x-2">
                <Clock className="h-4 w-4 text-gray-400" />
                <span className="text-sm text-gray-600">Responds in {merchant.response_time}</span>
              </div>

              <div className="text-sm">
                <p className="text-gray-600 mb-2">Specializations:</p>
                <div className="flex flex-wrap gap-1">
                  {merchant.specializations.map((spec) => (
                    <span key={spec} className="px-2 py-1 bg-gray-100 text-gray-700 rounded-full text-xs">
                      {spec}
                    </span>
                  ))}
                </div>
              </div>

              <div className="text-sm">
                <div className="flex justify-between items-center mb-1">
                  <span className="text-gray-600">Capacity:</span>
                  <span className="text-gray-900 font-medium">
                    {merchant.capacity.current_workload}/{merchant.capacity.max_capacity}
                  </span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2 mb-2">
                  <div 
                    className="bg-blue-600 h-2 rounded-full" 
                    style={{ width: `${(merchant.capacity.current_workload / merchant.capacity.max_capacity) * 100}%` }}
                  ></div>
                </div>
                <span className={`px-2 py-1 rounded-full text-xs font-medium ${getAvailabilityColor(merchant.capacity.availability)}`}>
                  {merchant.capacity.availability.replace('_', ' ')}
                </span>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Action Buttons */}
      <div className="flex justify-end space-x-3 pt-6 border-t border-gray-200">
        <button
          onClick={() => navigate(-1)}
          className="px-6 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
        >
          Back
        </button>
        <button
          onClick={handleRequestQuotations}
          disabled={selectedMerchants.length === 0}
          className={`px-6 py-2 rounded-lg flex items-center space-x-2 transition-colors ${
            selectedMerchants.length > 0
              ? 'bg-blue-600 hover:bg-blue-700 text-white'
              : 'bg-gray-300 text-gray-500 cursor-not-allowed'
          }`}
        >
          <Send className="h-4 w-4" />
          <span>Request Quotations ({selectedMerchants.length})</span>
        </button>
      </div>
    </div>
  );
};

export default MerchantSelectionPage;
