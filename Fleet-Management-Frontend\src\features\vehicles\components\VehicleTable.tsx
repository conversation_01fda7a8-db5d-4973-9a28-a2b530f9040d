
import React from 'react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  Eye, 
  Edit, 
  Settings, 
  AlertTriangle,
  ArrowUpDown 
} from 'lucide-react';
import { cn } from '@/lib/utils';
import type { Vehicle } from '@/types/vehicle';

interface VehicleTableProps {
  vehicles: Vehicle[];
  onView?: (id: string) => void;
  onEdit?: (id: string) => void;
  onScheduleService?: (id: string) => void;
  onSort?: (field: keyof Vehicle) => void;
  sortField?: keyof Vehicle;
  sortDirection?: 'asc' | 'desc';
  className?: string;
}

export const VehicleTable: React.FC<VehicleTableProps> = ({
  vehicles,
  onView,
  onEdit,
  onScheduleService,
  onSort,
  sortField,
  sortDirection,
  className
}) => {
  const getStatusColor = (status: Vehicle['status']) => {
    switch (status) {
      case 'active':
        return 'bg-green-100 text-green-800';
      case 'maintenance':
        return 'bg-yellow-100 text-yellow-800';
      case 'retired':
        return 'bg-gray-100 text-gray-800';
      case 'accident':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const isServiceDue = (vehicle: Vehicle) => {
    if (!vehicle.nextServiceDate) return false;
    const nextService = new Date(vehicle.nextServiceDate);
    const today = new Date();
    const daysUntilService = Math.ceil((nextService.getTime() - today.getTime()) / (1000 * 60 * 60 * 24));
    return daysUntilService <= 30;
  };

  const SortableHeader: React.FC<{ field: keyof Vehicle; children: React.ReactNode }> = ({ field, children }) => (
    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
      {onSort ? (
        <button
          onClick={() => onSort(field)}
          className="flex items-center gap-1 hover:text-gray-700"
        >
          {children}
          <ArrowUpDown className="h-3 w-3" />
        </button>
      ) : (
        children
      )}
    </th>
  );

  return (
    <div className={cn("w-full overflow-x-auto", className)}>
      <table className="w-full min-w-full divide-y divide-gray-200">
        <thead className="bg-gray-50">
          <tr>
            <SortableHeader field="registrationNumber">Registration</SortableHeader>
            <SortableHeader field="make">Vehicle</SortableHeader>
            <SortableHeader field="category">Category</SortableHeader>
            <SortableHeader field="department">Department</SortableHeader>
            <SortableHeader field="status">Status</SortableHeader>
            <SortableHeader field="mileage">Mileage</SortableHeader>
            <SortableHeader field="assignedDriver">Driver</SortableHeader>
            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Service
            </th>
            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Actions
            </th>
          </tr>
        </thead>
        <tbody className="bg-white divide-y divide-gray-200">
          {vehicles.map((vehicle) => (
            <tr key={vehicle.id} className="hover:bg-gray-50">
              <td className="px-6 py-4 whitespace-nowrap">
                <div className="font-medium text-gray-900">
                  {vehicle.registrationNumber}
                </div>
              </td>
              <td className="px-6 py-4 whitespace-nowrap">
                <div className="text-sm text-gray-900">
                  {vehicle.make} {vehicle.model}
                </div>
                <div className="text-sm text-gray-500">
                  {vehicle.year} • {vehicle.fuelType}
                </div>
              </td>
              <td className="px-6 py-4 whitespace-nowrap">
                <span className="text-sm text-gray-900 capitalize">
                  {vehicle.category}
                </span>
              </td>
              <td className="px-6 py-4 whitespace-nowrap">
                <span className="text-sm text-gray-900">
                  {vehicle.department}
                </span>
              </td>
              <td className="px-6 py-4 whitespace-nowrap">
                <Badge className={getStatusColor(vehicle.status)}>
                  {vehicle.status}
                </Badge>
              </td>
              <td className="px-6 py-4 whitespace-nowrap">
                <span className="text-sm text-gray-900">
                  {vehicle.mileage.toLocaleString()} km
                </span>
              </td>
              <td className="px-6 py-4 whitespace-nowrap">
                <span className="text-sm text-gray-900">
                  {vehicle.assignedDriver || 'Unassigned'}
                </span>
              </td>
              <td className="px-6 py-4 whitespace-nowrap">
                <div className="flex items-center gap-2">
                  {vehicle.nextServiceDate && (
                    <span className="text-sm text-gray-600">
                      {new Date(vehicle.nextServiceDate).toLocaleDateString()}
                    </span>
                  )}
                  {isServiceDue(vehicle) && (
                    <AlertTriangle className="h-4 w-4 text-yellow-500" />
                  )}
                </div>
              </td>
              <td className="px-6 py-4 whitespace-nowrap">
                <div className="flex gap-2">
                  {onView && (
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => onView(vehicle.id)}
                    >
                      <Eye className="h-4 w-4" />
                    </Button>
                  )}
                  {onEdit && (
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => onEdit(vehicle.id)}
                    >
                      <Edit className="h-4 w-4" />
                    </Button>
                  )}
                  {onScheduleService && (
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => onScheduleService(vehicle.id)}
                    >
                      <Settings className="h-4 w-4" />
                    </Button>
                  )}
                </div>
              </td>
            </tr>
          ))}
        </tbody>
      </table>
      
      {vehicles.length === 0 && (
        <div className="text-center py-12">
          <p className="text-gray-500">No vehicles found</p>
        </div>
      )}
    </div>
  );
};



