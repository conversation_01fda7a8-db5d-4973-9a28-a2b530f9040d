import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { 
  FileText, 
  Send, 
  CheckCircle, 
  AlertTriangle,
  Calculator,
  CreditCard,
  Upload,
  Trash2,
  ArrowLeft,
  Clock,
  Shield
} from 'lucide-react';

interface InvoiceItem {
  id: string;
  description: string;
  quantity: number;
  unitPrice: number;
  total: number;
  type: 'labor' | 'parts';
  completed: boolean;
}

interface CompletedJob {
  jobCardId: string;
  workOrderId: string;
  vehicle: {
    registration: string;
    make: string;
    model: string;
  };
  customer: {
    department: string;
    contactPerson: string;
    billingAddress: string;
    taxNumber: string;
  };
  completedDate: string;
  actualHours: number;
  estimatedHours: number;
  approvedItems: InvoiceItem[];
  additionalWork?: InvoiceItem[];
}

interface SupportingDocument {
  id: string;
  name: string;
  type: string;
  size: number;
  required: boolean;
  uploaded: boolean;
}

const InvoiceSubmissionPage: React.FC = () => {
  const navigate = useNavigate();
  const [jobCardId] = useState('JC-2025-001'); // From URL params
  const [invoiceNumber, setInvoiceNumber] = useState('INV-2025-001');
  const [invoiceDate, setInvoiceDate] = useState(new Date().toISOString().split('T')[0]);
  const [paymentTerms, setPaymentTerms] = useState('30');
  const [bankingDetails, setBankingDetails] = useState({
    accountName: 'AutoCare Services (Pty) Ltd',
    bank: 'Standard Bank',
    accountNumber: '*********',
    branchCode: '051001',
    accountType: 'Business Cheque'
  });
  const [additionalCharges, setAdditionalCharges] = useState<Array<{id: string, description: string, amount: number}>>([]);
  const [notes, setNotes] = useState('');
  const [complianceChecked, setComplianceChecked] = useState(false);
  const [taxComplianceChecked, setTaxComplianceChecked] = useState(false);

  // Mock completed job data
  const completedJob: CompletedJob = {
    jobCardId: 'JC-2025-001',
    workOrderId: 'WO-2025-001',
    vehicle: {
      registration: 'GP123ABC',
      make: 'Toyota',
      model: 'Hilux'
    },
    customer: {
      department: 'Department of Health',
      contactPerson: 'Dr. Sarah Johnson',
      billingAddress: '123 Government Ave, Pretoria Central, 0001',
      taxNumber: '**********'
    },
    completedDate: '2025-01-15T16:30:00Z',
    actualHours: 4.5,
    estimatedHours: 4,
    approvedItems: [
      {
        id: '1',
        description: 'Brake pad replacement (front)',
        quantity: 2,
        unitPrice: 400,
        total: 800,
        type: 'labor',
        completed: true
      },
      {
        id: '2',
        description: 'Brake fluid change',
        quantity: 1,
        unitPrice: 350,
        total: 350,
        type: 'labor',
        completed: true
      },
      {
        id: '3',
        description: 'Brake pads (front set) - OEM',
        quantity: 1,
        unitPrice: 450,
        total: 450,
        type: 'parts',
        completed: true
      },
      {
        id: '4',
        description: 'Brake fluid (DOT 4) - 1L',
        quantity: 1,
        unitPrice: 120,
        total: 120,
        type: 'parts',
        completed: true
      }
    ],
    additionalWork: [
      {
        id: '5',
        description: 'Additional brake line inspection',
        quantity: 0.5,
        unitPrice: 400,
        total: 200,
        type: 'labor',
        completed: true
      }
    ]
  };

  const [supportingDocs, setSupportingDocs] = useState<SupportingDocument[]>([
    { id: '1', name: 'Completion Certificate', type: 'PDF', size: 0, required: true, uploaded: false },
    { id: '2', name: 'Parts Receipts', type: 'PDF', size: 0, required: true, uploaded: false },
    { id: '3', name: 'Quality Inspection Report', type: 'PDF', size: 0, required: true, uploaded: false },
    { id: '4', name: 'Warranty Certificate', type: 'PDF', size: 0, required: false, uploaded: false },
    { id: '5', name: 'Before/After Photos', type: 'Images', size: 0, required: false, uploaded: false }
  ]);

  const calculateTotals = () => {
    const approvedTotal = completedJob.approvedItems.reduce((sum, item) => sum + item.total, 0);
    const additionalTotal = completedJob.additionalWork?.reduce((sum, item) => sum + item.total, 0) || 0;
    const chargesTotal = additionalCharges.reduce((sum, charge) => sum + charge.amount, 0);
    const subtotal = approvedTotal + additionalTotal + chargesTotal;
    const vat = subtotal * 0.15; // 15% VAT
    const total = subtotal + vat;
    
    return { approvedTotal, additionalTotal, chargesTotal, subtotal, vat, total };
  };

  const { approvedTotal, additionalTotal, chargesTotal, subtotal, vat, total } = calculateTotals();

  const addAdditionalCharge = () => {
    const newCharge = {
      id: Date.now().toString(),
      description: '',
      amount: 0
    };
    setAdditionalCharges([...additionalCharges, newCharge]);
  };

  const updateAdditionalCharge = (id: string, field: 'description' | 'amount', value: string | number) => {
    setAdditionalCharges(charges => 
      charges.map(charge => 
        charge.id === id ? { ...charge, [field]: value } : charge
      )
    );
  };

  const removeAdditionalCharge = (id: string) => {
    setAdditionalCharges(charges => charges.filter(charge => charge.id !== id));
  };

  const handleFileUpload = (docId: string, event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      setSupportingDocs(docs => 
        docs.map(doc => 
          doc.id === docId 
            ? { ...doc, uploaded: true, size: file.size, name: file.name }
            : doc
        )
      );
    }
  };

  const removeUploadedFile = (docId: string) => {
    setSupportingDocs(docs => 
      docs.map(doc => 
        doc.id === docId 
          ? { ...doc, uploaded: false, size: 0 }
          : doc
      )
    );
  };

  const canSubmitInvoice = () => {
    const requiredDocsUploaded = supportingDocs
      .filter(doc => doc.required)
      .every(doc => doc.uploaded);
    
    return complianceChecked && taxComplianceChecked && requiredDocsUploaded;
  };

  const handleSubmitInvoice = () => {
    console.log('Submitting invoice:', {
      invoiceNumber,
      jobCardId: completedJob.jobCardId,
      totals: { subtotal, vat, total },
      bankingDetails,
      supportingDocs: supportingDocs.filter(doc => doc.uploaded)
    });
    navigate('/merchant/invoices');
  };

  const handlePreviewInvoice = () => {
    console.log('Opening invoice preview');
  };

  const handleDownloadDraft = () => {
    console.log('Downloading invoice draft');
  };

  return (
    <div className="container mx-auto px-6 py-8 space-y-6 max-w-4xl">
      {/* Header */}
      <div className="flex items-center justify-between mb-8">
        <div className="flex items-center space-x-4">
          <Button
            variant="outline"
            onClick={() => navigate('/merchant/invoices')}
            className="flex items-center space-x-2"
          >
            <ArrowLeft className="h-4 w-4" />
            <span>Back to Invoices</span>
          </Button>
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Submit Invoice</h1>
            <p className="text-gray-600">Review and submit invoice for payment</p>
          </div>
        </div>
      </div>

      {/* Job Completion Summary */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <CheckCircle className="h-5 w-5 mr-2 text-green-600" />
            Job Completion Summary
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div>
              <p className="text-sm font-medium text-gray-500">Vehicle</p>
              <p className="text-sm">{completedJob.vehicle.make} {completedJob.vehicle.model}</p>
              <p className="text-sm text-gray-600">{completedJob.vehicle.registration}</p>
            </div>
            <div>
              <p className="text-sm font-medium text-gray-500">Customer</p>
              <p className="text-sm">{completedJob.customer.department}</p>
              <p className="text-sm text-gray-600">{completedJob.customer.contactPerson}</p>
            </div>
            <div>
              <p className="text-sm font-medium text-gray-500">Completed</p>
              <p className="text-sm">{new Date(completedJob.completedDate).toLocaleDateString()}</p>
              <p className="text-sm text-gray-600">{new Date(completedJob.completedDate).toLocaleTimeString()}</p>
            </div>
            <div>
              <p className="text-sm font-medium text-gray-500">Duration</p>
              <p className="text-sm">{completedJob.actualHours}h actual</p>
              <p className="text-sm text-gray-600">{completedJob.estimatedHours}h estimated</p>
            </div>
          </div>
        </CardContent>
      </Card>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Invoice Details */}
        <div className="lg:col-span-2 space-y-6">
          {/* Invoice Information */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <FileText className="h-5 w-5 mr-2" />
                Invoice Information
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="invoice-number">Invoice Number</Label>
                  <Input
                    id="invoice-number"
                    value={invoiceNumber}
                    onChange={(e) => setInvoiceNumber(e.target.value)}
                    placeholder="INV-2025-001"
                  />
                </div>
                <div>
                  <Label htmlFor="invoice-date">Invoice Date</Label>
                  <Input
                    id="invoice-date"
                    type="date"
                    value={invoiceDate}
                    onChange={(e) => setInvoiceDate(e.target.value)}
                  />
                </div>
                <div>
                  <Label htmlFor="payment-terms">Payment Terms (days)</Label>
                  <Select value={paymentTerms} onValueChange={setPaymentTerms}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="7">7 days</SelectItem>
                      <SelectItem value="14">14 days</SelectItem>
                      <SelectItem value="30">30 days</SelectItem>
                      <SelectItem value="60">60 days</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div>
                  <Label>Due Date</Label>
                  <Input
                    value={new Date(Date.now() + parseInt(paymentTerms) * 24 * 60 * 60 * 1000).toLocaleDateString()}
                    disabled
                    className="bg-gray-50"
                  />
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Approved Work Items */}
          <Card>
            <CardHeader>
              <CardTitle>Approved Work Items</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="grid grid-cols-12 gap-2 text-sm font-medium text-gray-500 border-b pb-2">
                  <div className="col-span-6">Description</div>
                  <div className="col-span-2">Quantity</div>
                  <div className="col-span-2">Unit Price</div>
                  <div className="col-span-2">Total</div>
                </div>
                
                {completedJob.approvedItems.map((item) => (
                  <div key={item.id} className="grid grid-cols-12 gap-2 text-sm items-center">
                    <div className="col-span-6 flex items-center">
                      <CheckCircle className="h-4 w-4 mr-2 text-green-500" />
                      {item.description}
                      <Badge variant="outline" className="ml-2 text-xs">
                        {item.type}
                      </Badge>
                    </div>
                    <div className="col-span-2">{item.quantity}</div>
                    <div className="col-span-2">R{item.unitPrice.toFixed(2)}</div>
                    <div className="col-span-2">R{item.total.toFixed(2)}</div>
                  </div>
                ))}
                
                <div className="border-t pt-2">
                  <div className="flex justify-between text-sm font-medium">
                    <span>Approved Work Subtotal:</span>
                    <span>R{approvedTotal.toFixed(2)}</span>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Additional Work */}
          {completedJob.additionalWork && completedJob.additionalWork.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <AlertTriangle className="h-5 w-5 mr-2 text-orange-500" />
                  Additional Work Performed
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="bg-orange-50 border border-orange-200 rounded p-3">
                    <p className="text-sm text-orange-800">
                      Additional work was performed beyond the original scope. This requires customer approval before invoicing.
                    </p>
                  </div>
                  
                  <div className="space-y-2">
                    {completedJob.additionalWork.map((item) => (
                      <div key={item.id} className="grid grid-cols-12 gap-2 text-sm items-center">
                        <div className="col-span-6 flex items-center">
                          <Clock className="h-4 w-4 mr-2 text-orange-500" />
                          {item.description}
                          <Badge variant="outline" className="ml-2 text-xs">
                            {item.type}
                          </Badge>
                        </div>
                        <div className="col-span-2">{item.quantity}</div>
                        <div className="col-span-2">R{item.unitPrice.toFixed(2)}</div>
                        <div className="col-span-2">R{item.total.toFixed(2)}</div>
                      </div>
                    ))}
                  </div>
                  
                  <div className="border-t pt-2">
                    <div className="flex justify-between text-sm font-medium">
                      <span>Additional Work Subtotal:</span>
                      <span>R{additionalTotal.toFixed(2)}</span>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Additional Charges */}
          <Card>
            <CardHeader>
              <div className="flex justify-between items-center">
                <CardTitle>Additional Charges</CardTitle>
                <Button size="sm" variant="outline" onClick={addAdditionalCharge}>
                  <Calculator className="h-4 w-4 mr-1" />
                  Add Charge
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {additionalCharges.length === 0 ? (
                  <p className="text-sm text-gray-500 text-center py-4">
                    No additional charges. Click "Add Charge" to include delivery fees, disposal costs, etc.
                  </p>
                ) : (
                  <div className="space-y-2">
                    {additionalCharges.map((charge) => (
                      <div key={charge.id} className="grid grid-cols-12 gap-2 items-center">
                        <div className="col-span-8">
                          <Input
                            placeholder="Description (e.g., Delivery fee, Disposal cost)"
                            value={charge.description}
                            onChange={(e) => updateAdditionalCharge(charge.id, 'description', e.target.value)}
                          />
                        </div>
                        <div className="col-span-3">
                          <Input
                            type="number"
                            placeholder="Amount"
                            value={charge.amount}
                            onChange={(e) => updateAdditionalCharge(charge.id, 'amount', parseFloat(e.target.value) || 0)}
                          />
                        </div>
                        <div className="col-span-1">
                          <Button size="sm" variant="ghost" onClick={() => removeAdditionalCharge(charge.id)}>
                            <Trash2 className="h-4 w-4 text-red-500" />
                          </Button>
                        </div>
                      </div>
                    ))}
                    <div className="border-t pt-2">
                      <div className="flex justify-between text-sm font-medium">
                        <span>Additional Charges Total:</span>
                        <span>R{chargesTotal.toFixed(2)}</span>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>

          {/* Banking Details */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <CreditCard className="h-5 w-5 mr-2" />
                Banking Details
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="account-name">Account Name</Label>
                  <Input
                    id="account-name"
                    value={bankingDetails.accountName}
                    onChange={(e) => setBankingDetails({...bankingDetails, accountName: e.target.value})}
                  />
                </div>
                <div>
                  <Label htmlFor="bank">Bank</Label>
                  <Input
                    id="bank"
                    value={bankingDetails.bank}
                    onChange={(e) => setBankingDetails({...bankingDetails, bank: e.target.value})}
                  />
                </div>
                <div>
                  <Label htmlFor="account-number">Account Number</Label>
                  <Input
                    id="account-number"
                    value={bankingDetails.accountNumber}
                    onChange={(e) => setBankingDetails({...bankingDetails, accountNumber: e.target.value})}
                  />
                </div>
                <div>
                  <Label htmlFor="branch-code">Branch Code</Label>
                  <Input
                    id="branch-code"
                    value={bankingDetails.branchCode}
                    onChange={(e) => setBankingDetails({...bankingDetails, branchCode: e.target.value})}
                  />
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Supporting Documents */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Upload className="h-5 w-5 mr-2" />
                Supporting Documents
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {supportingDocs.map((doc) => (
                  <div key={doc.id} className="flex items-center justify-between p-3 border rounded-lg">
                    <div className="flex items-center">
                      <FileText className="h-5 w-5 mr-3 text-gray-500" />
                      <div>
                        <p className="text-sm font-medium">{doc.name}</p>
                        <p className="text-xs text-gray-500">
                          {doc.required ? 'Required' : 'Optional'} • {doc.type}
                          {doc.uploaded && ` • ${(doc.size / 1024).toFixed(1)} KB`}
                        </p>
                      </div>
                      {doc.required && (
                        <Badge variant="destructive" className="ml-2 text-xs">Required</Badge>
                      )}
                    </div>
                    <div className="flex items-center space-x-2">
                      {doc.uploaded ? (
                        <>
                          <CheckCircle className="h-4 w-4 text-green-500" />
                          <Button size="sm" variant="ghost" onClick={() => removeUploadedFile(doc.id)}>
                            <Trash2 className="h-4 w-4 text-red-500" />
                          </Button>
                        </>
                      ) : (
                        <>
                          <input
                            type="file"
                            onChange={(e) => handleFileUpload(doc.id, e)}
                            className="hidden"
                            id={`file-${doc.id}`}
                            accept=".pdf,.doc,.docx,.jpg,.jpeg,.png"
                          />
                          <label htmlFor={`file-${doc.id}`}>
                            <Button size="sm" variant="outline" asChild>
                              <span>Upload</span>
                            </Button>
                          </label>
                        </>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Additional Notes */}
          <Card>
            <CardHeader>
              <CardTitle>Additional Notes</CardTitle>
            </CardHeader>
            <CardContent>
              <Textarea
                placeholder="Any additional information for the invoice..."
                value={notes}
                onChange={(e) => setNotes(e.target.value)}
                rows={3}
              />
            </CardContent>
          </Card>
        </div>

        {/* Invoice Summary & Compliance */}
        <div className="space-y-6">
          {/* Invoice Total */}
          <Card>
            <CardHeader>
              <CardTitle>Invoice Summary</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span>Approved Work:</span>
                  <span>R{approvedTotal.toFixed(2)}</span>
                </div>
                {additionalTotal > 0 && (
                  <div className="flex justify-between text-sm">
                    <span>Additional Work:</span>
                    <span>R{additionalTotal.toFixed(2)}</span>
                  </div>
                )}
                {chargesTotal > 0 && (
                  <div className="flex justify-between text-sm">
                    <span>Additional Charges:</span>
                    <span>R{chargesTotal.toFixed(2)}</span>
                  </div>
                )}
                <div className="flex justify-between text-sm border-t pt-2">
                  <span>Subtotal:</span>
                  <span>R{subtotal.toFixed(2)}</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span>VAT (15%):</span>
                  <span>R{vat.toFixed(2)}</span>
                </div>
                <div className="flex justify-between text-lg font-bold border-t pt-2">
                  <span>Total:</span>
                  <span>R{total.toFixed(2)}</span>
                </div>
              </div>
              
              <div className="text-sm text-gray-600">
                <p>Payment Terms: {paymentTerms} days</p>
                <p>Due Date: {new Date(Date.now() + parseInt(paymentTerms) * 24 * 60 * 60 * 1000).toLocaleDateString()}</p>
              </div>
            </CardContent>
          </Card>

          {/* Compliance Checklist */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Shield className="h-5 w-5 mr-2" />
                Compliance Checklist
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-3">
                <div className="flex items-start space-x-2">
                  <Checkbox
                    id="compliance-check"
                    checked={complianceChecked}
                    onCheckedChange={(checked) => setComplianceChecked(checked === true)}
                  />
                  <label htmlFor="compliance-check" className="text-sm">
                    I confirm that all work was completed according to specifications and quality standards.
                  </label>
                </div>
                
                <div className="flex items-start space-x-2">
                  <Checkbox
                    id="tax-compliance"
                    checked={taxComplianceChecked}
                    onCheckedChange={(checked) => setTaxComplianceChecked(checked === true)}
                  />
                  <label htmlFor="tax-compliance" className="text-sm">
                    I confirm tax compliance and that all VAT calculations are correct.
                  </label>
                </div>
              </div>
              
              <div className="space-y-2">
                <p className="text-sm font-medium">Required Documents:</p>
                {supportingDocs.filter(doc => doc.required).map((doc) => (
                  <div key={doc.id} className="flex items-center text-sm">
                    {doc.uploaded ? (
                      <CheckCircle className="h-4 w-4 mr-2 text-green-500" />
                    ) : (
                      <AlertTriangle className="h-4 w-4 mr-2 text-red-500" />
                    )}
                    <span className={doc.uploaded ? 'text-green-700' : 'text-red-700'}>
                      {doc.name}
                    </span>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Submit Button */}
          <Button 
            className="w-full" 
            size="lg"
            onClick={handleSubmitInvoice}
            disabled={!canSubmitInvoice()}
          >
            <Send className="h-4 w-4 mr-2" />
            Submit Invoice
          </Button>
          
          {!canSubmitInvoice() && (
            <p className="text-sm text-red-600 text-center">
              Complete compliance checklist and upload required documents to submit.
            </p>
          )}
        </div>
      </div>
    </div>
  );
};

export default InvoiceSubmissionPage;




