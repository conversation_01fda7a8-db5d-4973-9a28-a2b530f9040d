---
type: "manual"
---

# Database Operations - CRITICAL CONSTRAINTS

## ABSOLUTE PROHIBITIONS
- **NEVER RESET ANY DATABASE** - This is a government system with critical data
- **NEVER PURGE ANY TABLES FROM A DB WITHOUT CONSENT** - Data loss is unacceptable
- **IN PRISMA NO MATTER WHAT NEVER RESET ANY DATABASE** (Note: We don't use Prisma, but rule stands)

## Database Technology Stack
- **Primary Database**: PostgreSQL 15 on Cloud SQL
- **ORM**: SQLAlchemy Core (fine-grained SQL control required)
- **Document Storage**: Firestore for unstructured data
- **Cache**: Redis for session and application caching
- **Analytics**: BigQuery for reporting and analytics

## Database Development Rules
- Use database migrations for all schema changes
- Test migrations on development/staging before production
- Always backup before any structural changes
- Implement proper indexing strategies for performance
- Use read replicas for reporting workloads
- Maintain referential integrity through foreign keys
- Use check constraints for data validation

## Data Protection Requirements
- POPIA compliance mandatory for all personal data
- Encrypt sensitive data at rest and in transit
- Implement audit trails for all data modifications
- Use role-based access control for database access
- Regular backup verification and disaster recovery testing

## Query Optimization
- Use SQLAlchemy Core for complex queries requiring optimization
- Implement connection pooling (20 connections, 30 max overflow)
- Use async database operations throughout
- Monitor query performance and optimize slow queries
- Use materialized views for complex analytical queries
---
alwaysApply: true
---
