import React, { useState } from 'react';
import { use<PERSON><PERSON>, Controller } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { useNavigate } from 'react-router-dom';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  Building2, 
  User, 
  Mail,
  Phone,
  FileText,
  CheckCircle,
  ArrowLeft,
  AlertCircle,
  Settings
} from 'lucide-react';

// Validation schema for new customer profile
const customerProfileSchema = z.object({
  departmentName: z.string().min(2, 'Department name must be at least 2 characters'),
  departmentType: z.string().min(1, 'Please select department type'),
  contactPersonName: z.string().min(2, 'Contact person name is required'),
  email: z.string().email('Invalid email address'),
  phoneNumber: z.string().min(10, 'Phone number must be at least 10 digits'),
  slaLevel: z.string().min(1, 'Please select SLA level'),
  additionalNotes: z.string().optional(),
});

type CustomerProfileData = z.infer<typeof customerProfileSchema>;

// Department types for government entities
const departmentTypes = [
  'National Department',
  'Provincial Department', 
  'Municipal Department',
  'State-Owned Enterprise',
  'Public Entity',
  'Government Agency'
];

// SLA levels with descriptions
const slaLevels = [
  { value: 'standard', label: 'Standard', description: '5-7 business days response' },
  { value: 'priority', label: 'Priority', description: '2-3 business days response' },
  { value: 'custom', label: 'Custom', description: 'Tailored service agreement' }
];

const NewCustomerProfileForm: React.FC = () => {
  const navigate = useNavigate();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [success, setSuccess] = useState(false);

  const {
    register,
    handleSubmit,
    control,
    formState: { errors },
    reset,
  } = useForm<CustomerProfileData>({
    resolver: zodResolver(customerProfileSchema),
    mode: 'onChange',
  });

  const onSubmit = async (data: CustomerProfileData) => {
    setIsSubmitting(true);
    try {
      console.log('Creating new customer profile:', data);
      
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      setSuccess(true);
      
      // Auto-redirect after success
      setTimeout(() => {
        navigate('/fleet/customers/fleet-upload', {
          state: { customerData: data }
        });
      }, 2000);
      
    } catch (error) {
      console.error('Failed to create customer profile:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleCancel = () => {
    reset();
    navigate('/dashboard');
  };

  if (success) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <Card className="w-full max-w-md">
          <CardContent className="pt-6">
            <div className="text-center space-y-4">
              <div className="mx-auto w-16 h-16 bg-green-100 rounded-full flex items-center justify-center">
                <CheckCircle className="h-8 w-8 text-green-600" />
              </div>
              <div>
                <h3 className="text-lg font-semibold text-gray-900">Profile Created Successfully!</h3>
                <p className="text-sm text-gray-600 mt-2">
                  Redirecting to organizational hierarchy setup...
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <Building2 className="h-8 w-8 text-blue-600" />
              <div>
                <h1 className="text-xl font-bold text-gray-900">Fleet Management Portal</h1>
                <p className="text-sm text-gray-600">Customer Onboarding - New Profile</p>
              </div>
            </div>
            <Button variant="outline" onClick={handleCancel}>
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Dashboard
            </Button>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-2xl mx-auto">
          <Card className="w-full">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <User className="h-5 w-5 text-blue-600" />
                New Customer Profile Form
              </CardTitle>
              <p className="text-sm text-gray-600">
                Create a new customer profile for government department onboarding
              </p>
            </CardHeader>
            <CardContent>
              <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
                {/* Department Information */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-2">
                    <Label htmlFor="departmentName">Department Name *</Label>
                    <Input
                      id="departmentName"
                      {...register('departmentName')}
                      placeholder="e.g., Department of Transport"
                      className={errors.departmentName ? 'border-red-500' : ''}
                    />
                    {errors.departmentName && (
                      <p className="text-sm text-red-600 flex items-center gap-1">
                        <AlertCircle className="h-4 w-4" />
                        {errors.departmentName.message}
                      </p>
                    )}
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="departmentType">Department Type *</Label>
                    <Controller
                      name="departmentType"
                      control={control}
                      render={({ field }) => (
                        <Select onValueChange={field.onChange} value={field.value}>
                          <SelectTrigger className={errors.departmentType ? 'border-red-500' : ''}>
                            <SelectValue placeholder="Select department type" />
                          </SelectTrigger>
                          <SelectContent>
                            {departmentTypes.map((type) => (
                              <SelectItem key={type} value={type}>
                                {type}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      )}
                    />
                    {errors.departmentType && (
                      <p className="text-sm text-red-600 flex items-center gap-1">
                        <AlertCircle className="h-4 w-4" />
                        {errors.departmentType.message}
                      </p>
                    )}
                  </div>
                </div>

                {/* Contact Information */}
                <div className="space-y-2">
                  <Label htmlFor="contactPersonName">Contact Person Name *</Label>
                  <Input
                    id="contactPersonName"
                    {...register('contactPersonName')}
                    placeholder="e.g., John Smith"
                    className={errors.contactPersonName ? 'border-red-500' : ''}
                  />
                  {errors.contactPersonName && (
                    <p className="text-sm text-red-600 flex items-center gap-1">
                      <AlertCircle className="h-4 w-4" />
                      {errors.contactPersonName.message}
                    </p>
                  )}
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-2">
                    <Label htmlFor="email">Email Address *</Label>
                    <div className="relative">
                      <Mail className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                      <Input
                        id="email"
                        type="email"
                        {...register('email')}
                        placeholder="<EMAIL>"
                        className={`pl-10 ${errors.email ? 'border-red-500' : ''}`}
                      />
                    </div>
                    {errors.email && (
                      <p className="text-sm text-red-600 flex items-center gap-1">
                        <AlertCircle className="h-4 w-4" />
                        {errors.email.message}
                      </p>
                    )}
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="phoneNumber">Phone Number *</Label>
                    <div className="relative">
                      <Phone className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                      <Input
                        id="phoneNumber"
                        type="tel"
                        {...register('phoneNumber')}
                        placeholder="+27 12 345 6789"
                        className={`pl-10 ${errors.phoneNumber ? 'border-red-500' : ''}`}
                      />
                    </div>
                    {errors.phoneNumber && (
                      <p className="text-sm text-red-600 flex items-center gap-1">
                        <AlertCircle className="h-4 w-4" />
                        {errors.phoneNumber.message}
                      </p>
                    )}
                  </div>
                </div>

                {/* SLA Level Selection */}
                <div className="space-y-2">
                  <Label htmlFor="slaLevel">SLA Level *</Label>
                  <Controller
                    name="slaLevel"
                    control={control}
                    render={({ field }) => (
                      <Select onValueChange={field.onChange} value={field.value}>
                        <SelectTrigger className={errors.slaLevel ? 'border-red-500' : ''}>
                          <SelectValue placeholder="Select SLA level" />
                        </SelectTrigger>
                        <SelectContent>
                          {slaLevels.map((sla) => (
                            <SelectItem key={sla.value} value={sla.value}>
                              <div className="flex flex-col">
                                <span className="font-medium">{sla.label}</span>
                                <span className="text-xs text-gray-500">{sla.description}</span>
                              </div>
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    )}
                  />
                  {errors.slaLevel && (
                    <p className="text-sm text-red-600 flex items-center gap-1">
                      <AlertCircle className="h-4 w-4" />
                      {errors.slaLevel.message}
                    </p>
                  )}
                </div>

                {/* Additional Notes */}
                <div className="space-y-2">
                  <Label htmlFor="additionalNotes">Additional Notes</Label>
                  <Textarea
                    id="additionalNotes"
                    {...register('additionalNotes')}
                    placeholder="Any additional information about the department or special requirements..."
                    rows={4}
                    className="resize-none"
                  />
                  <p className="text-xs text-gray-500">
                    Optional: Include any special requirements, existing systems, or important context
                  </p>
                </div>

                {/* Information Alert */}
                <Alert>
                  <Settings className="h-4 w-4" />
                  <AlertDescription>
                    This profile will be used to configure the department's fleet management system.
                    You can modify these details later in the customer management section.
                  </AlertDescription>
                </Alert>

                {/* Form Actions */}
                <div className="flex justify-end gap-4 pt-6 border-t">
                  <Button
                    type="button"
                    variant="outline"
                    onClick={handleCancel}
                    disabled={isSubmitting}
                  >
                    Cancel
                  </Button>
                  <Button
                    type="submit"
                    disabled={isSubmitting}
                    className="min-w-[120px]"
                  >
                    {isSubmitting ? (
                      <div className="flex items-center gap-2">
                        <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                        Creating...
                      </div>
                    ) : (
                      'Create Profile'
                    )}
                  </Button>
                </div>
              </form>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
};

export default NewCustomerProfileForm;
