import { api } from '../api';
import type { PaginatedResponse } from '../../types/api';

export interface Budget {
  id: string;
  department: string;
  category: 'maintenance' | 'fuel' | 'insurance' | 'licensing' | 'other';
  allocated_amount: number;
  spent_amount: number;
  remaining_amount: number;
  period_start: string;
  period_end: string;
  status: 'active' | 'exceeded' | 'completed';
  created_at: string;
  updated_at: string;
}

export interface PaymentRecord {
  id: string;
  invoice_id: string;
  vendor_id: string;
  vendor_name: string;
  amount: number;
  payment_method: 'eft' | 'bank_transfer' | 'cheque';
  reference_number: string;
  payment_date: string;
  status: 'pending' | 'processed' | 'failed' | 'cancelled';
  processed_by: string;
  notes?: string;
  created_at: string;
}

export interface FinancialSummary {
  total_budget: number;
  total_spent: number;
  total_pending: number;
  budget_utilization: number;
  monthly_spending: Array<{
    month: string;
    amount: number;
  }>;
  spending_by_category: Record<string, number>;
  top_vendors: Array<{
    vendor_name: string;
    amount: number;
  }>;
}

export const financeApi = api.injectEndpoints({
  endpoints: (builder) => ({
    // Budget Management
    getBudgets: builder.query<PaginatedResponse<Budget>, {
      page?: number;
      limit?: number;
      department?: string;
      category?: string;
      status?: string;
    }>({
      query: (filters = {}) => {
        const params = new URLSearchParams();
        Object.entries(filters).forEach(([key, value]) => {
          if (value) params.append(key, String(value));
        });
        return {
          url: '/finance/budgets',
          params: Object.fromEntries(params),
        };
      },
      providesTags: [{ type: 'Budget', id: 'LIST' }],
    }),

    // Create budget
    createBudget: builder.mutation<Budget, {
      department: string;
      category: string;
      allocated_amount: number;
      period_start: string;
      period_end: string;
    }>({
      query: (data) => ({
        url: '/finance/budgets',
        method: 'POST',
        body: data,
      }),
      invalidatesTags: [{ type: 'Budget', id: 'LIST' }],
    }),

    // Update budget
    updateBudget: builder.mutation<Budget, {
      id: string;
      allocated_amount?: number;
      period_start?: string;
      period_end?: string;
    }>({
      query: ({ id, ...data }) => ({
        url: `/finance/budgets/${id}`,
        method: 'PUT',
        body: data,
      }),
      invalidatesTags: [{ type: 'Budget', id: 'LIST' }],
    }),

    // Payment Processing
    getPaymentRecords: builder.query<PaginatedResponse<PaymentRecord>, {
      page?: number;
      limit?: number;
      status?: string;
      vendor_id?: string;
      date_from?: string;
      date_to?: string;
    }>({
      query: (filters = {}) => {
        const params = new URLSearchParams();
        Object.entries(filters).forEach(([key, value]) => {
          if (value) params.append(key, String(value));
        });
        return {
          url: '/finance/payments',
          params: Object.fromEntries(params),
        };
      },
      providesTags: [{ type: 'Payment', id: 'LIST' }],
    }),

    // Process payment
    processPayment: builder.mutation<PaymentRecord, {
      invoice_id: string;
      amount: number;
      payment_method: string;
      reference_number: string;
      notes?: string;
    }>({
      query: (data) => ({
        url: '/finance/payments/process',
        method: 'POST',
        body: data,
      }),
      invalidatesTags: [
        { type: 'Payment', id: 'LIST' },
        { type: 'FinancialSummary', id: 'LIST' }
      ],
    }),

    // Bulk payment processing
    processBulkPayments: builder.mutation<{ processed: number; failed: number }, {
      invoice_ids: string[];
      payment_method: string;
      notes?: string;
    }>({
      query: (data) => ({
        url: '/finance/payments/bulk',
        method: 'POST',
        body: data,
      }),
      invalidatesTags: [
        { type: 'Payment', id: 'LIST' },
        { type: 'FinancialSummary', id: 'LIST' }
      ],
    }),

    // Financial Reports
    getFinancialSummary: builder.query<FinancialSummary, {
      date_from: string;
      date_to: string;
      department?: string;
    }>({
      query: (params) => ({
        url: '/finance/reports/summary',
        params,
      }),
      providesTags: [{ type: 'FinancialSummary', id: 'LIST' }],
    }),

    // Budget vs Actual Report
    getBudgetVsActualReport: builder.query<Array<{
      department: string;
      category: string;
      budgeted: number;
      actual: number;
      variance: number;
      variance_percentage: number;
    }>, { period_start: string; period_end: string }>({
      query: (params) => ({
        url: '/finance/reports/budget-vs-actual',
        params,
      }),
    }),

    // Cash flow projection
    getCashFlowProjection: builder.query<Array<{
      month: string;
      projected_income: number;
      projected_expenses: number;
      net_cash_flow: number;
    }>, { months_ahead: number }>({
      query: (params) => ({
        url: '/finance/reports/cash-flow',
        params,
      }),
    }),

    // Vendor payment analysis
    getVendorPaymentAnalysis: builder.query<Array<{
      vendor_id: string;
      vendor_name: string;
      total_invoiced: number;
      total_paid: number;
      outstanding_amount: number;
      average_payment_days: number;
      payment_terms_compliance: number;
    }>, { date_from: string; date_to: string }>({
      query: (params) => ({
        url: '/finance/reports/vendor-payments',
        params,
      }),
    }),

    // Export financial data
    exportFinancialData: builder.mutation<{ download_url: string }, {
      report_type: 'budget' | 'payments' | 'summary' | 'vendor_analysis';
      format: 'excel' | 'csv' | 'pdf';
      filters: Record<string, any>;
    }>({
      query: (data) => ({
        url: '/finance/export',
        method: 'POST',
        body: data,
      }),
    }),
  }),
});

export const {
  useGetBudgetsQuery,
  useCreateBudgetMutation,
  useUpdateBudgetMutation,
  useGetPaymentRecordsQuery,
  useProcessPaymentMutation,
  useProcessBulkPaymentsMutation,
  useGetFinancialSummaryQuery,
  useGetBudgetVsActualReportQuery,
  useGetCashFlowProjectionQuery,
  useGetVendorPaymentAnalysisQuery,
  useExportFinancialDataMutation,
} = financeApi;