import React from 'react';
import ConfirmDialog from '../ui/ConfirmDialog';

interface LogoutConfirmModalProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => void;
  isLoading?: boolean;
}

const LogoutConfirmModal: React.FC<LogoutConfirmModalProps> = ({
  isOpen,
  onClose,
  onConfirm,
  isLoading = false
}) => {
  return (
    <ConfirmDialog
      isOpen={isOpen}
      onClose={onClose}
      onConfirm={onConfirm}
      title="Confirm Logout"
      message="Are you sure you want to log out? Any unsaved changes will be lost."
      type="warning"
      confirmText="Logout"
      cancelText="Stay Logged In"
      isLoading={isLoading}
      icon="logout"
    />
  );
};

export default LogoutConfirmModal;