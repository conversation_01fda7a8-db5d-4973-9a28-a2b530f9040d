# RT46-2026 Fleet Management System - Technical Specifications

## Document Information
- **Version:** 1.1
- **Date:** July 15 2025
- **Author:** <PERSON>
- **Status:** Active Technical Specification
- **Classification:** Technical Specification

---

## 1. Executive Summary

This document outlines the technical design specification for the RT46-2026 Vehicle Fleet Management System. It provides detailed architectural decisions, system design patterns, data models, API specifications, and technical implementation guidelines for the development team.

### 1.1 Scope
This specification covers:
- System architecture and design patterns
- Technology stack implementation details
- Database design and data models
- API design and integration patterns
- Security architecture and compliance
- Performance optimization strategies
- Deployment and infrastructure design

---

## 2. System Architecture

### 2.1 High-Level Architecture

The system architecture is designed around a modular, microservices-oriented approach, utilizing Google Cloud Platform and a specific set of open-source technologies to meet the performance, scalability, and security requirements of the RT46-2026 contract.

```
┌─────────────────────────────────────────────────────────────────────────────────────────┐
│                                   PRESENTATION LAYER                                     │
├──────────────────────────┬───────────────────────────┬──────────────────────────────────┤
│   Web Portal (React.js)  │  Mobile App (React Native)│   Admin <PERSON> (Directus on GKE)     │
│  - Fleet Management      │  - Vehicle Inspections    │   - Vehicle Registry             │
│  - Work Orders           │  - Issue Reporting        │   - Merchant Registry            │
│  - Analytics Dashboard   │  - Offline Capability     │   - User Management              │
└───────────┬──────────────┴────────────┬──────────────┴───────────────────┬──────────────┘
            │                           │                                  │
┌───────────▼───────────────────────────▼──────────────────────────────────▼──────────────┐
│                        API GATEWAY (APIGEE) & SECURITY (AUTHELIA)                        │
├──────────────────────────────────────────────────────────────────────────────────────────┤
│ • API Routing & Management (Apigee)                                                      │
│ • Authentication & Authorization (Authelia - OIDC, RBAC, MFA)                            │
│ • Security Policies, Rate Limiting, Threat Detection                                     │
└────────────────────────────────────┬─────────────────────────────────────────────────────┘
                                     │
                                     ▼
┌──────────────────────────────────────────────────────────────────────────────────────────┐
│                             CORE MODULES & MICROSERVICES                                 │
├──────────────────────────────────────────────────────────────────────────────────────────┤
│                                                                                          │
│  • Work Allocation Engine (Cloud Workflows, Airflow on GKE)                              │
│  • Maintenance & Repair Orders (Custom Backend on Cloud Run, Tryton on GKE)              │
│  • Orchestration & Workflow (Conductor)                                                  │
│  • Custom Business Logic (Python/FastAPI on Cloud Run)                                   │
│                                                                                          │
└────────────────────────────────────┬─────────────────────────────────────────────────────┘
                                     │
                                     ▼
┌──────────────────────────────────────────────────────────────────────────────────────────┐
│                     AI, ANALYTICS & DATA INTELLIGENCE LAYER (Vertex AI)                    │
├──────────────────────────────────────────────────────────────────────────────────────────┤
│                                                                                          │
│  • Fraud & Anomaly Detection (Vertex AI, BigQuery ML, Wazuh, pyod)                       │
│  • Search & Geo-Matching (Vertex AI Matching Engine, Meilisearch, PostGIS)               │
│  • Document & Image AI (Document AI, Vision AI, Tesseract)                               │
│  • Analytics & Reporting (Looker, BigQuery BI Engine)                                    │
│  • Audit Logging Pipeline (Cloud Logging -> Pub/Sub -> BigQuery)                         │
│                                                                                          │
└────────────────────────────────────┬─────────────────────────────────────────────────────┘
                                     │
                                     ▼
┌──────────────────────────────────────────────────────────────────────────────────────────┐
│                                     DATA LAYER                                         │
├─────────────────────────┬──────────────────────────┬──────────────────┬──────────────────┤
│  Cloud SQL (PostgreSQL) │        Firestore         │      BigQuery    │  Cloud Storage   │
│  - Transactional Data   │  - Documents, Metadata   │  - Analytics     │  - Files, Images │
│  - Relational Integrity │  - App Configuration     │  - Data Warehouse│  - Backups       │
└─────────────────────────┴──────────────────────────┴──────────────────┴──────────────────┘
```

### 2.2 Architectural Patterns

#### 2.2.1 Microservices Architecture
- **Domain-Driven Design (DDD)**: Services organized around business domains
- **Single Responsibility**: Each service manages one business capability
- **Database per Service**: Independent data stores for each service
- **API-First**: All communication through well-defined APIs

#### 2.2.2 Event-Driven Architecture
- **Event Sourcing**: Critical events stored for audit and replay
- **CQRS (Command Query Responsibility Segregation)**: Separate read/write models
- **Pub/Sub Messaging**: Asynchronous communication between services
- **Saga Pattern**: Distributed transaction management

#### 2.2.3 Layered Architecture
- **Presentation Layer**: User interfaces and API endpoints
- **Business Logic Layer**: Core business rules and workflows
- **Data Access Layer**: Database operations and data persistence
- **Infrastructure Layer**: External integrations and technical concerns

---

## 3. Technology Stack Specification

### 3.1 Frontend Technologies

#### 3.1.1 Web Application (React.js) & Admin UI (Directus)
- **Primary Web Portal:** Custom-built application using **React 18+** with **TypeScript**.
- **Admin UI:** **Directus** running on GKE will provide a ready-made, user-friendly interface for managing the vehicle and merchant registry, significantly reducing development time for administrative tasks.
- **State Management:** Redux Toolkit.
- **Styling:** Material-UI (MUI).
- **Build Tool:** Vite.

#### 3.1.2 Mobile Application (React Native)
- **Framework:** **React Native** for cross-platform development (iOS & Android).
- **Focus:** Offline-first capabilities for vehicle inspections and issue reporting in the field.
- **Monitoring:** **Firebase Crashlytics** and **Firebase Performance Monitoring** for app health.

### 3.2 Backend Technologies

#### 3.2.1 Core Services & Authentication
- **Framework:** **Python 3.11+** with **FastAPI** for high-performance, asynchronous APIs.
- **Authentication:** **Authelia** will serve as the centralized, self-hosted OIDC provider, handling user authentication, role-based access control (RBAC), and multi-factor authentication (MFA) before requests reach the core services.

#### 3.2.2 Workflow, Search, and Data Processing
- **Work Allocation & Scheduling:** A hybrid approach using **Google Cloud Workflows** for simple, event-driven tasks and **Apache Airflow** (on GKE) for more complex, scheduled batch processes.
- **Orchestration:** **Conductor** will be used to manage and visualize complex, long-running business workflows that span multiple microservices.
- **Search:** **Meilisearch** will be deployed for high-speed, full-text search capabilities, while **PostGIS** will extend PostgreSQL for powerful geospatial queries.
- **AI-Powered Search:** **Vertex AI Matching Engine** will be used for more advanced semantic search and recommendation tasks.

#### 3.2.3 AI & Analytics
- **Fraud & Anomaly Detection:** A multi-layered approach using **Vertex AI** and **BigQuery ML** for model training and prediction, combined with the **Wazuh** security platform and the **pyod** library for statistical anomaly detection.
- **Document & Image Processing:** **Google's Document AI** and **Vision AI** will handle complex document parsing, supplemented by **Tesseract** for open-source OCR needs.
- **Reporting & BI:** **Looker** will be the primary tool for creating and visualizing business intelligence dashboards, powered by **BigQuery BI Engine** for accelerated query performance.

### 3.3 Infrastructure Technologies

#### 3.3.1 Google Cloud Platform (GCP)
- **Compute:**
    - **Google Kubernetes Engine (GKE):** To run stateful or specialized open-source applications like Airflow, Tryton, Directus, and Conductor.
    - **Cloud Run & Cloud Functions:** For deploying stateless, serverless microservices written in Python/FastAPI.
- **Databases:**
    - **Cloud SQL for PostgreSQL:** The primary transactional database.
    - **Firestore:** For document and configuration storage.
    - **BigQuery:** The central data warehouse for all analytics, audit logs, and ML data.
- **DevOps & Operations:**
    - **Infrastructure as Code:** **Terraform**.
    - **CI/CD:** **GitHub Actions** integrated with **Cloud Deploy**.
    - **Monitoring:** **Cloud Monitoring** as the base, supplemented with **Prometheus** for detailed container metrics.
    - **Security:** **Apigee** as the API Gateway and **Secret Manager** for credentials.

---

## 4. Database Design

### 4.1 PostgreSQL Schema Design

#### 4.1.1 Core Entities
```sql
-- Vehicle Management Schema
CREATE TABLE vehicles (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    vin VARCHAR(17) UNIQUE NOT NULL,
    registration_number VARCHAR(20) UNIQUE NOT NULL,
    make VARCHAR(50) NOT NULL,
    model VARCHAR(100) NOT NULL,
    year INTEGER NOT NULL,
    engine_capacity DECIMAL(4,2),
    fuel_type VARCHAR(20) NOT NULL,
    vehicle_type VARCHAR(30) NOT NULL,
    status VARCHAR(20) DEFAULT 'active',
    department_id UUID NOT NULL,
    assigned_driver_id UUID,
    current_mileage INTEGER DEFAULT 0,
    acquisition_date DATE NOT NULL,
    acquisition_cost DECIMAL(12,2),
    warranty_expiry DATE,
    insurance_policy_number VARCHAR(50),
    insurance_expiry DATE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    CONSTRAINT fk_vehicle_department 
        FOREIGN KEY (department_id) REFERENCES departments(id),
    CONSTRAINT fk_vehicle_driver 
        FOREIGN KEY (assigned_driver_id) REFERENCES users(id)
);

-- User Management Schema
CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    employee_number VARCHAR(20) UNIQUE,
    email VARCHAR(255) UNIQUE NOT NULL,
    phone VARCHAR(20),
    first_name VARCHAR(100) NOT NULL,
    last_name VARCHAR(100) NOT NULL,
    id_number VARCHAR(13) UNIQUE,
    role VARCHAR(30) NOT NULL,
    department_id UUID NOT NULL,
    manager_id UUID,
    is_active BOOLEAN DEFAULT true,
    license_number VARCHAR(20),
    license_expiry DATE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    CONSTRAINT fk_user_department 
        FOREIGN KEY (department_id) REFERENCES departments(id),
    CONSTRAINT fk_user_manager 
        FOREIGN KEY (manager_id) REFERENCES users(id)
);

-- Work Order Management Schema
CREATE TABLE work_orders (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    work_order_number VARCHAR(30) UNIQUE NOT NULL,
    vehicle_id UUID NOT NULL,
    vendor_id UUID,
    work_type VARCHAR(50) NOT NULL,
    priority VARCHAR(20) DEFAULT 'medium',
    status VARCHAR(30) DEFAULT 'created',
    description TEXT NOT NULL,
    estimated_cost DECIMAL(12,2),
    actual_cost DECIMAL(12,2),
    estimated_completion DATE,
    actual_completion DATE,
    mileage_at_service INTEGER,
    requested_by UUID NOT NULL,
    assigned_by UUID,
    approved_by UUID,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    CONSTRAINT fk_wo_vehicle 
        FOREIGN KEY (vehicle_id) REFERENCES vehicles(id),
    CONSTRAINT fk_wo_vendor 
        FOREIGN KEY (vendor_id) REFERENCES vendors(id),
    CONSTRAINT fk_wo_requester 
        FOREIGN KEY (requested_by) REFERENCES users(id)
);

-- Vendor Management Schema
CREATE TABLE vendors (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    company_name VARCHAR(200) NOT NULL,
    registration_number VARCHAR(50) UNIQUE,
    vat_number VARCHAR(20),
    bbbee_level INTEGER,
    bbbee_certificate_expiry DATE,
    contact_person_name VARCHAR(100),
    contact_email VARCHAR(255),
    contact_phone VARCHAR(20),
    physical_address TEXT,
    postal_address TEXT,
    bank_account_number VARCHAR(20),
    bank_name VARCHAR(100),
    bank_branch_code VARCHAR(10),
    specializations TEXT[], -- Array of specialization types
    service_radius INTEGER, -- Radius in kilometers
    capacity_score DECIMAL(3,2) DEFAULT 1.0,
    performance_score DECIMAL(3,2) DEFAULT 0.0,
    compliance_status VARCHAR(20) DEFAULT 'pending',
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Fuel Management Schema
CREATE TABLE fuel_transactions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    vehicle_id UUID NOT NULL,
    driver_id UUID NOT NULL,
    fuel_card_id UUID,
    fuel_station_id UUID,
    transaction_number VARCHAR(50) UNIQUE,
    fuel_type VARCHAR(20) NOT NULL,
    quantity_liters DECIMAL(8,3) NOT NULL,
    price_per_liter DECIMAL(6,3) NOT NULL,
    total_amount DECIMAL(10,2) NOT NULL,
    mileage_at_fill INTEGER,
    transaction_date TIMESTAMP NOT NULL,
    location_lat DECIMAL(10,8),
    location_lng DECIMAL(11,8),
    is_authorized BOOLEAN DEFAULT true,
    anomaly_flags TEXT[],
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    CONSTRAINT fk_fuel_vehicle 
        FOREIGN KEY (vehicle_id) REFERENCES vehicles(id),
    CONSTRAINT fk_fuel_driver 
        FOREIGN KEY (driver_id) REFERENCES users(id)
);
```

#### 4.1.2 Indexing Strategy
```sql
-- Performance Indexes
CREATE INDEX idx_vehicles_department ON vehicles(department_id);
CREATE INDEX idx_vehicles_status ON vehicles(status);
CREATE INDEX idx_vehicles_registration ON vehicles(registration_number);

CREATE INDEX idx_work_orders_vehicle ON work_orders(vehicle_id);
CREATE INDEX idx_work_orders_status ON work_orders(status);
CREATE INDEX idx_work_orders_created ON work_orders(created_at);

CREATE INDEX idx_users_department ON users(department_id);
CREATE INDEX idx_users_role ON users(role);
CREATE INDEX idx_users_email ON users(email);

CREATE INDEX idx_vendors_specializations ON vendors USING GIN(specializations);
CREATE INDEX idx_vendors_performance ON vendors(performance_score);

CREATE INDEX idx_fuel_vehicle_date ON fuel_transactions(vehicle_id, transaction_date);
CREATE INDEX idx_fuel_driver ON fuel_transactions(driver_id);

-- Composite Indexes for Common Queries
CREATE INDEX idx_work_orders_vehicle_status 
    ON work_orders(vehicle_id, status);
CREATE INDEX idx_vehicles_dept_status 
    ON vehicles(department_id, status);
```

### 4.2 Firestore Collections Design

#### 4.2.1 Document Structure
```typescript
// Vehicle Documents Collection
interface VehicleDocument {
  id: string;
  vehicleId: string;
  documentType: 'license' | 'insurance' | 'service_manual' | 'inspection' | 'other';
  title: string;
  description?: string;
  fileUrl: string;
  fileName: string;
  fileSize: number;
  mimeType: string;
  uploadedBy: string;
  uploadedAt: FirebaseTimestamp;
  expiryDate?: FirebaseTimestamp;
  tags: string[];
  isActive: boolean;
  metadata: {
    [key: string]: any;
  };
}

// Configuration Collection
interface SystemConfiguration {
  id: string;
  module: string;
  key: string;
  value: any;
  dataType: 'string' | 'number' | 'boolean' | 'object' | 'array';
  description: string;
  isActive: boolean;
  createdAt: FirebaseTimestamp;
  updatedAt: FirebaseTimestamp;
  updatedBy: string;
}

// Templates Collection
interface WorkOrderTemplate {
  id: string;
  name: string;
  workType: string;
  description: string;
  estimatedDuration: number; // in hours
  checklistItems: {
    id: string;
    description: string;
    required: boolean;
    category: string;
  }[];
  requiredDocuments: string[];
  estimatedCost: {
    min: number;
    max: number;
  };
  isActive: boolean;
  createdAt: FirebaseTimestamp;
  updatedAt: FirebaseTimestamp;
}
```

### 4.3 Data Relationships & Constraints

#### 4.3.1 Referential Integrity
```sql
-- Foreign Key Constraints
ALTER TABLE vehicles 
    ADD CONSTRAINT fk_vehicles_department 
    FOREIGN KEY (department_id) REFERENCES departments(id) ON DELETE RESTRICT;

ALTER TABLE work_orders 
    ADD CONSTRAINT fk_work_orders_vehicle 
    FOREIGN KEY (vehicle_id) REFERENCES vehicles(id) ON DELETE CASCADE;

-- Check Constraints
ALTER TABLE vehicles 
    ADD CONSTRAINT check_year_valid 
    CHECK (year >= 1900 AND year <= EXTRACT(YEAR FROM CURRENT_DATE) + 1);

ALTER TABLE fuel_transactions 
    ADD CONSTRAINT check_quantity_positive 
    CHECK (quantity_liters > 0);

ALTER TABLE vendors 
    ADD CONSTRAINT check_bbbee_level 
    CHECK (bbbee_level >= 1 AND bbbee_level <= 8);
```

#### 4.3.2 Python Database Models (SQLAlchemy Core)
```python
# SQLAlchemy Core table definitions for fine-grained SQL control
from sqlalchemy import (
    Table, Column, String, Integer, DateTime, Decimal, Boolean, 
    Text, ForeignKey, UUID, CheckConstraint, Index, text
)
from sqlalchemy.dialects.postgresql import ARRAY
from datetime import datetime

# Vehicles table
vehicles = Table(
    'vehicles',
    metadata,
    Column('id', UUID(as_uuid=True), primary_key=True, server_default=text('gen_random_uuid()')),
    Column('vin', String(17), unique=True, nullable=False),
    Column('registration_number', String(20), unique=True, nullable=False),
    Column('make', String(50), nullable=False),
    Column('model', String(100), nullable=False),
    Column('year', Integer, nullable=False),
    Column('status', String(20), nullable=False, default='active'),
    Column('department_id', UUID(as_uuid=True), ForeignKey('departments.id'), nullable=False),
    Column('current_mileage', Integer, default=0),
    Column('created_at', DateTime, default=datetime.utcnow),
    Column('updated_at', DateTime, default=datetime.utcnow, onupdate=datetime.utcnow),
    
    CheckConstraint('length(vin) = 17', name='check_vin_length'),
    CheckConstraint('year >= 1900 AND year <= EXTRACT(YEAR FROM CURRENT_DATE) + 1', name='check_year_valid'),
    CheckConstraint("status IN ('active', 'maintenance', 'disposed', 'accident')", name='check_status_valid'),
    
    Index('idx_vehicles_department_status', 'department_id', 'status'),
    Index('idx_vehicles_registration', 'registration_number'),
    Index('idx_vehicles_vin', 'vin'),
)

# Raw SQL query examples for complex operations
class VehicleQueries:
    @staticmethod
    def get_dashboard_summary():
        return text("""
            SELECT 
                d.name as department_name,
                COUNT(v.id) as total_vehicles,
                COUNT(CASE WHEN v.status = 'active' THEN 1 END) as active_vehicles,
                COUNT(CASE WHEN v.status = 'maintenance' THEN 1 END) as maintenance_vehicles,
                COALESCE(AVG(v.current_mileage), 0) as average_mileage
            FROM departments d
            LEFT JOIN vehicles v ON d.id = v.department_id
            GROUP BY d.id, d.name
            ORDER BY d.name
        """)
    
    @staticmethod
    def get_maintenance_due():
        return text("""
            SELECT 
                v.id,
                v.registration_number,
                v.current_mileage,
                ms.next_service_mileage,
                ms.next_service_date,
                CASE 
                    WHEN v.current_mileage >= ms.next_service_mileage THEN 'overdue'
                    WHEN v.current_mileage >= (ms.next_service_mileage - 1000) THEN 'due_soon'
                    ELSE 'ok'
                END as maintenance_status
            FROM vehicles v
            JOIN maintenance_schedules ms ON v.id = ms.vehicle_id
            WHERE v.status = 'active'
                AND (v.current_mileage >= (ms.next_service_mileage - 1000)
                OR ms.next_service_date <= CURRENT_DATE + INTERVAL '30 days')
            ORDER BY v.current_mileage - ms.next_service_mileage DESC
        """)
```

---

## 5. API Design Specification

### 5.1 API Architecture Principles

#### 5.1.1 RESTful Design
- Resource-based URLs
- HTTP methods for operations (GET, POST, PUT, PATCH, DELETE)
- Stateless communication
- Consistent response formats
- Proper HTTP status codes

#### 5.1.2 API Versioning
```
/api/v1/vehicles
/api/v1/work-orders
/api/v1/vendors
```

### 5.2 Core API Endpoints

#### 5.2.1 Vehicle Management API
```python
# FastAPI Vehicle Management Endpoints
from fastapi import FastAPI, Query, Path, HTTPException, Depends
from pydantic import BaseModel, Field
from typing import Optional, List
from enum import Enum
from datetime import datetime
import uuid

class VehicleStatus(str, Enum):
    active = "active"
    maintenance = "maintenance"
    disposed = "disposed"
    accident = "accident"

class Department(BaseModel):
    id: uuid.UUID
    name: str

class AssignedDriver(BaseModel):
    id: uuid.UUID
    name: str
    employee_number: str

class Vehicle(BaseModel):
    id: uuid.UUID
    vin: str = Field(..., min_length=17, max_length=17)
    registration_number: str = Field(..., max_length=20)
    make: str = Field(..., max_length=50)
    model: str = Field(..., max_length=100)
    year: int = Field(..., ge=1900, le=2030)
    fuel_type: str
    vehicle_type: str
    status: VehicleStatus
    department: Department
    assigned_driver: Optional[AssignedDriver] = None
    current_mileage: int = Field(default=0, ge=0)
    next_service_date: Optional[datetime] = None
    next_service_mileage: Optional[int] = None
    created_at: datetime
    updated_at: datetime

class CreateVehicleRequest(BaseModel):
    vin: str = Field(..., min_length=17, max_length=17)
    registration_number: str = Field(..., max_length=20)
    make: str = Field(..., max_length=50)
    model: str = Field(..., max_length=100)
    year: int = Field(..., ge=1900, le=2030)
    fuel_type: str
    vehicle_type: str
    department_id: uuid.UUID

class PaginatedResponse(BaseModel):
    data: List[Vehicle]
    page: int
    limit: int
    total: int
    total_pages: int

# FastAPI Endpoints
@app.get("/api/v1/vehicles", response_model=PaginatedResponse)
async def get_vehicles(
    page: int = Query(1, ge=1),
    limit: int = Query(20, ge=1, le=100),
    department: Optional[str] = Query(None),
    status: Optional[VehicleStatus] = Query(None),
    search: Optional[str] = Query(None),
    sort_by: Optional[str] = Query("created_at"),
    sort_order: Optional[str] = Query("desc"),
    db: AsyncSession = Depends(get_db)
):
    """Get all vehicles with filtering and pagination"""
    return await vehicle_service.get_vehicles(
        db, page, limit, department, status, search, sort_by, sort_order
    )

@app.get("/api/v1/vehicles/{vehicle_id}", response_model=Vehicle)
async def get_vehicle(
    vehicle_id: uuid.UUID = Path(...),
    db: AsyncSession = Depends(get_db)
):
    """Get specific vehicle by ID"""
    vehicle = await vehicle_service.get_vehicle_by_id(db, vehicle_id)
    if not vehicle:
        raise HTTPException(status_code=404, detail="Vehicle not found")
    return vehicle

@app.post("/api/v1/vehicles", response_model=Vehicle, status_code=201)
async def create_vehicle(
    vehicle_data: CreateVehicleRequest,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Create a new vehicle"""
    return await vehicle_service.create_vehicle(db, vehicle_data, current_user.id)

@app.put("/api/v1/vehicles/{vehicle_id}", response_model=Vehicle)
async def update_vehicle(
    vehicle_id: uuid.UUID = Path(...),
    vehicle_data: UpdateVehicleRequest,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Update vehicle information"""
    vehicle = await vehicle_service.update_vehicle(db, vehicle_id, vehicle_data)
    if not vehicle:
        raise HTTPException(status_code=404, detail="Vehicle not found")
    return vehicle
```

#### 5.2.2 Work Order Management API
```typescript
interface WorkOrderAPI {
  // Get work orders with filtering
  'GET /api/v1/work-orders': {
    query: {
      page?: number;
      limit?: number;
      status?: string;
      priority?: string;
      workType?: string;
      vehicleId?: string;
      vendorId?: string;
      fromDate?: string;
      toDate?: string;
    };
    response: PaginatedResponse<WorkOrder>;
  };

  // Create work order
  'POST /api/v1/work-orders': {
    body: CreateWorkOrderRequest;
    response: WorkOrder;
  };

  // Assign work order to vendor
  'POST /api/v1/work-orders/:id/assign': {
    body: {
      vendorId: string;
      estimatedCompletion: string;
      estimatedCost: number;
      notes?: string;
    };
    response: WorkOrder;
  };

  // Update work order status
  'PATCH /api/v1/work-orders/:id/status': {
    body: {
      status: WorkOrderStatus;
      notes?: string;
      actualCost?: number;
      completionDate?: string;
    };
    response: WorkOrder;
  };

  // Work order approval
  'POST /api/v1/work-orders/:id/approve': {
    body: {
      approved: boolean;
      comments?: string;
    };
    response: WorkOrder;
  };
}

interface WorkOrder {
  id: string;
  workOrderNumber: string;
  vehicle: {
    id: string;
    registrationNumber: string;
    make: string;
    model: string;
  };
  vendor?: {
    id: string;
    companyName: string;
    contactPerson: string;
    contactPhone: string;
  };
  workType: string;
  priority: 'low' | 'medium' | 'high' | 'urgent';
  status: WorkOrderStatus;
  description: string;
  estimatedCost?: number;
  actualCost?: number;
  estimatedCompletion?: string;
  actualCompletion?: string;
  requestedBy: {
    id: string;
    name: string;
  };
  assignedBy?: {
    id: string;
    name: string;
  };
  approvedBy?: {
    id: string;
    name: string;
  };
  createdAt: string;
  updatedAt: string;
}

type WorkOrderStatus = 
  | 'created' 
  | 'approved' 
  | 'assigned' 
  | 'in_progress' 
  | 'completed' 
  | 'cancelled' 
  | 'rejected';
```

#### 5.2.3 Vendor Management API
```typescript
interface VendorAPI {
  // Get vendors with filtering
  'GET /api/v1/vendors': {
    query: {
      page?: number;
      limit?: number;
      specialization?: string;
      location?: string;
      radius?: number;
      bbbeeLevel?: number;
      performanceScore?: number;
      isActive?: boolean;
    };
    response: PaginatedResponse<Vendor>;
  };

  // Register new vendor
  'POST /api/v1/vendors/register': {
    body: VendorRegistrationRequest;
    response: Vendor;
  };

  // Update vendor profile
  'PUT /api/v1/vendors/:id': {
    body: UpdateVendorRequest;
    response: Vendor;
  };

  // Get vendor performance metrics
  'GET /api/v1/vendors/:id/performance': {
    query: {
      fromDate?: string;
      toDate?: string;
    };
    response: VendorPerformanceMetrics;
  };

  // Get available vendors for work order
  'POST /api/v1/vendors/find-available': {
    body: {
      workType: string;
      location: {
        lat: number;
        lng: number;
      };
      urgency: string;
      estimatedCost: number;
    };
    response: Vendor[];
  };
}
```

### 5.3 Authentication & Authorization

#### 5.3.1 JWT Token Structure
```python
from pydantic import BaseModel
from typing import List, Optional
from datetime import datetime
import uuid

class JWTPayload(BaseModel):
    sub: uuid.UUID  # User ID
    email: str
    role: str
    department: str
    permissions: List[str]
    iat: int  # Issued at
    exp: int  # Expiration
    iss: str  # Issuer
    aud: str  # Audience

# Authentication Dependencies
from fastapi import HTTPException, Depends, status
from fastapi.security import HTTPBearer, HTTPAuthorizationCredentials
from jose import JWTError, jwt

security = HTTPBearer()

async def get_current_user(
    credentials: HTTPAuthorizationCredentials = Depends(security),
    db: AsyncSession = Depends(get_db)
) -> User:
    """Extract and validate JWT token"""
    try:
        payload = jwt.decode(credentials.credentials, SECRET_KEY, algorithms=[ALGORITHM])
        user_id = payload.get("sub")
        if user_id is None:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid authentication credentials"
            )
    except JWTError:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid authentication credentials"
        )
    
    user = await get_user_by_id(db, user_id)
    if user is None or not user.is_active:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="User not found or inactive"
        )
    return user

def require_permission(permission: str):
    """Dependency factory for permission-based authorization"""
    def check_permission(current_user: User = Depends(get_current_user)):
        if permission not in current_user.permissions:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=f"Permission '{permission}' required"
            )
        return current_user
    return check_permission

def require_role(allowed_roles: List[str]):
    """Dependency factory for role-based authorization"""
    def check_role(current_user: User = Depends(get_current_user)):
        if current_user.role not in allowed_roles:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=f"Role must be one of: {', '.join(allowed_roles)}"
            )
        return current_user
    return check_role
```

#### 5.3.2 Role-Based Access Control
```python
from enum import Enum
from typing import Dict, List

class Role(str, Enum):
    SUPER_ADMIN = "super_admin"
    ADMIN = "admin"
    FLEET_MANAGER = "fleet_manager"
    TRANSPORT_OFFICER = "transport_officer"
    DRIVER = "driver"
    FINANCE_OFFICER = "finance_officer"
    VENDOR = "vendor"
    INSPECTOR = "inspector"
    AUDITOR = "auditor"

class Permission(str, Enum):
    # Vehicle Management
    VEHICLE_VIEW = "vehicle:view"
    VEHICLE_CREATE = "vehicle:create"
    VEHICLE_UPDATE = "vehicle:update"
    VEHICLE_DELETE = "vehicle:delete"
    
    # Work Order Management
    WORK_ORDER_VIEW = "work_order:view"
    WORK_ORDER_CREATE = "work_order:create"
    WORK_ORDER_ASSIGN = "work_order:assign"
    WORK_ORDER_APPROVE = "work_order:approve"
    
    # Vendor Management
    VENDOR_VIEW = "vendor:view"
    VENDOR_MANAGE = "vendor:manage"
    VENDOR_PERFORMANCE = "vendor:performance"
    
    # Financial
    PAYMENT_VIEW = "payment:view"
    PAYMENT_APPROVE = "payment:approve"
    PAYMENT_PROCESS = "payment:process"
    
    # System Administration
    USER_MANAGE = "user:manage"
    SYSTEM_CONFIG = "system:config"
    AUDIT_VIEW = "audit:view"

# Role-Permission Mapping
ROLE_PERMISSIONS: Dict[Role, List[Permission]] = {
    Role.SUPER_ADMIN: [p for p in Permission],  # All permissions
    Role.ADMIN: [
        Permission.VEHICLE_VIEW, Permission.VEHICLE_CREATE, Permission.VEHICLE_UPDATE,
        Permission.WORK_ORDER_VIEW, Permission.WORK_ORDER_CREATE, Permission.WORK_ORDER_ASSIGN,
        Permission.VENDOR_VIEW, Permission.VENDOR_MANAGE,
        Permission.USER_MANAGE, Permission.AUDIT_VIEW
    ],
    Role.FLEET_MANAGER: [
        Permission.VEHICLE_VIEW, Permission.VEHICLE_CREATE, Permission.VEHICLE_UPDATE,
        Permission.WORK_ORDER_VIEW, Permission.WORK_ORDER_CREATE, Permission.WORK_ORDER_APPROVE,
        Permission.VENDOR_VIEW, Permission.VENDOR_PERFORMANCE,
        Permission.PAYMENT_VIEW
    ],
    Role.TRANSPORT_OFFICER: [
        Permission.VEHICLE_VIEW, Permission.VEHICLE_UPDATE,
        Permission.WORK_ORDER_VIEW, Permission.WORK_ORDER_CREATE,
        Permission.VENDOR_VIEW
    ],
    Role.DRIVER: [
        Permission.VEHICLE_VIEW,
        Permission.WORK_ORDER_VIEW
    ],
    Role.VENDOR: [
        Permission.WORK_ORDER_VIEW,
        Permission.PAYMENT_VIEW
    ]
}

# Usage example in endpoint
@app.post("/api/v1/vehicles", response_model=Vehicle)
async def create_vehicle(
    vehicle_data: CreateVehicleRequest,
    current_user: User = Depends(require_permission(Permission.VEHICLE_CREATE)),
    db: AsyncSession = Depends(get_db)
):
    """Create a new vehicle - requires vehicle:create permission"""
    return await vehicle_service.create_vehicle(db, vehicle_data, current_user.id)
```

### 5.4 Error Handling & Response Format

#### 5.4.1 Standard Response Format
```typescript
interface APIResponse<T = any> {
  success: boolean;
  data?: T;
  error?: {
    code: string;
    message: string;
    details?: any;
  };
  metadata?: {
    timestamp: string;
    requestId: string;
    version: string;
  };
}

interface PaginatedResponse<T> extends APIResponse<T[]> {
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
}
```

#### 5.4.2 Error Codes & HTTP Status Mapping
```typescript
enum ErrorCode {
  // Client Errors (4xx)
  VALIDATION_ERROR = 'VALIDATION_ERROR', // 400
  UNAUTHORIZED = 'UNAUTHORIZED', // 401
  FORBIDDEN = 'FORBIDDEN', // 403
  NOT_FOUND = 'NOT_FOUND', // 404
  CONFLICT = 'CONFLICT', // 409
  RATE_LIMITED = 'RATE_LIMITED', // 429
  
  // Server Errors (5xx)
  INTERNAL_ERROR = 'INTERNAL_ERROR', // 500
  SERVICE_UNAVAILABLE = 'SERVICE_UNAVAILABLE', // 503
  DATABASE_ERROR = 'DATABASE_ERROR', // 500
  EXTERNAL_SERVICE_ERROR = 'EXTERNAL_SERVICE_ERROR' // 502
}
```

---

## 6. Security Architecture

### 6.1 Security Principles

#### 6.1.1 Zero Trust Architecture
- Never trust, always verify
- Least privilege access
- Assume breach mentality
- Continuous monitoring

#### 6.1.2 Defense in Depth
- Multiple security layers
- Network security
- Application security
- Data security
- Identity security

### 6.2 Authentication Strategy

#### 6.2.1 Multi-Factor Authentication
```typescript
interface MFAConfiguration {
  methods: {
    sms: boolean;
    email: boolean;
    authenticatorApp: boolean;
    biometric: boolean;
  };
  requiredFor: string[]; // Roles requiring MFA
  sessionDuration: number;
  maxAttempts: number;
  lockoutDuration: number;
}
```

#### 6.2.2 Single Sign-On Integration
```typescript
interface SSOConfiguration {
  provider: 'government_sso' | 'azure_ad' | 'okta';
  endpoints: {
    authorization: string;
    token: string;
    userInfo: string;
    logout: string;
  };
  clientId: string;
  redirectUri: string;
  scopes: string[];
  claimsMapping: {
    userId: string;
    email: string;
    department: string;
    role: string;
  };
}
```

### 6.3 Data Protection

#### 6.3.1 Encryption Standards
```typescript
interface EncryptionConfig {
  atRest: {
    algorithm: 'AES-256-GCM';
    keyManagement: 'Google Cloud KMS';
    keyRotation: '90 days';
  };
  inTransit: {
    protocol: 'TLS 1.3';
    cipherSuites: string[];
    certificates: 'Let\'s Encrypt';
  };
  atApplication: {
    personalData: 'AES-256-CBC';
    sensitiveFields: string[];
    keyDerivation: 'PBKDF2';
  };
}
```

#### 6.3.2 POPIA Compliance
```typescript
interface POPIACompliance {
  dataCategories: {
    personal: string[];
    sensitive: string[];
    biometric: string[];
  };
  retention: {
    [category: string]: string; // Retention period
  };
  consent: {
    required: boolean;
    granular: boolean;
    withdrawable: boolean;
  };
  rightsManagement: {
    access: boolean;
    correction: boolean;
    deletion: boolean;
    portability: boolean;
  };
}
```

### 6.4 Network Security

#### 6.4.1 Network Architecture
```yaml
# Network Security Configuration
vpc:
  cidr: "10.0.0.0/16"
  subnets:
    public: "********/24"
    private: "********/24"
    database: "********/24"

firewall:
  ingress:
    - port: 443
      protocol: TCP
      source: "0.0.0.0/0"
    - port: 80
      protocol: TCP
      source: "0.0.0.0/0"
      redirect: true
  egress:
    - port: 443
      protocol: TCP
      destination: "external_apis"

loadBalancer:
  type: "Application"
  sslTermination: true
  wafEnabled: true
  ddosProtection: true
```

---

## 7. Performance Optimization

### 7.1 Performance Requirements

#### 7.1.1 Response Time Targets
```typescript
interface PerformanceTargets {
  api: {
    p50: '200ms';
    p95: '500ms';
    p99: '1000ms';
  };
  web: {
    initialLoad: '2000ms';
    routeChange: '500ms';
    interaction: '100ms';
  };
  mobile: {
    appLaunch: '3000ms';
    screenTransition: '300ms';
    dataSync: '5000ms';
  };
}
```

#### 7.1.2 Scalability Targets
```typescript
interface ScalabilityTargets {
  concurrentUsers: 10000;
  requestsPerSecond: 5000;
  dataGrowthRate: '100GB/month';
  peakLoadMultiplier: 3;
  autoScaling: {
    cpuThreshold: 70;
    memoryThreshold: 80;
    responseTimeThreshold: '500ms';
  };
}
```

### 7.2 Caching Strategy

#### 7.2.1 Multi-Level Caching
```typescript
interface CachingStrategy {
  browser: {
    staticAssets: '1 year';
    apiResponses: '5 minutes';
    userSession: 'session';
  };
  cdn: {
    images: '1 month';
    documents: '1 week';
    apiResponses: '1 hour';
  };
  application: {
    redis: {
      sessionData: '24 hours';
      frequentQueries: '1 hour';
      userPreferences: '1 week';
    };
  };
  database: {
    queryResultCache: '15 minutes';
    connectionPooling: true;
    readReplicas: 3;
  };
}
```

#### 7.2.2 Cache Invalidation
```typescript
interface CacheInvalidation {
  strategies: {
    timeBasedExpiry: boolean;
    eventBasedInvalidation: boolean;
    manualInvalidation: boolean;
  };
  events: {
    'vehicle.updated': ['vehicle:*', 'fleet:summary'];
    'work_order.created': ['work_orders:*', 'dashboard:*'];
    'user.role_changed': ['user:*', 'permissions:*'];
  };
}
```

### 7.3 Database Optimization

#### 7.3.1 Query Optimization
```sql
-- Optimized Queries with Proper Indexing
-- Dashboard Summary Query
SELECT 
    d.name as department_name,
    COUNT(v.id) as total_vehicles,
    COUNT(CASE WHEN v.status = 'active' THEN 1 END) as active_vehicles,
    COUNT(CASE WHEN v.status = 'maintenance' THEN 1 END) as maintenance_vehicles,
    COALESCE(SUM(ft.total_amount), 0) as monthly_fuel_cost
FROM departments d
LEFT JOIN vehicles v ON d.id = v.department_id
LEFT JOIN fuel_transactions ft ON v.id = ft.vehicle_id 
    AND ft.transaction_date >= DATE_TRUNC('month', CURRENT_DATE)
GROUP BY d.id, d.name
ORDER BY d.name;

-- Work Order Performance Query
SELECT 
    wo.id,
    wo.work_order_number,
    v.registration_number,
    ven.company_name,
    wo.status,
    wo.created_at,
    wo.estimated_completion,
    EXTRACT(EPOCH FROM (wo.actual_completion - wo.created_at))/3600 as completion_hours
FROM work_orders wo
JOIN vehicles v ON wo.vehicle_id = v.id
LEFT JOIN vendors ven ON wo.vendor_id = ven.id
WHERE wo.created_at >= CURRENT_DATE - INTERVAL '30 days'
    AND wo.status IN ('completed', 'in_progress')
ORDER BY wo.created_at DESC
LIMIT 100;
```

#### 7.3.2 Connection Pooling
```python
from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession
from sqlalchemy.orm import sessionmaker
from sqlalchemy.pool import QueuePool

# Database Configuration
DATABASE_CONFIG = {
    "connection_pool": {
        "pool_size": 20,          # Number of connections to maintain
        "max_overflow": 30,       # Additional connections beyond pool_size
        "pool_timeout": 30,       # Timeout to get connection from pool
        "pool_recycle": 3600,     # Recycle connections after 1 hour
        "pool_pre_ping": True,    # Validate connections before use
    },
    "read_replicas": {
        "count": 3,
        "load_balancing": "round_robin",
        "failover": True
    }
}

# Async Engine with Connection Pooling
engine = create_async_engine(
    DATABASE_URL,
    poolclass=QueuePool,
    pool_size=DATABASE_CONFIG["connection_pool"]["pool_size"],
    max_overflow=DATABASE_CONFIG["connection_pool"]["max_overflow"],
    pool_timeout=DATABASE_CONFIG["connection_pool"]["pool_timeout"],
    pool_recycle=DATABASE_CONFIG["connection_pool"]["pool_recycle"],
    pool_pre_ping=DATABASE_CONFIG["connection_pool"]["pool_pre_ping"],
    echo=False,  # Set to True for SQL logging in development
)

# Session Factory
AsyncSessionLocal = sessionmaker(
    engine, class_=AsyncSession, expire_on_commit=False
)

# Database Dependency
async def get_db() -> AsyncSession:
    async with AsyncSessionLocal() as session:
        try:
            yield session
        finally:
            await session.close()

# Read Replica Configuration (for read-heavy operations)
read_engine = create_async_engine(
    READ_REPLICA_URL,
    poolclass=QueuePool,
    pool_size=10,
    max_overflow=20,
    pool_timeout=30,
    pool_recycle=3600,
    pool_pre_ping=True,
)

async def get_read_db() -> AsyncSession:
    """Use read replica for query-only operations"""
    async with sessionmaker(read_engine, class_=AsyncSession)() as session:
        try:
            yield session
        finally:
            await session.close()
```

---

## 8. Integration Architecture

### 8.1 External System Integration

#### 8.1.1 Integration Patterns
```typescript
interface IntegrationPatterns {
  synchronous: {
    pattern: 'Request-Response';
    protocol: 'REST/GraphQL';
    timeout: '30 seconds';
    retries: 3;
    circuitBreaker: true;
  };
  asynchronous: {
    pattern: 'Event-Driven';
    protocol: 'Message Queue';
    durability: true;
    ordering: false;
    deadLetterQueue: true;
  };
  batch: {
    pattern: 'ETL Pipeline';
    schedule: 'Daily/Hourly';
    chunkSize: 1000;
    errorHandling: 'Skip and Log';
  };
}
```

#### 8.1.2 API Integration Framework
```typescript
interface APIIntegration {
  rtmc: {
    baseUrl: string;
    authentication: 'API_KEY';
    endpoints: {
      fineDetails: '/api/v1/fines/{id}';
      vehicleFines: '/api/v1/vehicles/{registration}/fines';
      paymentNotification: '/api/v1/payments';
    };
    rateLimit: '100/minute';
    timeout: 15000;
  };
  
  banking: {
    baseUrl: string;
    authentication: 'OAuth2';
    endpoints: {
      payments: '/api/v2/payments';
      accountBalance: '/api/v2/accounts/{id}/balance';
      transactions: '/api/v2/accounts/{id}/transactions';
    };
    rateLimit: '500/minute';
    timeout: 30000;
  };
  
  telematics: {
    providers: ['provider1', 'provider2'];
    protocol: 'WebSocket';
    dataFormat: 'JSON';
    realTimeUpdates: true;
    batchSync: {
      interval: '5 minutes';
      batchSize: 100;
    };
  };
}
```

### 8.2 Message Queue Architecture

#### 8.2.1 Queue Configuration
```typescript
interface MessageQueueConfig {
  exchanges: {
    'fleet.events': {
      type: 'topic';
      durable: true;
      autoDelete: false;
    };
    'payments.events': {
      type: 'topic';
      durable: true;
      autoDelete: false;
    };
  };
  
  queues: {
    'work-order.created': {
      exchange: 'fleet.events';
      routingKey: 'work_order.created';
      consumers: ['notification-service', 'analytics-service'];
    };
    'payment.approved': {
      exchange: 'payments.events';
      routingKey: 'payment.approved';
      consumers: ['vendor-service', 'finance-service'];
    };
  };
  
  deadLetterQueues: {
    enabled: true;
    maxRetries: 3;
    retryDelay: '5 minutes';
  };
}
```

---

## 9. Testing Strategy

### 9.1 Testing Pyramid

#### 9.1.1 Unit Testing (70%)
```typescript
interface UnitTestingStrategy {
  framework: 'Jest';
  coverage: {
    minimum: 80;
    target: 90;
    statements: 85;
    branches: 80;
    functions: 90;
    lines: 85;
  };
  mocking: {
    database: 'In-memory SQLite';
    externalAPIs: 'MSW (Mock Service Worker)';
    timeDependent: 'Jest fake timers';
  };
  testTypes: [
    'Pure function testing',
    'Business logic testing',
    'Service layer testing',
    'Utility function testing'
  ];
}
```

#### 9.1.2 Integration Testing (20%)
```typescript
interface IntegrationTestingStrategy {
  framework: 'Jest + Supertest';
  database: 'Test PostgreSQL instance';
  scope: [
    'API endpoint testing',
    'Database integration',
    'External service integration',
    'Message queue testing'
  ];
  testContainers: {
    enabled: true;
    services: ['postgres', 'redis', 'rabbitmq'];
  };
}
```

#### 9.1.3 End-to-End Testing (10%)
```typescript
interface E2ETestingStrategy {
  web: {
    framework: 'Cypress';
    browsers: ['Chrome', 'Firefox', 'Safari'];
    viewports: ['desktop', 'tablet', 'mobile'];
  };
  mobile: {
    framework: 'Detox';
    platforms: ['iOS', 'Android'];
    devices: ['iPhone 13', 'Samsung Galaxy S21'];
  };
  criticalUserJourneys: [
    'User login and authentication',
    'Vehicle registration process',
    'Work order creation and approval',
    'Vendor assignment and completion',
    'Report generation and export'
  ];
}
```

### 9.2 Performance Testing

#### 9.2.1 Load Testing
```typescript
interface LoadTestingStrategy {
  tools: ['Artillery', 'K6'];
  scenarios: {
    normalLoad: {
      users: 1000;
      duration: '10 minutes';
      rampUp: '2 minutes';
    };
    peakLoad: {
      users: 5000;
      duration: '30 minutes';
      rampUp: '5 minutes';
    };
    stressTest: {
      users: 10000;
      duration: '15 minutes';
      rampUp: '3 minutes';
    };
  };
  metrics: [
    'Response time percentiles',
    'Throughput (requests/second)',
    'Error rates',
    'Resource utilization'
  ];
}
```

---

## 10. Monitoring & Observability

### 10.1 Monitoring Stack

#### 10.1.1 Application Monitoring
```typescript
interface MonitoringConfiguration {
  metrics: {
    collector: 'Prometheus';
    storage: 'Google Cloud Monitoring';
    visualization: 'Grafana';
    alerting: 'Alertmanager';
  };
  
  logging: {
    aggregation: 'Google Cloud Logging';
    structured: true;
    format: 'JSON';
    levels: ['error', 'warn', 'info', 'debug'];
  };
  
  tracing: {
    system: 'Google Cloud Trace';
    sampling: 0.1; // 10% sampling rate
    instrumentation: 'OpenTelemetry';
  };
  
  synthetics: {
    provider: 'Google Cloud Monitoring';
    checks: [
      'API health checks',
      'User journey monitoring',
      'Third-party service availability'
    ];
    frequency: '1 minute';
  };
}
```

#### 10.1.2 Business Metrics
```typescript
interface BusinessMetrics {
  kpis: {
    vehicleUtilization: {
      metric: 'percentage';
      target: 85;
      alert: 'below 70';
    };
    maintenanceCostPerKm: {
      metric: 'currency';
      target: 'R 2.50';
      alert: 'above R 3.00';
    };
    workOrderTurnaround: {
      metric: 'hours';
      target: 48;
      alert: 'above 72';
    };
    vendorCompliance: {
      metric: 'percentage';
      target: 95;
      alert: 'below 90';
    };
  };
  
  dashboards: [
    'Executive Summary',
    'Fleet Operations',
    'Vendor Performance',
    'Financial Overview',
    'System Health'
  ];
}
```

### 10.2 Alerting Strategy

#### 10.2.1 Alert Categories
```typescript
interface AlertConfiguration {
  critical: {
    conditions: [
      'System downtime > 5 minutes',
      'Database connection failures',
      'Payment processing errors',
      'Security incidents'
    ];
    channels: ['pagerduty', 'sms', 'email'];
    escalation: '15 minutes';
  };
  
  warning: {
    conditions: [
      'Response time > 1 second',
      'Error rate > 5%',
      'Queue depth > 1000',
      'Disk usage > 80%'
    ];
    channels: ['slack', 'email'];
    escalation: '1 hour';
  };
  
  info: {
    conditions: [
      'Deployment completed',
      'Backup completed',
      'Scheduled maintenance',
      'Performance reports'
    ];
    channels: ['slack'];
    escalation: 'none';
  };
}
```

---

## 11. Deployment Architecture

### 11.1 Container Strategy

#### 11.1.1 Docker Configuration
```dockerfile
# Multi-stage Dockerfile for Python FastAPI services
FROM python:3.11-slim AS base
WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y \
    gcc \
    postgresql-client \
    && rm -rf /var/lib/apt/lists/*

# Copy requirements first for better layer caching
COPY requirements.txt .
RUN pip install --no-cache-dir --upgrade pip \
    && pip install --no-cache-dir -r requirements.txt

FROM python:3.11-slim AS runtime
WORKDIR /app

# Install runtime dependencies only
RUN apt-get update && apt-get install -y \
    postgresql-client \
    && rm -rf /var/lib/apt/lists/*

# Copy installed packages from base stage
COPY --from=base /usr/local/lib/python3.11/site-packages /usr/local/lib/python3.11/site-packages
COPY --from=base /usr/local/bin /usr/local/bin

# Copy application code
COPY . .

# Create non-root user
RUN addgroup --system --gid 1001 appgroup \
    && adduser --system --uid 1001 --gid 1001 appuser

# Change ownership of app directory
RUN chown -R appuser:appgroup /app
USER appuser

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
    CMD python -c "import requests; requests.get('http://localhost:8000/health')" || exit 1

EXPOSE 8000
CMD ["uvicorn", "main:app", "--host", "0.0.0.0", "--port", "8000", "--workers", "4"]
```

#### 11.1.2 Kubernetes Deployment
```yaml
# Kubernetes Deployment Configuration
apiVersion: apps/v1
kind: Deployment
metadata:
  name: fleet-api
  namespace: fleet-management
spec:
  replicas: 3
  selector:
    matchLabels:
      app: fleet-api
  template:
    metadata:
      labels:
        app: fleet-api
    spec:
      containers:
      - name: fleet-api
        image: gcr.io/project/fleet-api:latest
        ports:
        - containerPort: 3000
        env:
        - name: DATABASE_URL
          valueFrom:
            secretKeyRef:
              name: database-secrets
              key: url
        resources:
          requests:
            memory: "256Mi"
            cpu: "250m"
          limits:
            memory: "512Mi"
            cpu: "500m"
        livenessProbe:
          httpGet:
            path: /health
            port: 3000
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /ready
            port: 3000
          initialDelaySeconds: 5
          periodSeconds: 5
```

### 11.2 CI/CD Pipeline

#### 11.2.1 GitHub Actions Workflow
```yaml
name: Build and Deploy
on:
  push:
    branches: [main, develop]
  pull_request:
    branches: [main]

env:
  PYTHON_VERSION: '3.11'

jobs:
  test:
    runs-on: ubuntu-latest
    
    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_PASSWORD: test
          POSTGRES_USER: test
          POSTGRES_DB: test_db
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432

    steps:
    - uses: actions/checkout@v4
    - uses: actions/setup-python@v4
      with:
        python-version: ${{ env.PYTHON_VERSION }}
        cache: 'pip'
    - run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt
        pip install -r requirements-dev.txt
    - run: ruff check .
    - run: black --check .
    - run: mypy .
    - run: pytest --cov=. --cov-report=xml
      env:
        DATABASE_URL: postgresql://test:test@localhost:5432/test_db
        TEST_MODE: true

  security-scan:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v4
    - uses: actions/setup-python@v4
      with:
        python-version: ${{ env.PYTHON_VERSION }}
    - run: |
        pip install safety bandit
        safety check
        bandit -r . -f json

  deploy:
    needs: [test, security-scan]
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'
    steps:
    - uses: actions/checkout@v4
    - uses: google-github-actions/setup-gcloud@v1
      with:
        service_account_key: ${{ secrets.GCP_SA_KEY }}
        project_id: ${{ secrets.GCP_PROJECT_ID }}
    - run: gcloud auth configure-docker
    - run: docker build -t gcr.io/${{ secrets.GCP_PROJECT_ID }}/fleet-api:${{ github.sha }} .
    - run: docker push gcr.io/${{ secrets.GCP_PROJECT_ID }}/fleet-api:${{ github.sha }}
    - run: kubectl set image deployment/fleet-api fleet-api=gcr.io/${{ secrets.GCP_PROJECT_ID }}/fleet-api:${{ github.sha }}
```

---

## 12. Disaster Recovery & Business Continuity

### 12.1 Backup Strategy

#### 12.1.1 Data Backup
```typescript
interface BackupStrategy {
  databases: {
    postgresql: {
      frequency: 'every 6 hours';
      retention: '30 days';
      encryption: true;
      compression: true;
      verification: 'daily';
    };
    firestore: {
      frequency: 'daily';
      retention: '90 days';
      export: 'Cloud Storage';
    };
  };
  
  files: {
    userUploads: {
      frequency: 'continuous (replication)';
      retention: '1 year';
      crossRegion: true;
    };
    systemLogs: {
      frequency: 'real-time';
      retention: '6 months';
      coldStorage: true;
    };
  };
  
  configuration: {
    kubernetes: {
      frequency: 'on change';
      retention: '6 months';
      gitOps: true;
    };
    secrets: {
      frequency: 'weekly';
      retention: '3 months';
      encrypted: true;
    };
  };
}
```

### 12.2 Recovery Procedures

#### 12.2.1 Recovery Time Objectives
```typescript
interface RecoveryObjectives {
  criticalSystems: {
    rto: '1 hour'; // Recovery Time Objective
    rpo: '15 minutes'; // Recovery Point Objective
    systems: ['authentication', 'core APIs', 'database'];
  };
  
  normalOperations: {
    rto: '4 hours';
    rpo: '1 hour';
    systems: ['reporting', 'analytics', 'integrations'];
  };
  
  nonCritical: {
    rto: '24 hours';
    rpo: '6 hours';
    systems: ['historical reports', 'archived data'];
  };
}
```

---
