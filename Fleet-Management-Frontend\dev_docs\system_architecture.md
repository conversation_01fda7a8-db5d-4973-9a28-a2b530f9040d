# RT46-2026 Fleet Management System Architecture

An interactive version of this diagram is available on [Mermaid Chart](https://www.mermaidchart.com/app/projects/6241f6e2-8771-4cb3-b5f0-e24fc5adff68/diagrams/d11cb81a-f601-4a26-b252-0496abc1a138/version/v0.1/edit).

```mermaid
flowchart TB
 subgraph subGraph0["Presentation Layer"]
    direction LR
        WebPortal["Web Portal <br>(React.js)"]
        MobileApp["Mobile App <br>(React Native)"]
        AdminUI["Admin UI <br>(Directus on GKE)"]
  end
 subgraph subGraph1["API Gateway & Security"]
    direction TB
        Apigee["Apigee <br>(API Gateway)"]
        Authelia["Authelia <br>(OIDC / RBAC / MFA)"]
  end
 subgraph subGraph2["Core Microservices <br>(Python · FastAPI · Cloud Run)"]
    direction TB
        VehicleService["Vehicle & Merchant Registry"]
        WorkOrderService["Work Order Service"]
        MROService["Maintenance &amp; Repair Orders <br>(Tryton on GKE)"]
        DocumentService["Document Mgmt Service"]
        AllocationEngine["Work Allocation Engine <br>(Cloud Workflows / Airflow)"]
        WorkflowEngine["Workflow Orchestration <br>(Conductor)"]
        PaymentService["Payment Service"]
        AuditService["Audit Logging Service"]
  end
 subgraph subGraph3["AI, Analytics & Data Intelligence"]
    direction TB
        FraudAI["Fraud &amp; Anomaly Detection <br>(Vertex AI · BigQuery ML · Wazuh · pyod)"]
        SearchAI["Search &amp; Geo-Matching <br>(Vertex AI Matching Engine · Meilisearch · PostGIS)"]
        DocAI["Document &amp; Image AI <br>(Document AI · Vision AI · Tesseract)"]
        Analytics["Analytics &amp; Reporting <br>(Looker · BigQuery BI Engine)"]
        AuditPipeline["Audit Pipeline <br>(Cloud Logging → Pub/Sub → BigQuery)"]
  end
 subgraph subGraph4["Messaging &amp; Eventing"]
    direction TB
        RabbitMQ["RabbitMQ"]
        PubSub["Google Pub/Sub"]
  end
 subgraph subGraph5["Data Layer"]
    direction TB
        CloudSQL[("Cloud SQL <br>(PostgreSQL + PostGIS)")]
        FirestoreDB[("Firestore")]
        BigQueryDB[("BigQuery")]
        RedisCache[("Redis / Memorystore")]
        CloudStorage[("Cloud Storage")]
  end
 subgraph subGraph6["External Systems"]
    direction TB
        RTMC["RTMC APIs"]
        SARS["SARS APIs"]
        Banking["Banking Systems"]
        Telematics["Telematics Providers"]
        FuelNetwork["Fuel Network"]
        Insurance["Insurance Providers"]
  end
 subgraph subGraph7["Observability & Monitoring"]
    direction TB
        CloudMonitoring["Cloud Monitoring"]
        Prometheus["Prometheus"]
        Grafana["Grafana"]
        CloudLogging["Cloud Logging"]
  end
 subgraph subGraph8["CI/CD & Infrastructure"]
    direction TB
        GitHub["GitHub Actions"]
        CloudDeploy["Cloud Deploy"]
        Terraform["Terraform"]
        GKE["Google Kubernetes Engine"]
        CloudRun["Cloud Run"]
  end
    WebPortal --> Apigee
    MobileApp --> Apigee
    AdminUI --> Apigee
    Apigee --> Authelia
    Authelia --> VehicleService & WorkOrderService & DocumentService & PaymentService
    WorkOrderService --> AllocationEngine & CloudSQL & RabbitMQ & RedisCache & FuelNetwork
    AllocationEngine --> WorkflowEngine
    VehicleService --> CloudSQL & RabbitMQ & RedisCache & Telematics & RTMC & SARS
    MROService --> CloudSQL
    DocumentService --> CloudStorage & FirestoreDB
    PaymentService --> Banking & Banking
    WorkflowEngine --> RabbitMQ
    RabbitMQ --> FraudAI & Analytics
    PubSub --> AuditPipeline
    FraudAI --> BigQueryDB & CloudStorage
    SearchAI --> CloudSQL & BigQueryDB
    DocAI --> CloudStorage
    Analytics --> BigQueryDB
    AuditService --> AuditPipeline
    AuditPipeline --> BigQueryDB
    Insurance --> PaymentService
    CloudMonitoring -.-> VehicleService & WorkOrderService
    Prometheus -.-> GKE
    Grafana -.-> Prometheus
    CloudLogging -.-> VehicleService & WorkOrderService
    GitHub --> CloudDeploy & CloudDeploy
    CloudDeploy --> GKE & CloudRun
    Terraform --> GKE & CloudRun

``` 