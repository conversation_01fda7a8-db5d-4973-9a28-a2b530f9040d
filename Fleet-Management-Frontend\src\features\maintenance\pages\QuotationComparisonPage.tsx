import React, { useState } from 'react';
import { ArrowLeft, CheckCircle, XCircle, Star, MapPin, Phone, Mail, Clock, DollarSign, Award, AlertTriangle } from 'lucide-react';
import { useNavigate, useParams } from 'react-router-dom';

interface Quote {
  id: string;
  merchantId: string;
  merchantName: string;
  merchantRating: number;
  merchantLocation: string;
  merchantContact: string;
  merchantEmail: string;
  totalCost: number;
  laborCost: number;
  partsCost: number;
  estimatedDays: number;
  warranty: string;
  submittedDate: string;
  validUntil: string;
  status: 'Pending' | 'Submitted' | 'Approved' | 'Rejected';
  notes?: string;
  breakdown: {
    item: string;
    description: string;
    quantity: number;
    unitPrice: number;
    total: number;
  }[];
  certifications: string[];
  previousWork: number;
}

const QuotationComparisonPage: React.FC = () => {
  const navigate = useNavigate();
  const { requestId } = useParams();
  const [selectedQuote, setSelectedQuote] = useState<string | null>(null);
  const [showApprovalModal, setShowApprovalModal] = useState(false);

  // Mock data - replace with API call
  const maintenanceRequest = {
    id: requestId,
    vehicleReg: 'GP 123 ABC',
    vehicleMake: 'Toyota',
    vehicleModel: 'Hilux',
    serviceType: 'Major Service',
    description: 'Engine service, brake pads replacement, and general inspection',
    requestDate: '2025-01-10',
    urgency: 'Medium'
  };

  const quotes: Quote[] = [
    {
      id: 'Q-001',
      merchantId: 'M-001',
      merchantName: 'AutoCare Services',
      merchantRating: 4.8,
      merchantLocation: 'Pretoria Central',
      merchantContact: '012 345 6789',
      merchantEmail: '<EMAIL>',
      totalCost: 8500,
      laborCost: 3500,
      partsCost: 5000,
      estimatedDays: 3,
      warranty: '12 months',
      submittedDate: '2025-01-11',
      validUntil: '2025-01-25',
      status: 'Submitted',
      breakdown: [
        { item: 'Engine Oil Change', description: 'Full synthetic oil change', quantity: 1, unitPrice: 800, total: 800 },
        { item: 'Brake Pads', description: 'Front brake pads replacement', quantity: 1, unitPrice: 2500, total: 2500 },
        { item: 'Air Filter', description: 'Engine air filter replacement', quantity: 1, unitPrice: 350, total: 350 },
        { item: 'Labor', description: 'Service labor (3 hours)', quantity: 3, unitPrice: 450, total: 1350 }
      ],
      certifications: ['ISO 9001', 'MIWA Certified'],
      previousWork: 15
    },
    {
      id: 'Q-002',
      merchantId: 'M-002',
      merchantName: 'Premium Motors',
      merchantRating: 4.6,
      merchantLocation: 'Sandton',
      merchantContact: '011 234 5678',
      merchantEmail: '<EMAIL>',
      totalCost: 9200,
      laborCost: 4200,
      partsCost: 5000,
      estimatedDays: 2,
      warranty: '18 months',
      submittedDate: '2025-01-12',
      validUntil: '2025-01-26',
      status: 'Submitted',
      breakdown: [
        { item: 'Premium Oil Change', description: 'High-grade synthetic oil', quantity: 1, unitPrice: 1200, total: 1200 },
        { item: 'Brake Pads', description: 'OEM brake pads with sensors', quantity: 1, unitPrice: 3000, total: 3000 },
        { item: 'Air Filter', description: 'High-flow air filter', quantity: 1, unitPrice: 500, total: 500 },
        { item: 'Labor', description: 'Premium service (2.5 hours)', quantity: 2.5, unitPrice: 600, total: 1500 }
      ],
      certifications: ['ISO 9001', 'Toyota Certified', 'AA Approved'],
      previousWork: 28
    },
    {
      id: 'Q-003',
      merchantId: 'M-003',
      merchantName: 'Budget Auto Repair',
      merchantRating: 4.2,
      merchantLocation: 'Johannesburg South',
      merchantContact: '011 987 6543',
      merchantEmail: '<EMAIL>',
      totalCost: 7200,
      laborCost: 2700,
      partsCost: 4500,
      estimatedDays: 4,
      warranty: '6 months',
      submittedDate: '2025-01-13',
      validUntil: '2025-01-27',
      status: 'Submitted',
      breakdown: [
        { item: 'Standard Oil Change', description: 'Semi-synthetic oil change', quantity: 1, unitPrice: 600, total: 600 },
        { item: 'Brake Pads', description: 'Aftermarket brake pads', quantity: 1, unitPrice: 1800, total: 1800 },
        { item: 'Air Filter', description: 'Standard air filter', quantity: 1, unitPrice: 250, total: 250 },
        { item: 'Labor', description: 'Standard service (3.5 hours)', quantity: 3.5, unitPrice: 350, total: 1225 }
      ],
      certifications: ['MIWA Certified'],
      previousWork: 8
    }
  ];

  const handleApproveQuote = (quoteId: string) => {
    setSelectedQuote(quoteId);
    setShowApprovalModal(true);
  };

  const confirmApproval = () => {
    console.log('Approving quote:', selectedQuote);
    // API call to approve quote
    setShowApprovalModal(false);
    navigate('/maintenance/requests', {
      state: { message: 'Quote approved successfully. Work order has been created.' }
    });
  };

  const handleRejectQuote = (quoteId: string) => {
    console.log('Rejecting quote:', quoteId);
    // API call to reject quote
  };

  const getBestValue = () => {
    const lowestCost = Math.min(...quotes.map(q => q.totalCost));
    const highestRating = Math.max(...quotes.map(q => q.merchantRating));
    const shortestTime = Math.min(...quotes.map(q => q.estimatedDays));
    
    return quotes.map(quote => {
      let score = 0;
      if (quote.totalCost === lowestCost) score += 3;
      if (quote.merchantRating === highestRating) score += 2;
      if (quote.estimatedDays === shortestTime) score += 1;
      return { ...quote, valueScore: score };
    }).sort((a, b) => b.valueScore - a.valueScore)[0];
  };

  const bestValue = getBestValue();

  return (
    <div className="max-w-7xl mx-auto p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center gap-4">
        <button 
          onClick={() => navigate('/maintenance/requests')}
          className="p-2 hover:bg-gray-100 rounded-lg"
        >
          <ArrowLeft className="w-5 h-5" />
        </button>
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Quote Comparison</h1>
          <p className="text-gray-600">
            {maintenanceRequest.vehicleReg} - {maintenanceRequest.serviceType}
          </p>
        </div>
      </div>

      {/* Request Summary */}
      <div className="bg-white rounded-lg shadow-sm border p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Maintenance Request Details</h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div>
            <p className="text-sm text-gray-600">Vehicle</p>
            <p className="font-medium">{maintenanceRequest.vehicleReg} - {maintenanceRequest.vehicleMake} {maintenanceRequest.vehicleModel}</p>
          </div>
          <div>
            <p className="text-sm text-gray-600">Service Type</p>
            <p className="font-medium">{maintenanceRequest.serviceType}</p>
          </div>
          <div>
            <p className="text-sm text-gray-600">Request Date</p>
            <p className="font-medium">{new Date(maintenanceRequest.requestDate).toLocaleDateString()}</p>
          </div>
        </div>
        <div className="mt-4">
          <p className="text-sm text-gray-600">Description</p>
          <p className="font-medium">{maintenanceRequest.description}</p>
        </div>
      </div>

      {/* Comparison Summary */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div className="bg-white rounded-lg shadow-sm border p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">Quotes Received</p>
              <p className="text-2xl font-bold text-gray-900">{quotes.length}</p>
            </div>
            <DollarSign className="w-8 h-8 text-blue-500" />
          </div>
        </div>
        <div className="bg-white rounded-lg shadow-sm border p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">Lowest Quote</p>
              <p className="text-2xl font-bold text-green-600">R {Math.min(...quotes.map(q => q.totalCost)).toLocaleString()}</p>
            </div>
            <Award className="w-8 h-8 text-green-500" />
          </div>
        </div>
        <div className="bg-white rounded-lg shadow-sm border p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">Fastest Delivery</p>
              <p className="text-2xl font-bold text-orange-600">{Math.min(...quotes.map(q => q.estimatedDays))} days</p>
            </div>
            <Clock className="w-8 h-8 text-orange-500" />
          </div>
        </div>
        <div className="bg-white rounded-lg shadow-sm border p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">Best Value</p>
              <p className="text-lg font-bold text-purple-600">{bestValue.merchantName}</p>
            </div>
            <Star className="w-8 h-8 text-purple-500" />
          </div>
        </div>
      </div>

      {/* Quotes Comparison */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {quotes.map((quote) => (
          <div key={quote.id} className={`bg-white rounded-lg shadow-sm border ${quote.id === bestValue.id ? 'ring-2 ring-purple-500' : ''}`}>
            {quote.id === bestValue.id && (
              <div className="bg-purple-500 text-white text-center py-2 rounded-t-lg">
                <Award className="w-4 h-4 inline mr-2" />
                Best Value
              </div>
            )}
            
            <div className="p-6">
              {/* Merchant Info */}
              <div className="mb-4">
                <h3 className="text-lg font-semibold text-gray-900">{quote.merchantName}</h3>
                <div className="flex items-center gap-1 mt-1">
                  {Array.from({ length: 5 }, (_, i) => (
                    <Star key={i} className={`w-4 h-4 ${i < Math.floor(quote.merchantRating) ? 'text-yellow-400 fill-current' : 'text-gray-300'}`} />
                  ))}
                  <span className="text-sm text-gray-600 ml-1">({quote.merchantRating})</span>
                </div>
                <div className="text-sm text-gray-600 mt-2 space-y-1">
                  <div className="flex items-center gap-2">
                    <MapPin className="w-4 h-4" />
                    {quote.merchantLocation}
                  </div>
                  <div className="flex items-center gap-2">
                    <Phone className="w-4 h-4" />
                    {quote.merchantContact}
                  </div>
                </div>
              </div>

              {/* Cost Breakdown */}
              <div className="mb-4">
                <div className="text-2xl font-bold text-gray-900 mb-2">
                  R {quote.totalCost.toLocaleString()}
                </div>
                <div className="text-sm text-gray-600 space-y-1">
                  <div className="flex justify-between">
                    <span>Parts:</span>
                    <span>R {quote.partsCost.toLocaleString()}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Labor:</span>
                    <span>R {quote.laborCost.toLocaleString()}</span>
                  </div>
                </div>
              </div>

              {/* Key Details */}
              <div className="mb-4 space-y-2">
                <div className="flex justify-between text-sm">
                  <span className="text-gray-600">Estimated Time:</span>
                  <span className="font-medium">{quote.estimatedDays} days</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span className="text-gray-600">Warranty:</span>
                  <span className="font-medium">{quote.warranty}</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span className="text-gray-600">Previous Jobs:</span>
                  <span className="font-medium">{quote.previousWork}</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span className="text-gray-600">Valid Until:</span>
                  <span className="font-medium">{new Date(quote.validUntil).toLocaleDateString()}</span>
                </div>
              </div>

              {/* Certifications */}
              <div className="mb-4">
                <p className="text-sm text-gray-600 mb-2">Certifications:</p>
                <div className="flex flex-wrap gap-1">
                  {quote.certifications.map((cert, index) => (
                    <span key={index} className="bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded">
                      {cert}
                    </span>
                  ))}
                </div>
              </div>

              {/* Actions */}
              <div className="flex gap-2">
                <button
                  onClick={() => handleApproveQuote(quote.id)}
                  className="flex-1 bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg flex items-center justify-center gap-2"
                >
                  <CheckCircle className="w-4 h-4" />
                  Approve
                </button>
                <button
                  onClick={() => handleRejectQuote(quote.id)}
                  className="flex-1 bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg flex items-center justify-center gap-2"
                >
                  <XCircle className="w-4 h-4" />
                  Reject
                </button>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Approval Modal */}
      {showApprovalModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 max-w-md w-full mx-4">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Confirm Quote Approval</h3>
            <p className="text-gray-600 mb-6">
              Are you sure you want to approve this quote? This will create a work order and notify the merchant.
            </p>
            <div className="flex gap-4">
              <button
                onClick={() => setShowApprovalModal(false)}
                className="flex-1 bg-gray-100 hover:bg-gray-200 text-gray-700 px-4 py-2 rounded-lg"
              >
                Cancel
              </button>
              <button
                onClick={confirmApproval}
                className="flex-1 bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg"
              >
                Confirm Approval
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default QuotationComparisonPage;
