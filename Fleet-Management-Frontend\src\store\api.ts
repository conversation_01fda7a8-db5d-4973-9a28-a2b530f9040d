import { createApi, fetchBaseQuery, retry } from '@reduxjs/toolkit/query/react';
import type { RootState } from './index';

// Retry logic for failed requests
const staggeredBaseQuery = retry(
  fetchBaseQuery({
    baseUrl: '/api/v1',
    prepareHeaders: (headers, { getState }) => {
      const token = (getState() as RootState).auth.token;
      if (token) {
        headers.set('authorization', `Bearer ${token}`);
      }
      headers.set('content-type', 'application/json');
      headers.set('accept', 'application/json');
      return headers;
    },
    timeout: 15000, // 15 second timeout
  }),
  {
    maxRetries: 3
  }
);

export const api = createApi({
  reducerPath: 'api',
  baseQuery: staggeredBaseQuery,
  tagTypes: ['Vehicle', 'WorkOrder', 'User', 'Vendor', 'Invoice', 'Merchant', 'Driver', 'Inspector', 'Budget', 'Payment', 'FinancialSummary', 'Document'],
  keepUnusedDataFor: 300, // 5 minutes cache
  refetchOnMountOrArgChange: 30, // Refetch if data is older than 30 seconds
  refetchOnFocus: true,
  refetchOnReconnect: true,
  endpoints: () => ({}),
});
