import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Progress } from '@/components/ui/progress';
import { ArrowLeft, ArrowRight, User, Building2, Mail, Phone, Hash, UserCheck } from 'lucide-react';
import type { DriverFormStep1 } from '@/types/driver';

// Validation schema for Step 1
const step1Schema = z.object({
  firstName: z.string().min(2, 'First name must be at least 2 characters'),
  lastName: z.string().min(2, 'Last name must be at least 2 characters'),
  employeeId: z.string().min(3, 'Employee ID must be at least 3 characters'),
  department: z.string().min(1, 'Please select a department'),
  email: z.string().email('Please enter a valid email address'),
  phone: z.string().min(10, 'Phone number must be at least 10 digits'),
  status: z.enum(['active', 'inactive', 'probationary'], {
    required_error: 'Please select a driver status',
  }),
});

const AddDriverPage: React.FC = () => {
  const navigate = useNavigate();
  const [currentStep, setCurrentStep] = useState(1);
  const [formData, setFormData] = useState<Partial<DriverFormStep1>>({});

  const {
    register,
    handleSubmit,
    formState: { errors, isValid },
    setValue,
    watch,
    trigger,
  } = useForm<DriverFormStep1>({
    resolver: zodResolver(step1Schema),
    mode: 'onChange',
    defaultValues: formData,
  });

  const watchedValues = watch();

  const departments = [
    { value: 'operations', label: 'Operations' },
    { value: 'logistics', label: 'Logistics' },
    { value: 'maintenance', label: 'Maintenance' },
    { value: 'administration', label: 'Administration' },
    { value: 'security', label: 'Security' },
    { value: 'management', label: 'Management' },
  ];

  const statusOptions = [
    { value: 'active', label: 'Active', description: 'Driver can immediately start using the system' },
    { value: 'inactive', label: 'Inactive', description: 'Driver account created but not yet active' },
    { value: 'probationary', label: 'Probationary', description: 'Driver under probationary period with limited access' },
  ];

  const onSubmit = async (data: DriverFormStep1) => {
    setFormData(data);
    // For now, just show success - in next tasks we'll implement the full wizard
    navigate('/drivers/add/success', {
      state: {
        driverData: data,
        step: 1
      }
    });
  };

  const handleCancel = () => {
    navigate('/drivers');
  };

  const progressPercentage = (currentStep / 4) * 100;

  return (
    <div className="container mx-auto px-3 sm:px-4 py-4 sm:py-6 max-w-4xl">
      {/* Header */}
      <div className="mb-4 sm:mb-6">
        <Button
          variant="ghost"
          onClick={() => navigate('/drivers')}
          className="mb-3 sm:mb-4"
        >
          <ArrowLeft className="h-4 w-4 mr-2" />
          <span className="hidden sm:inline">Back to Drivers</span>
          <span className="sm:hidden">Back</span>
        </Button>

        <div>
          <h1 className="text-xl sm:text-2xl font-bold text-gray-900">Add New Driver</h1>
          <p className="text-sm sm:text-base text-gray-600">Register a new driver in the fleet management system</p>
        </div>
      </div>

      {/* Progress Indicator */}
      <Card className="mb-6">
        <CardContent className="p-4">
          <div className="flex items-center justify-between mb-2">
            <span className="text-sm font-medium text-gray-700">Step {currentStep} of 4</span>
            <span className="text-sm text-gray-500">{Math.round(progressPercentage)}% Complete</span>
          </div>
          <Progress value={progressPercentage} className="h-2" />
          <div className="flex justify-between mt-3 text-xs text-gray-500">
            <span className={currentStep >= 1 ? 'text-blue-600 font-medium' : ''}>Basic Information</span>
            <span className={currentStep >= 2 ? 'text-blue-600 font-medium' : ''}>License & Qualifications</span>
            <span className={currentStep >= 3 ? 'text-blue-600 font-medium' : ''}>Vehicle Assignment</span>
            <span className={currentStep >= 4 ? 'text-blue-600 font-medium' : ''}>System Access</span>
          </div>
        </CardContent>
      </Card>

      {/* Step 1: Basic Information */}
      <Card className="w-full">
        <CardHeader className="p-4 sm:p-6">
          <CardTitle className="text-lg sm:text-xl flex items-center gap-2">
            <User className="h-5 w-5 text-blue-600" />
            Basic Information
          </CardTitle>
          <p className="text-sm text-gray-600">Enter the driver's personal and employment details</p>
        </CardHeader>
        <CardContent className="p-4 sm:p-6">
          <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
            {/* Personal Information */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="firstName" className="flex items-center gap-2">
                  <User className="h-4 w-4" />
                  First Name *
                </Label>
                <Input
                  id="firstName"
                  {...register('firstName')}
                  placeholder="Enter first name"
                  className={errors.firstName ? 'border-red-500' : ''}
                />
                {errors.firstName && (
                  <p className="text-sm text-red-600">{errors.firstName.message}</p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="lastName" className="flex items-center gap-2">
                  <User className="h-4 w-4" />
                  Last Name *
                </Label>
                <Input
                  id="lastName"
                  {...register('lastName')}
                  placeholder="Enter last name"
                  className={errors.lastName ? 'border-red-500' : ''}
                />
                {errors.lastName && (
                  <p className="text-sm text-red-600">{errors.lastName.message}</p>
                )}
              </div>
            </div>

            {/* Employment Information */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="employeeId" className="flex items-center gap-2">
                  <Hash className="h-4 w-4" />
                  Employee ID *
                </Label>
                <Input
                  id="employeeId"
                  {...register('employeeId')}
                  placeholder="Enter employee ID"
                  className={errors.employeeId ? 'border-red-500' : ''}
                />
                {errors.employeeId && (
                  <p className="text-sm text-red-600">{errors.employeeId.message}</p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="department" className="flex items-center gap-2">
                  <Building2 className="h-4 w-4" />
                  Department *
                </Label>
                <Select
                  value={watchedValues.department || ''}
                  onValueChange={(value) => {
                    setValue('department', value);
                    trigger('department');
                  }}
                >
                  <SelectTrigger className={errors.department ? 'border-red-500' : ''}>
                    <SelectValue placeholder="Select department" />
                  </SelectTrigger>
                  <SelectContent>
                    {departments.map((dept) => (
                      <SelectItem key={dept.value} value={dept.value}>
                        {dept.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                {errors.department && (
                  <p className="text-sm text-red-600">{errors.department.message}</p>
                )}
              </div>
            </div>

            {/* Contact Information */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="email" className="flex items-center gap-2">
                  <Mail className="h-4 w-4" />
                  Email Address *
                </Label>
                <Input
                  id="email"
                  type="email"
                  {...register('email')}
                  placeholder="Enter email address"
                  className={errors.email ? 'border-red-500' : ''}
                />
                {errors.email && (
                  <p className="text-sm text-red-600">{errors.email.message}</p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="phone" className="flex items-center gap-2">
                  <Phone className="h-4 w-4" />
                  Phone Number *
                </Label>
                <Input
                  id="phone"
                  type="tel"
                  {...register('phone')}
                  placeholder="Enter phone number"
                  className={errors.phone ? 'border-red-500' : ''}
                />
                {errors.phone && (
                  <p className="text-sm text-red-600">{errors.phone.message}</p>
                )}
              </div>
            </div>

            {/* Driver Status */}
            <div className="space-y-2">
              <Label htmlFor="status" className="flex items-center gap-2">
                <UserCheck className="h-4 w-4" />
                Driver Status *
              </Label>
              <Select
                value={watchedValues.status || ''}
                onValueChange={(value) => {
                  setValue('status', value as 'active' | 'inactive' | 'probationary');
                  trigger('status');
                }}
              >
                <SelectTrigger className={errors.status ? 'border-red-500' : ''}>
                  <SelectValue placeholder="Select driver status" />
                </SelectTrigger>
                <SelectContent>
                  {statusOptions.map((status) => (
                    <SelectItem key={status.value} value={status.value}>
                      <div>
                        <div className="font-medium">{status.label}</div>
                        <div className="text-sm text-gray-500">{status.description}</div>
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              {errors.status && (
                <p className="text-sm text-red-600">{errors.status.message}</p>
              )}
            </div>

            {/* Form Actions */}
            <div className="flex flex-col sm:flex-row gap-3 pt-6 border-t">
              <Button
                type="button"
                variant="outline"
                onClick={handleCancel}
                className="flex items-center gap-2"
              >
                Cancel
              </Button>

              <div className="flex-1" />

              <Button
                type="submit"
                disabled={!isValid}
                className="flex items-center gap-2"
              >
                Next: License & Qualifications
                <ArrowRight className="h-4 w-4" />
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>
    </div>
  );
};

export default AddDriverPage;
