import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Progress } from '@/components/ui/progress';
import { Checkbox } from '@/components/ui/checkbox';
import {
  ArrowLeft,
  ArrowRight,
  User,
  Building2,
  Mail,
  Phone,
  Hash,
  UserCheck,
  FileText,
  Upload,
  Calendar,
  AlertTriangle,
  CheckCircle,
  X
} from 'lucide-react';
import type { DriverFormStep1, DriverFormStep2, DriverFormData } from '@/types/driver';
import { LICENSE_CLASSES } from '@/types/driver';

// Validation schemas for each step
const step1Schema = z.object({
  firstName: z.string().min(2, 'First name must be at least 2 characters'),
  lastName: z.string().min(2, 'Last name must be at least 2 characters'),
  employeeId: z.string().min(3, 'Employee ID must be at least 3 characters'),
  department: z.string().min(1, 'Please select a department'),
  email: z.string().email('Please enter a valid email address'),
  phone: z.string().min(10, 'Phone number must be at least 10 digits'),
  status: z.enum(['active', 'inactive', 'probationary'], {
    required_error: 'Please select a driver status',
  }),
});

const step2Schema = z.object({
  licenseNumber: z.string().min(5, 'License number must be at least 5 characters'),
  licenseClass: z.string().min(1, 'Please select a license class'),
  licenseExpiry: z.string().min(1, 'Please select license expiry date'),
  licenseEndorsements: z.array(z.string()).default([]),
  licenseRestrictions: z.array(z.string()).default([]),
  licenseDocument: z.any().optional(),
  certifications: z.array(z.string()).default([]),
  certificationDocuments: z.any().optional(),
});

const AddDriverPage: React.FC = () => {
  const navigate = useNavigate();
  const [currentStep, setCurrentStep] = useState(1);
  const [formData, setFormData] = useState<Partial<DriverFormData>>({});
  const [uploadedFiles, setUploadedFiles] = useState<{
    licenseDocument?: File;
    certificationDocuments?: File[];
  }>({});

  // Get the appropriate schema based on current step
  const getSchema = () => {
    switch (currentStep) {
      case 1: return step1Schema;
      case 2: return step2Schema;
      default: return step1Schema;
    }
  };

  const {
    register,
    handleSubmit,
    formState: { errors, isValid },
    setValue,
    watch,
    trigger,
    reset,
  } = useForm({
    resolver: zodResolver(getSchema()),
    mode: 'onChange',
    defaultValues: formData,
  });

  const watchedValues = watch();

  const departments = [
    { value: 'operations', label: 'Operations' },
    { value: 'logistics', label: 'Logistics' },
    { value: 'maintenance', label: 'Maintenance' },
    { value: 'administration', label: 'Administration' },
    { value: 'security', label: 'Security' },
    { value: 'management', label: 'Management' },
  ];

  const statusOptions = [
    { value: 'active', label: 'Active', description: 'Driver can immediately start using the system' },
    { value: 'inactive', label: 'Inactive', description: 'Driver account created but not yet active' },
    { value: 'probationary', label: 'Probationary', description: 'Driver under probationary period with limited access' },
  ];

  const endorsementOptions = [
    'Dangerous Goods',
    'Passenger Transport',
    'Heavy Vehicle',
    'Motorcycle',
    'Forklift',
    'Crane Operation',
  ];

  const restrictionOptions = [
    'Automatic Transmission Only',
    'Daylight Driving Only',
    'No Highway Driving',
    'Medical Restrictions',
    'Corrective Lenses Required',
  ];

  const certificationOptions = [
    'Defensive Driving',
    'First Aid',
    'Fire Safety',
    'Hazmat Handling',
    'Customer Service',
    'Fleet Safety',
  ];

  const onSubmit = async (data: any) => {
    const updatedFormData = { ...formData, ...data };
    setFormData(updatedFormData);

    if (currentStep < 4) {
      // Move to next step
      setCurrentStep(currentStep + 1);
      // Reset form with new schema and existing data
      setTimeout(() => {
        reset(updatedFormData);
      }, 0);
    } else {
      // Final submission
      navigate('/drivers/add/success', {
        state: {
          driverData: updatedFormData,
          step: 4,
          isComplete: true
        }
      });
    }
  };

  const handlePrevious = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
      setTimeout(() => {
        reset(formData);
      }, 0);
    }
  };

  const handleCancel = () => {
    navigate('/drivers');
  };

  const handleFileUpload = (file: File, type: 'license' | 'certification') => {
    if (type === 'license') {
      setUploadedFiles(prev => ({ ...prev, licenseDocument: file }));
      setValue('licenseDocument', file);
    } else {
      setUploadedFiles(prev => ({
        ...prev,
        certificationDocuments: [...(prev.certificationDocuments || []), file]
      }));
    }
  };

  const removeFile = (type: 'license' | 'certification', index?: number) => {
    if (type === 'license') {
      setUploadedFiles(prev => ({ ...prev, licenseDocument: undefined }));
      setValue('licenseDocument', undefined);
    } else if (index !== undefined) {
      setUploadedFiles(prev => ({
        ...prev,
        certificationDocuments: prev.certificationDocuments?.filter((_, i) => i !== index)
      }));
    }
  };

  const isLicenseExpiringSoon = (dateString: string) => {
    if (!dateString) return false;
    const expiry = new Date(dateString);
    const today = new Date();
    const diffTime = expiry.getTime() - today.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return diffDays <= 90; // Warning if expiring within 90 days
  };

  const progressPercentage = (currentStep / 4) * 100;

  return (
    <div className="container mx-auto px-3 sm:px-4 py-4 sm:py-6">
      {/* Header */}
      <div className="mb-4 sm:mb-6">
        <Button
          variant="ghost"
          onClick={() => navigate('/drivers')}
          className="mb-3 sm:mb-4"
        >
          <ArrowLeft className="h-4 w-4 mr-2" />
          <span className="hidden sm:inline">Back to Drivers</span>
          <span className="sm:hidden">Back</span>
        </Button>

        <div>
          <h1 className="text-xl sm:text-2xl font-bold text-gray-900">Add New Driver</h1>
          <p className="text-sm sm:text-base text-gray-600">Register a new driver in the fleet management system</p>
        </div>
      </div>

      {/* Progress Indicator */}
      <Card className="mb-6">
        <CardContent className="p-4">
          <div className="flex items-center justify-between mb-2">
            <span className="text-sm font-medium text-gray-700">Step {currentStep} of 4</span>
            <span className="text-sm text-gray-500">{Math.round(progressPercentage)}% Complete</span>
          </div>
          <Progress value={progressPercentage} className="h-2" />
          <div className="flex justify-between mt-3 text-xs text-gray-500">
            <span className={currentStep >= 1 ? 'text-blue-600 font-medium' : ''}>Basic Info</span>
            <span className={currentStep >= 2 ? 'text-blue-600 font-medium' : ''}>License</span>
            <span className={currentStep >= 3 ? 'text-blue-600 font-medium' : ''}>Vehicles</span>
            <span className={currentStep >= 4 ? 'text-blue-600 font-medium' : ''}>Access</span>
          </div>
        </CardContent>
      </Card>

      {/* Dynamic Step Content */}
      <Card className="w-full">
        <CardHeader className="p-4 sm:p-6">
          <CardTitle className="text-lg sm:text-xl flex items-center gap-2">
            {currentStep === 1 && <User className="h-5 w-5 text-blue-600" />}
            {currentStep === 2 && <FileText className="h-5 w-5 text-blue-600" />}
            {currentStep === 3 && <Building2 className="h-5 w-5 text-blue-600" />}
            {currentStep === 4 && <UserCheck className="h-5 w-5 text-blue-600" />}
            {currentStep === 1 && 'Basic Information'}
            {currentStep === 2 && 'License & Qualifications'}
            {currentStep === 3 && 'Vehicle Assignment & Permissions'}
            {currentStep === 4 && 'System Access & Review'}
          </CardTitle>
          <p className="text-sm text-gray-600">
            {currentStep === 1 && "Enter the driver's personal and employment details"}
            {currentStep === 2 && "Upload license documents and enter qualification details"}
            {currentStep === 3 && "Assign vehicles and configure driver permissions"}
            {currentStep === 4 && "Configure system access and review all information"}
          </p>
        </CardHeader>
        <CardContent className="p-4 sm:p-6">
          <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">

            {/* Step 1: Basic Information */}
            {currentStep === 1 && (
              <>
            {/* Personal Information */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="firstName" className="flex items-center gap-2">
                  <User className="h-4 w-4" />
                  First Name *
                </Label>
                <Input
                  id="firstName"
                  {...register('firstName')}
                  placeholder="Enter first name"
                  className={errors.firstName ? 'border-red-500' : ''}
                />
                {errors.firstName && (
                  <p className="text-sm text-red-600">{errors.firstName.message}</p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="lastName" className="flex items-center gap-2">
                  <User className="h-4 w-4" />
                  Last Name *
                </Label>
                <Input
                  id="lastName"
                  {...register('lastName')}
                  placeholder="Enter last name"
                  className={errors.lastName ? 'border-red-500' : ''}
                />
                {errors.lastName && (
                  <p className="text-sm text-red-600">{errors.lastName.message}</p>
                )}
              </div>
            </div>

            {/* Employment Information */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="employeeId" className="flex items-center gap-2">
                  <Hash className="h-4 w-4" />
                  Employee ID *
                </Label>
                <Input
                  id="employeeId"
                  {...register('employeeId')}
                  placeholder="Enter employee ID"
                  className={errors.employeeId ? 'border-red-500' : ''}
                />
                {errors.employeeId && (
                  <p className="text-sm text-red-600">{errors.employeeId.message}</p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="department" className="flex items-center gap-2">
                  <Building2 className="h-4 w-4" />
                  Department *
                </Label>
                <Select
                  value={watchedValues.department || ''}
                  onValueChange={(value) => {
                    setValue('department', value);
                    trigger('department');
                  }}
                >
                  <SelectTrigger className={errors.department ? 'border-red-500' : ''}>
                    <SelectValue placeholder="Select department" />
                  </SelectTrigger>
                  <SelectContent>
                    {departments.map((dept) => (
                      <SelectItem key={dept.value} value={dept.value}>
                        {dept.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                {errors.department && (
                  <p className="text-sm text-red-600">{errors.department.message}</p>
                )}
              </div>
            </div>

            {/* Contact Information */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="email" className="flex items-center gap-2">
                  <Mail className="h-4 w-4" />
                  Email Address *
                </Label>
                <Input
                  id="email"
                  type="email"
                  {...register('email')}
                  placeholder="Enter email address"
                  className={errors.email ? 'border-red-500' : ''}
                />
                {errors.email && (
                  <p className="text-sm text-red-600">{errors.email.message}</p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="phone" className="flex items-center gap-2">
                  <Phone className="h-4 w-4" />
                  Phone Number *
                </Label>
                <Input
                  id="phone"
                  type="tel"
                  {...register('phone')}
                  placeholder="Enter phone number"
                  className={errors.phone ? 'border-red-500' : ''}
                />
                {errors.phone && (
                  <p className="text-sm text-red-600">{errors.phone.message}</p>
                )}
              </div>
            </div>

            {/* Driver Status */}
            <div className="space-y-2">
              <Label htmlFor="status" className="flex items-center gap-2">
                <UserCheck className="h-4 w-4" />
                Driver Status *
              </Label>
              <Select
                value={watchedValues.status || ''}
                onValueChange={(value) => {
                  setValue('status', value as 'active' | 'inactive' | 'probationary');
                  trigger('status');
                }}
              >
                <SelectTrigger className={errors.status ? 'border-red-500' : ''}>
                  <SelectValue placeholder="Select driver status" />
                </SelectTrigger>
                <SelectContent>
                  {statusOptions.map((status) => (
                    <SelectItem key={status.value} value={status.value}>
                      <div>
                        <div className="font-medium">{status.label}</div>
                        <div className="text-sm text-gray-500">{status.description}</div>
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              {errors.status && (
                <p className="text-sm text-red-600">{errors.status.message}</p>
              )}
                </div>
              </>
            )}

            {/* Step 2: License & Qualifications */}
            {currentStep === 2 && (
              <>
                {/* License Information */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="licenseNumber" className="flex items-center gap-2">
                      <FileText className="h-4 w-4" />
                      License Number *
                    </Label>
                    <Input
                      id="licenseNumber"
                      {...register('licenseNumber')}
                      placeholder="Enter license number"
                      className={errors.licenseNumber ? 'border-red-500' : ''}
                    />
                    {errors.licenseNumber && (
                      <p className="text-sm text-red-600">{errors.licenseNumber.message}</p>
                    )}
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="licenseClass" className="flex items-center gap-2">
                      <FileText className="h-4 w-4" />
                      License Class *
                    </Label>
                    <Select
                      value={watchedValues.licenseClass || ''}
                      onValueChange={(value) => {
                        setValue('licenseClass', value);
                        trigger('licenseClass');
                      }}
                    >
                      <SelectTrigger className={errors.licenseClass ? 'border-red-500' : ''}>
                        <SelectValue placeholder="Select license class" />
                      </SelectTrigger>
                      <SelectContent>
                        {LICENSE_CLASSES.map((license) => (
                          <SelectItem key={license.code} value={license.code}>
                            <div>
                              <div className="font-medium">{license.name}</div>
                              <div className="text-sm text-gray-500">{license.description}</div>
                            </div>
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    {errors.licenseClass && (
                      <p className="text-sm text-red-600">{errors.licenseClass.message}</p>
                    )}
                  </div>
                </div>

                {/* License Expiry */}
                <div className="space-y-2">
                  <Label htmlFor="licenseExpiry" className="flex items-center gap-2">
                    <Calendar className="h-4 w-4" />
                    License Expiry Date *
                  </Label>
                  <Input
                    id="licenseExpiry"
                    type="date"
                    {...register('licenseExpiry')}
                    className={errors.licenseExpiry ? 'border-red-500' : ''}
                  />
                  {errors.licenseExpiry && (
                    <p className="text-sm text-red-600">{errors.licenseExpiry.message}</p>
                  )}
                  {watchedValues.licenseExpiry && isLicenseExpiringSoon(watchedValues.licenseExpiry) && (
                    <div className="flex items-center gap-2 text-orange-600 text-sm">
                      <AlertTriangle className="h-4 w-4" />
                      <span>License expires within 90 days</span>
                    </div>
                  )}
                </div>

                {/* License Document Upload */}
                <div className="space-y-2">
                  <Label className="flex items-center gap-2">
                    <Upload className="h-4 w-4" />
                    License Document *
                  </Label>
                  <div className="border-2 border-dashed border-gray-300 rounded-lg p-6">
                    {uploadedFiles.licenseDocument ? (
                      <div className="flex items-center justify-between bg-green-50 border border-green-200 rounded-lg p-3">
                        <div className="flex items-center gap-2">
                          <CheckCircle className="h-5 w-5 text-green-600" />
                          <span className="text-sm font-medium text-green-800">
                            {uploadedFiles.licenseDocument.name}
                          </span>
                        </div>
                        <Button
                          type="button"
                          variant="ghost"
                          size="sm"
                          onClick={() => removeFile('license')}
                        >
                          <X className="h-4 w-4" />
                        </Button>
                      </div>
                    ) : (
                      <div className="text-center">
                        <Upload className="mx-auto h-12 w-12 text-gray-400" />
                        <div className="mt-4">
                          <label htmlFor="license-upload" className="cursor-pointer">
                            <span className="mt-2 block text-sm font-medium text-gray-900">
                              Upload license scan
                            </span>
                            <span className="mt-1 block text-sm text-gray-500">
                              PNG, JPG, PDF up to 10MB
                            </span>
                          </label>
                          <input
                            id="license-upload"
                            type="file"
                            className="hidden"
                            accept=".png,.jpg,.jpeg,.pdf"
                            onChange={(e) => {
                              const file = e.target.files?.[0];
                              if (file) handleFileUpload(file, 'license');
                            }}
                          />
                        </div>
                      </div>
                    )}
                  </div>
                </div>

                {/* Endorsements */}
                <div className="space-y-3">
                  <Label className="text-base font-medium">License Endorsements</Label>
                  <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
                    {endorsementOptions.map((endorsement) => (
                      <div key={endorsement} className="flex items-center space-x-2">
                        <Checkbox
                          id={`endorsement-${endorsement}`}
                          checked={watchedValues.licenseEndorsements?.includes(endorsement) || false}
                          onCheckedChange={(checked) => {
                            const current = watchedValues.licenseEndorsements || [];
                            const updated = checked
                              ? [...current, endorsement]
                              : current.filter(item => item !== endorsement);
                            setValue('licenseEndorsements', updated);
                          }}
                        />
                        <Label
                          htmlFor={`endorsement-${endorsement}`}
                          className="text-sm font-normal cursor-pointer"
                        >
                          {endorsement}
                        </Label>
                      </div>
                    ))}
                  </div>
                </div>

                {/* Restrictions */}
                <div className="space-y-3">
                  <Label className="text-base font-medium">License Restrictions</Label>
                  <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
                    {restrictionOptions.map((restriction) => (
                      <div key={restriction} className="flex items-center space-x-2">
                        <Checkbox
                          id={`restriction-${restriction}`}
                          checked={watchedValues.licenseRestrictions?.includes(restriction) || false}
                          onCheckedChange={(checked) => {
                            const current = watchedValues.licenseRestrictions || [];
                            const updated = checked
                              ? [...current, restriction]
                              : current.filter(item => item !== restriction);
                            setValue('licenseRestrictions', updated);
                          }}
                        />
                        <Label
                          htmlFor={`restriction-${restriction}`}
                          className="text-sm font-normal cursor-pointer"
                        >
                          {restriction}
                        </Label>
                      </div>
                    ))}
                  </div>
                </div>

                {/* Additional Certifications */}
                <div className="space-y-3">
                  <Label className="text-base font-medium">Additional Certifications</Label>
                  <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
                    {certificationOptions.map((certification) => (
                      <div key={certification} className="flex items-center space-x-2">
                        <Checkbox
                          id={`cert-${certification}`}
                          checked={watchedValues.certifications?.includes(certification) || false}
                          onCheckedChange={(checked) => {
                            const current = watchedValues.certifications || [];
                            const updated = checked
                              ? [...current, certification]
                              : current.filter(item => item !== certification);
                            setValue('certifications', updated);
                          }}
                        />
                        <Label
                          htmlFor={`cert-${certification}`}
                          className="text-sm font-normal cursor-pointer"
                        >
                          {certification}
                        </Label>
                      </div>
                    ))}
                  </div>
                </div>

                {/* Certification Documents Upload */}
                <div className="space-y-2">
                  <Label className="flex items-center gap-2">
                    <Upload className="h-4 w-4" />
                    Certification Documents (Optional)
                  </Label>
                  <div className="border-2 border-dashed border-gray-300 rounded-lg p-4">
                    <div className="text-center">
                      <Upload className="mx-auto h-8 w-8 text-gray-400" />
                      <div className="mt-2">
                        <label htmlFor="cert-upload" className="cursor-pointer">
                          <span className="text-sm font-medium text-gray-900">
                            Upload certification documents
                          </span>
                          <span className="block text-sm text-gray-500">
                            Multiple files allowed
                          </span>
                        </label>
                        <input
                          id="cert-upload"
                          type="file"
                          multiple
                          className="hidden"
                          accept=".png,.jpg,.jpeg,.pdf"
                          onChange={(e) => {
                            const files = Array.from(e.target.files || []);
                            files.forEach(file => handleFileUpload(file, 'certification'));
                          }}
                        />
                      </div>
                    </div>
                    {uploadedFiles.certificationDocuments && uploadedFiles.certificationDocuments.length > 0 && (
                      <div className="mt-3 space-y-2">
                        {uploadedFiles.certificationDocuments.map((file, index) => (
                          <div key={index} className="flex items-center justify-between bg-blue-50 border border-blue-200 rounded-lg p-2">
                            <div className="flex items-center gap-2">
                              <CheckCircle className="h-4 w-4 text-blue-600" />
                              <span className="text-sm text-blue-800">{file.name}</span>
                            </div>
                            <Button
                              type="button"
                              variant="ghost"
                              size="sm"
                              onClick={() => removeFile('certification', index)}
                            >
                              <X className="h-4 w-4" />
                            </Button>
                          </div>
                        ))}
                      </div>
                    )}
                  </div>
                </div>
              </>
            )}

            {/* Form Actions */}
            <div className="flex flex-col sm:flex-row gap-3 pt-6 border-t">
              {currentStep > 1 && (
                <Button
                  type="button"
                  variant="outline"
                  onClick={handlePrevious}
                  className="flex items-center gap-2"
                >
                  <ArrowLeft className="h-4 w-4" />
                  Previous
                </Button>
              )}

              <Button
                type="button"
                variant="outline"
                onClick={handleCancel}
                className="flex items-center gap-2"
              >
                Cancel
              </Button>

              <div className="flex-1" />

              <Button
                type="submit"
                disabled={!isValid}
                className="flex items-center gap-2"
              >
                {currentStep === 4 ? 'Complete Registration' :
                 currentStep === 3 ? 'Next: System Access' :
                 currentStep === 2 ? 'Next: Vehicle Assignment' :
                 'Next: License & Qualifications'}
                {currentStep < 4 && <ArrowRight className="h-4 w-4" />}
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>
    </div>
  );
};

export default AddDriverPage;
