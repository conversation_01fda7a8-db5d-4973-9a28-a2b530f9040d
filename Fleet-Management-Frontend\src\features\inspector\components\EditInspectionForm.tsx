import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';

interface ScheduledInspection {
  id: string;
  type: 'vehicle' | 'merchant';
  subject: {
    name: string;
    registration?: string;
    contactPerson?: string;
  };
  date: string;
  time: string;
  duration: number;
  location: string;
  inspector: string;
  priority: 'Low' | 'Medium' | 'High' | 'Critical';
  status: 'Scheduled' | 'Confirmed' | 'In Progress' | 'Completed' | 'Cancelled' | 'Overdue';
  reason: string;
  phone: string;
}

interface EditInspectionFormProps {
  inspection: ScheduledInspection;
  onClose: () => void;
  onSave: (updatedInspection: ScheduledInspection) => void;
}

const EditInspectionForm: React.FC<EditInspectionFormProps> = ({
  inspection,
  onClose,
  onSave
}) => {
  const [formData, setFormData] = useState({
    subjectName: inspection.subject.name,
    registration: inspection.subject.registration || '',
    contactPerson: inspection.subject.contactPerson || '',
    phone: inspection.phone,
    date: inspection.date,
    time: inspection.time,
    duration: inspection.duration.toString(),
    location: inspection.location,
    inspector: inspection.inspector,
    priority: inspection.priority,
    status: inspection.status,
    reason: inspection.reason
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    const updatedInspection: ScheduledInspection = {
      ...inspection,
      subject: {
        name: formData.subjectName,
        registration: inspection.type === 'vehicle' ? formData.registration : undefined,
        contactPerson: inspection.type === 'merchant' ? formData.contactPerson : undefined
      },
      date: formData.date,
      time: formData.time,
      duration: parseInt(formData.duration),
      location: formData.location,
      inspector: formData.inspector,
      priority: formData.priority as ScheduledInspection['priority'],
      status: formData.status as ScheduledInspection['status'],
      reason: formData.reason,
      phone: formData.phone
    };

    onSave(updatedInspection);
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      {/* Subject Details */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div className="space-y-2">
          <Label htmlFor="subjectName">
            {inspection.type === 'vehicle' ? 'Vehicle Name' : 'Business Name'}
          </Label>
          <Input
            id="subjectName"
            value={formData.subjectName}
            onChange={(e) => setFormData({...formData, subjectName: e.target.value})}
            required
          />
        </div>

        {inspection.type === 'vehicle' ? (
          <div className="space-y-2">
            <Label htmlFor="registration">Registration Number</Label>
            <Input
              id="registration"
              value={formData.registration}
              onChange={(e) => setFormData({...formData, registration: e.target.value})}
            />
          </div>
        ) : (
          <div className="space-y-2">
            <Label htmlFor="contactPerson">Contact Person</Label>
            <Input
              id="contactPerson"
              value={formData.contactPerson}
              onChange={(e) => setFormData({...formData, contactPerson: e.target.value})}
            />
          </div>
        )}
      </div>

      {/* Contact & Inspector */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div className="space-y-2">
          <Label htmlFor="phone">Phone Number</Label>
          <Input
            id="phone"
            value={formData.phone}
            onChange={(e) => setFormData({...formData, phone: e.target.value})}
          />
        </div>

        <div className="space-y-2">
          <Label htmlFor="inspector">Inspector</Label>
          <Select value={formData.inspector} onValueChange={(value) => setFormData({...formData, inspector: value})}>
            <SelectTrigger>
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="John Doe">John Doe</SelectItem>
              <SelectItem value="Jane Smith">Jane Smith</SelectItem>
              <SelectItem value="Mike Johnson">Mike Johnson</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      {/* Date & Time */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div className="space-y-2">
          <Label htmlFor="date">Date</Label>
          <Input
            id="date"
            type="date"
            value={formData.date}
            onChange={(e) => setFormData({...formData, date: e.target.value})}
            required
          />
        </div>

        <div className="space-y-2">
          <Label htmlFor="time">Time</Label>
          <Input
            id="time"
            type="time"
            value={formData.time}
            onChange={(e) => setFormData({...formData, time: e.target.value})}
            required
          />
        </div>

        <div className="space-y-2">
          <Label htmlFor="duration">Duration (minutes)</Label>
          <Select value={formData.duration} onValueChange={(value) => setFormData({...formData, duration: value})}>
            <SelectTrigger>
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="30">30 minutes</SelectItem>
              <SelectItem value="45">45 minutes</SelectItem>
              <SelectItem value="60">1 hour</SelectItem>
              <SelectItem value="90">1.5 hours</SelectItem>
              <SelectItem value="120">2 hours</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      {/* Location */}
      <div className="space-y-2">
        <Label htmlFor="location">Location</Label>
        <Input
          id="location"
          value={formData.location}
          onChange={(e) => setFormData({...formData, location: e.target.value})}
          required
        />
      </div>

      {/* Priority & Status */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div className="space-y-2">
          <Label htmlFor="priority">Priority</Label>
          <Select value={formData.priority} onValueChange={(value: ScheduledInspection['priority']) => setFormData({...formData, priority: value})}>
            <SelectTrigger>
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="Low">Low</SelectItem>
              <SelectItem value="Medium">Medium</SelectItem>
              <SelectItem value="High">High</SelectItem>
              <SelectItem value="Critical">Critical</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div className="space-y-2">
          <Label htmlFor="status">Status</Label>
          <Select value={formData.status} onValueChange={(value: ScheduledInspection['status']) => setFormData({...formData, status: value})}>
            <SelectTrigger>
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="Scheduled">Scheduled</SelectItem>
              <SelectItem value="Confirmed">Confirmed</SelectItem>
              <SelectItem value="In Progress">In Progress</SelectItem>
              <SelectItem value="Completed">Completed</SelectItem>
              <SelectItem value="Cancelled">Cancelled</SelectItem>
              <SelectItem value="Overdue">Overdue</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      {/* Reason */}
      <div className="space-y-2">
        <Label htmlFor="reason">Inspection Reason</Label>
        <Textarea
          id="reason"
          value={formData.reason}
          onChange={(e) => setFormData({...formData, reason: e.target.value})}
          className="min-h-[80px]"
        />
      </div>

      {/* Actions */}
      <div className="flex space-x-4 pt-4">
        <Button type="button" variant="outline" onClick={onClose} className="flex-1">
          Cancel
        </Button>
        <Button type="submit" className="flex-1">
          Save Changes
        </Button>
      </div>
    </form>
  );
};

export default EditInspectionForm;


