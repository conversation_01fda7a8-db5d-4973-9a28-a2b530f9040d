import React, { useState } from 'react';
import { Search, Star, MapPin, Phone, Mail, Clock } from 'lucide-react';
import Modal from '../ui/Modal';

interface Merchant {
  id: string;
  name: string;
  contactPerson: string;
  email: string;
  phone: string;
  rating: number;
  location: string;
  specialties: string[];
  responseTime: string;
  completedJobs: number;
  distance: number;
}

interface AssignMerchantModalProps {
  isOpen: boolean;
  onClose: () => void;
  onAssign: (merchantId: string) => void;
  workOrderId: string;
  serviceType: string;
}

const AssignMerchantModal: React.FC<AssignMerchantModalProps> = ({
  isOpen,
  onClose,
  onAssign,
  workOrderId,
  serviceType
}) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedMerchant, setSelectedMerchant] = useState<string | null>(null);

  // Mock data
  const merchants: Merchant[] = [
    {
      id: '1',
      name: 'AutoZone Service Center',
      contactPerson: '<PERSON>',
      email: '<EMAIL>',
      phone: '+27 11 987 6543',
      rating: 4.5,
      location: 'Johannesburg CBD',
      specialties: ['Brake Service', 'Engine Repair', 'Transmission'],
      responseTime: '2 hours',
      completedJobs: 156,
      distance: 2.5
    },
    {
      id: '2',
      name: 'Quick Brake Pro',
      contactPerson: 'Sarah Williams',
      email: '<EMAIL>',
      phone: '+27 11 456 7890',
      rating: 4.8,
      location: 'Sandton',
      specialties: ['Brake Service', 'Suspension', 'Wheel Alignment'],
      responseTime: '1 hour',
      completedJobs: 203,
      distance: 5.2
    },
    {
      id: '3',
      name: 'City Motors Workshop',
      contactPerson: 'David Brown',
      email: '<EMAIL>',
      phone: '+27 11 234 5678',
      rating: 4.2,
      location: 'Pretoria',
      specialties: ['General Maintenance', 'Electrical', 'Air Conditioning'],
      responseTime: '3 hours',
      completedJobs: 89,
      distance: 15.8
    }
  ];

  const filteredMerchants = merchants.filter(merchant =>
    merchant.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    merchant.specialties.some(specialty => 
      specialty.toLowerCase().includes(searchTerm.toLowerCase())
    )
  );

  const handleAssign = () => {
    if (selectedMerchant) {
      onAssign(selectedMerchant);
      onClose();
    }
  };

  const renderStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, i) => (
      <Star
        key={i}
        className={`h-4 w-4 ${
          i < Math.floor(rating) ? 'text-yellow-400 fill-current' : 'text-gray-300'
        }`}
      />
    ));
  };

  return (
    <Modal isOpen={isOpen} onClose={onClose} title="Assign Merchant" size="lg">
      <div className="space-y-6">
        <div>
          <p className="text-sm text-gray-600 mb-4">
            Select a merchant for work order <span className="font-medium">{workOrderId}</span> - {serviceType}
          </p>
          
          {/* Search */}
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <input
              type="text"
              placeholder="Search merchants by name or specialty..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>
        </div>

        {/* Merchant List */}
        <div className="space-y-3 max-h-96 overflow-y-auto">
          {filteredMerchants.map((merchant) => (
            <div
              key={merchant.id}
              className={`p-4 border rounded-lg cursor-pointer transition-colors ${
                selectedMerchant === merchant.id
                  ? 'border-blue-500 bg-blue-50'
                  : 'border-gray-200 hover:border-gray-300'
              }`}
              onClick={() => setSelectedMerchant(merchant.id)}
            >
              <div className="flex items-start justify-between">
                <div className="flex-1">
                  <div className="flex items-center space-x-3 mb-2">
                    <h4 className="font-medium text-gray-900">{merchant.name}</h4>
                    <div className="flex items-center space-x-1">
                      {renderStars(merchant.rating)}
                      <span className="text-sm text-gray-600">({merchant.rating})</span>
                    </div>
                  </div>
                  
                  <div className="grid grid-cols-2 gap-4 text-sm text-gray-600">
                    <div className="space-y-1">
                      <div className="flex items-center space-x-2">
                        <Phone className="h-3 w-3" />
                        <span>{merchant.phone}</span>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Mail className="h-3 w-3" />
                        <span>{merchant.email}</span>
                      </div>
                      <div className="flex items-center space-x-2">
                        <MapPin className="h-3 w-3" />
                        <span>{merchant.location} ({merchant.distance}km)</span>
                      </div>
                    </div>
                    
                    <div className="space-y-1">
                      <div className="flex items-center space-x-2">
                        <Clock className="h-3 w-3" />
                        <span>Response: {merchant.responseTime}</span>
                      </div>
                      <div>
                        <span>Jobs completed: {merchant.completedJobs}</span>
                      </div>
                      <div>
                        <span>Contact: {merchant.contactPerson}</span>
                      </div>
                    </div>
                  </div>
                  
                  <div className="mt-2">
                    <div className="flex flex-wrap gap-1">
                      {merchant.specialties.map((specialty) => (
                        <span
                          key={specialty}
                          className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                            specialty === serviceType
                              ? 'bg-blue-100 text-blue-800'
                              : 'bg-gray-100 text-gray-800'
                          }`}
                        >
                          {specialty}
                        </span>
                      ))}
                    </div>
                  </div>
                </div>
                
                <div className="ml-4">
                  <input
                    type="radio"
                    checked={selectedMerchant === merchant.id}
                    onChange={() => setSelectedMerchant(merchant.id)}
                    className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300"
                  />
                </div>
              </div>
            </div>
          ))}
        </div>

        {filteredMerchants.length === 0 && (
          <div className="text-center py-8 text-gray-500">
            <p>No merchants found matching your search criteria.</p>
          </div>
        )}

        {/* Actions */}
        <div className="flex justify-end space-x-3 pt-4 border-t border-gray-200">
          <button
            onClick={onClose}
            className="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-lg transition-colors"
          >
            Cancel
          </button>
          <button
            onClick={handleAssign}
            disabled={!selectedMerchant}
            className="px-4 py-2 text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 rounded-lg transition-colors disabled:opacity-50"
          >
            Assign Merchant
          </button>
        </div>
      </div>
    </Modal>
  );
};

export default AssignMerchantModal;