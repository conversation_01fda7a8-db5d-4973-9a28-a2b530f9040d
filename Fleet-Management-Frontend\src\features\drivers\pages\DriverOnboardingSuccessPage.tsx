import React from 'react';
import { useNavigate, useSearchParams, useLocation } from 'react-router-dom';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { CheckCircle, User, Plus, ArrowLeft, AlertTriangle } from 'lucide-react';

const DriverOnboardingSuccessPage: React.FC = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const [searchParams] = useSearchParams();

  // Get data from navigation state (temporary for step 1) or search params (full flow)
  const stateData = location.state;
  const driverId = searchParams.get('driverId') || 'TEMP-' + Date.now();
  const driverName = searchParams.get('driverName') ||
    (stateData?.driverData ? `${stateData.driverData.firstName} ${stateData.driverData.lastName}` : null);

  const isPartialOnboarding = stateData?.step === 1;

  return (
    <div className="container mx-auto px-3 sm:px-4 py-4 sm:py-6">
      <div className="max-w-2xl mx-auto">
        {/* Success Card */}
        <Card className="w-full">
          <CardHeader className="text-center p-6">
            <div className="mx-auto w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mb-4">
              <CheckCircle className="h-8 w-8 text-green-600" />
            </div>
            <CardTitle className="text-2xl text-green-600">
              {isPartialOnboarding ? 'Step 1 Complete!' : 'Driver Successfully Added!'}
            </CardTitle>
          </CardHeader>
          <CardContent className="p-6 text-center">
            <div className="space-y-4">
              {isPartialOnboarding ? (
                <div className="space-y-3">
                  <p className="text-gray-600">
                    Basic information for {driverName || 'the driver'} has been saved.
                  </p>
                  <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                    <div className="flex items-center gap-2 text-yellow-800">
                      <AlertTriangle className="h-5 w-5" />
                      <p className="font-medium">Onboarding Incomplete</p>
                    </div>
                    <p className="text-sm text-yellow-700 mt-1">
                      This is a preview of Step 1. The full wizard with all 4 steps will be implemented in the next tasks.
                    </p>
                  </div>
                </div>
              ) : (
                <p className="text-gray-600">
                  {driverName || 'The driver'} has been successfully registered in the fleet management system.
                </p>
              )}
              
              {driverId && (
                <div className="bg-gray-50 p-4 rounded-lg">
                  <p className="text-sm text-gray-600">Driver ID</p>
                  <p className="text-lg font-mono font-semibold text-gray-900">{driverId}</p>
                </div>
              )}

              <div className="border-t pt-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">
                  {isPartialOnboarding ? 'What\'s Next' : 'Next Steps'}
                </h3>
                <div className="space-y-3 text-left">
                  {isPartialOnboarding ? (
                    <>
                      <div className="flex items-start gap-3">
                        <div className="w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                          <span className="text-xs font-semibold text-blue-600">2</span>
                        </div>
                        <div>
                          <p className="font-medium text-gray-900">License & Qualifications</p>
                          <p className="text-sm text-gray-600">Upload license documents and certifications</p>
                        </div>
                      </div>

                      <div className="flex items-start gap-3">
                        <div className="w-6 h-6 bg-gray-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                          <span className="text-xs font-semibold text-gray-600">3</span>
                        </div>
                        <div>
                          <p className="font-medium text-gray-900">Vehicle Assignment & Permissions</p>
                          <p className="text-sm text-gray-600">Assign vehicles and set driver permissions</p>
                        </div>
                      </div>

                      <div className="flex items-start gap-3">
                        <div className="w-6 h-6 bg-gray-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                          <span className="text-xs font-semibold text-gray-600">4</span>
                        </div>
                        <div>
                          <p className="font-medium text-gray-900">System Access & Review</p>
                          <p className="text-sm text-gray-600">Configure app access and complete onboarding</p>
                        </div>
                      </div>
                    </>
                  ) : (
                    <>
                      <div className="flex items-start gap-3">
                        <div className="w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                          <span className="text-xs font-semibold text-blue-600">1</span>
                        </div>
                        <div>
                          <p className="font-medium text-gray-900">Welcome email sent</p>
                          <p className="text-sm text-gray-600">Driver will receive login credentials and app download instructions</p>
                        </div>
                      </div>

                      <div className="flex items-start gap-3">
                        <div className="w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                          <span className="text-xs font-semibold text-blue-600">2</span>
                        </div>
                        <div>
                          <p className="font-medium text-gray-900">License verification in progress</p>
                          <p className="text-sm text-gray-600">System is verifying license details with transport authority</p>
                        </div>
                      </div>

                      <div className="flex items-start gap-3">
                        <div className="w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                          <span className="text-xs font-semibold text-blue-600">3</span>
                        </div>
                        <div>
                          <p className="font-medium text-gray-900">Vehicle assignments active</p>
                          <p className="text-sm text-gray-600">Driver can now book and operate assigned vehicles</p>
                        </div>
                      </div>
                    </>
                  )}
                </div>
              </div>

              <div className="flex flex-col sm:flex-row gap-3 pt-6">
                {isPartialOnboarding ? (
                  <>
                    <Button
                      onClick={() => navigate('/drivers/add')}
                      className="flex items-center gap-2"
                    >
                      <Plus className="h-4 w-4" />
                      Continue Full Onboarding
                    </Button>

                    <Button
                      variant="outline"
                      onClick={() => navigate('/drivers')}
                      className="flex items-center gap-2"
                    >
                      <ArrowLeft className="h-4 w-4" />
                      Back to Driver List
                    </Button>
                  </>
                ) : (
                  <>
                    <Button
                      onClick={() => navigate(`/drivers/${driverId}`)}
                      className="flex items-center gap-2"
                      disabled={!driverId}
                    >
                      <User className="h-4 w-4" />
                      View Driver Profile
                    </Button>

                    <Button
                      variant="outline"
                      onClick={() => navigate('/drivers/add')}
                      className="flex items-center gap-2"
                    >
                      <Plus className="h-4 w-4" />
                      Add Another Driver
                    </Button>

                    <Button
                      variant="outline"
                      onClick={() => navigate('/drivers')}
                      className="flex items-center gap-2"
                    >
                      <ArrowLeft className="h-4 w-4" />
                      Back to Driver List
                    </Button>
                  </>
                )}
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default DriverOnboardingSuccessPage;
