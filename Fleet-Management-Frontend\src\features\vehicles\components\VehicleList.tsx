
import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent } from '@/components/ui/card';
import { 
  Search, 
  Filter, 
  Grid, 
  List, 
  Plus,
  Download,
  Upload,
  RefreshCw
} from 'lucide-react';
import { VehicleCard } from './VehicleCard';
import { VehicleTable } from './VehicleTable';
import type { Vehicle, VehicleFilters } from '@/types/vehicle';

interface VehicleListProps {
  vehicles: Vehicle[];
  loading?: boolean;
  pagination?: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
  onView?: (id: string) => void;
  onEdit?: (id: string) => void;
  onAdd?: () => void;
  onScheduleService?: (id: string) => void;
  onExport?: () => void;
  onImport?: () => void;
  onFiltersChange?: (filters: VehicleFilters) => void;
  onPageChange?: (page: number) => void;
  onRefresh?: () => void;
}

export const VehicleList: React.FC<VehicleListProps> = ({
  vehicles,
  loading = false,
  pagination,
  onView,
  onEdit,
  onAdd,
  onScheduleService,
  onExport,
  onImport,
  onFiltersChange,
  onPageChange,
  onRefresh
}) => {
  const [viewMode, setViewMode] = useState<'grid' | 'table'>('grid');
  const [searchTerm, setSearchTerm] = useState('');
  const [filters, setFilters] = useState<VehicleFilters>({});
  const [showFilters, setShowFilters] = useState(false);

  const handleSearchChange = (value: string) => {
    setSearchTerm(value);
    const newFilters = { ...filters, search: value };
    setFilters(newFilters);
    onFiltersChange?.(newFilters);
  };

  const handleFilterChange = (key: keyof VehicleFilters, value: any) => {
    const newFilters = { ...filters, [key]: value };
    setFilters(newFilters);
    onFiltersChange?.(newFilters);
  };

  const clearFilters = () => {
    const clearedFilters = { search: searchTerm };
    setFilters(clearedFilters);
    onFiltersChange?.(clearedFilters);
  };

  return (
    <div className="space-y-4 sm:space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row gap-3 sm:gap-4 justify-between items-start sm:items-center">
        <div>
          <h1 className="text-xl sm:text-2xl font-bold text-gray-900">Vehicle Fleet</h1>
          <p className="text-sm sm:text-base text-gray-600">Manage your government vehicle fleet</p>
        </div>

        <div className="flex flex-wrap gap-2 w-full sm:w-auto">
          {onRefresh && (
            <Button variant="outline" onClick={onRefresh} disabled={loading} className="flex-1 sm:flex-none text-sm h-9 sm:h-10">
              <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
              <span className="hidden sm:inline">Refresh</span>
              <span className="sm:hidden">Refresh</span>
            </Button>
          )}
          {onImport && (
            <Button variant="outline" onClick={onImport} className="flex-1 sm:flex-none text-sm h-9 sm:h-10">
              <Upload className="h-4 w-4 mr-2" />
              <span className="hidden sm:inline">Import</span>
              <span className="sm:hidden">Import</span>
            </Button>
          )}
          {onExport && (
            <Button variant="outline" onClick={onExport} className="flex-1 sm:flex-none text-sm h-9 sm:h-10">
              <Download className="h-4 w-4 mr-2" />
              <span className="hidden sm:inline">Export</span>
              <span className="sm:hidden">Export</span>
            </Button>
          )}
          {onAdd && (
            <Button onClick={onAdd} className="flex-1 sm:flex-none text-sm h-9 sm:h-10">
              <Plus className="h-4 w-4 mr-2" />
              <span className="hidden sm:inline">Add Vehicle</span>
              <span className="sm:hidden">Add</span>
            </Button>
          )}
        </div>
      </div>

      {/* Search and Filters */}
      <Card>
        <CardContent className="p-4 sm:p-6">
          <div className="flex flex-col sm:flex-row gap-3 sm:gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  placeholder="Search vehicles..."
                  value={searchTerm}
                  onChange={(e) => handleSearchChange(e.target.value)}
                  className="pl-10 h-10 sm:h-9 text-base sm:text-sm"
                />
              </div>
            </div>

            <div className="flex gap-2">
              <Button
                variant="outline"
                onClick={() => setShowFilters(!showFilters)}
                className="flex-1 sm:flex-none h-10 sm:h-9 text-sm"
              >
                <Filter className="h-4 w-4 mr-2" />
                <span className="hidden sm:inline">Filters</span>
                <span className="sm:hidden">Filter</span>
              </Button>

              <div className="flex border rounded-md">
                <Button
                  variant={viewMode === 'grid' ? 'default' : 'ghost'}
                  size="sm"
                  onClick={() => setViewMode('grid')}
                  className="rounded-r-none h-10 sm:h-9 px-2 sm:px-3"
                >
                  <Grid className="h-4 w-4" />
                </Button>
                <Button
                  variant={viewMode === 'table' ? 'default' : 'ghost'}
                  size="sm"
                  onClick={() => setViewMode('table')}
                  className="rounded-l-none h-10 sm:h-9 px-2 sm:px-3"
                >
                  <List className="h-4 w-4" />
                </Button>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Results */}
      {loading ? (
        <div className="text-center py-8 sm:py-12">
          <p className="text-gray-500 text-sm sm:text-base">Loading vehicles...</p>
        </div>
      ) : (
        <>
          {/* Results Count and Pagination Info */}
          <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-3 text-xs sm:text-sm text-gray-600">
            <div>
              Showing {vehicles.length} vehicle{vehicles.length !== 1 ? 's' : ''}
              {pagination && ` of ${pagination.total} total`}
            </div>
            {pagination && pagination.totalPages > 1 && (
              <div className="flex gap-2 justify-center sm:justify-end">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => onPageChange?.(pagination.page - 1)}
                  disabled={pagination.page <= 1}
                  className="h-8 px-3 text-xs"
                >
                  <span className="hidden sm:inline">Previous</span>
                  <span className="sm:hidden">Prev</span>
                </Button>
                <span className="px-2 sm:px-3 py-1 text-xs sm:text-sm">
                  Page {pagination.page} of {pagination.totalPages}
                </span>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => onPageChange?.(pagination.page + 1)}
                  disabled={pagination.page >= pagination.totalPages}
                  className="h-8 px-3 text-xs"
                >
                  Next
                </Button>
              </div>
            )}
          </div>

          {/* Vehicle Display */}
          {viewMode === 'grid' ? (
            <div className="w-full grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6">
              {vehicles.map((vehicle) => (
                <VehicleCard
                  key={vehicle.id}
                  vehicle={vehicle}
                  onView={onView}
                  onEdit={onEdit}
                  onScheduleService={onScheduleService}
                />
              ))}
            </div>
          ) : (
            <Card className="w-full">
              <VehicleTable
                vehicles={vehicles}
                onView={onView}
                onEdit={onEdit}
                onScheduleService={onScheduleService}
              />
            </Card>
          )}

          {/* Empty State */}
          {vehicles.length === 0 && !loading && (
            <div className="text-center py-12">
              <p className="text-gray-500 mb-4">No vehicles found</p>
              {onAdd && (
                <Button onClick={onAdd}>
                  <Plus className="h-4 w-4 mr-2" />
                  Add First Vehicle
                </Button>
              )}
            </div>
          )}
        </>
      )}
    </div>
  );
};



