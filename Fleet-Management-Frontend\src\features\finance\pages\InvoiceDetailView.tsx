import React, { useState } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import {
  ArrowLeft,
  Download,
  CheckCircle,
  XCircle,
  Clock,
  AlertTriangle,
  FileText,
  User,
  Calendar,
  DollarSign,
  MessageSquare,
  Paperclip,
  Eye
} from 'lucide-react';
import { useNotifications } from '@/hooks/useNotifications';

interface InvoiceDetail {
  id: string;
  invoiceNumber: string;
  vendor: {
    name: string;
    email: string;
    phone: string;
    address: string;
  };
  workOrder: {
    id: string;
    description: string;
    vehicle: string;
    requestedBy: string;
  };
  amount: number;
  taxAmount: number;
  totalAmount: number;
  status: 'pending' | 'approved' | 'paid' | 'rejected' | 'overdue';
  submittedDate: string;
  dueDate: string;
  approvedDate?: string;
  paidDate?: string;
  description: string;
  department: string;
  lineItems: Array<{
    description: string;
    quantity: number;
    unitPrice: number;
    total: number;
  }>;
  attachments: Array<{
    name: string;
    type: string;
    size: string;
    url: string;
  }>;
  approvalHistory: Array<{
    action: string;
    user: string;
    date: string;
    comment?: string;
  }>;
}

const InvoiceDetailView: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const { showSuccess, showError } = useNotifications();
  const [comment, setComment] = useState('');
  const [showApprovalModal, setShowApprovalModal] = useState(false);
  const [actionType, setActionType] = useState<'approve' | 'reject'>('approve');

  // Mock data - replace with actual API call
  const invoice: InvoiceDetail = {
    id: '1',
    invoiceNumber: 'INV-2025-001',
    vendor: {
      name: 'AutoFix Garage',
      email: '<EMAIL>',
      phone: '+27 11 123 4567',
      address: '123 Workshop Street, Johannesburg, 2001'
    },
    workOrder: {
      id: 'WO-2025-045',
      description: 'Engine repair and maintenance',
      vehicle: 'Toyota Hilux - GP123ABC',
      requestedBy: 'John Smith (Transport Officer)'
    },
    amount: 38500,
    taxAmount: 5775,
    totalAmount: 44275,
    status: 'pending',
    submittedDate: '2025-01-15',
    dueDate: '2025-01-30',
    description: 'Engine repair and maintenance services',
    department: 'Transport Dept A',
    lineItems: [
      { description: 'Engine oil change', quantity: 1, unitPrice: 850, total: 850 },
      { description: 'Oil filter replacement', quantity: 1, unitPrice: 320, total: 320 },
      { description: 'Spark plugs (set of 4)', quantity: 1, unitPrice: 1200, total: 1200 },
      { description: 'Engine diagnostic', quantity: 2, unitPrice: 750, total: 1500 },
      { description: 'Labor - Engine repair', quantity: 8, unitPrice: 450, total: 3600 },
      { description: 'Timing belt replacement', quantity: 1, unitPrice: 2800, total: 2800 },
      { description: 'Coolant system flush', quantity: 1, unitPrice: 650, total: 650 }
    ],
    attachments: [
      { name: 'invoice_INV-2025-001.pdf', type: 'PDF', size: '2.4 MB', url: '#' },
      { name: 'work_order_photos.zip', type: 'ZIP', size: '15.8 MB', url: '#' },
      { name: 'parts_receipt.pdf', type: 'PDF', size: '1.2 MB', url: '#' }
    ],
    approvalHistory: [
      { action: 'Submitted', user: 'AutoFix Garage', date: '2025-01-15 09:30', comment: 'Invoice submitted for work order WO-2025-045' },
      { action: 'Under Review', user: 'Sarah Johnson (Finance)', date: '2025-01-15 14:20', comment: 'Invoice received and under review' }
    ]
  };

  const handleApproval = (action: 'approve' | 'reject') => {
    setActionType(action);
    setShowApprovalModal(true);
  };

  const confirmAction = () => {
    if (actionType === 'approve') {
      showSuccess('Invoice Approved', 'Invoice has been approved and forwarded for payment processing.');
    } else {
      showError('Invoice Rejected', 'Invoice has been rejected and vendor has been notified.');
    }
    setShowApprovalModal(false);
    setComment('');
    navigate('/finance/invoices');
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending': return 'bg-yellow-100 text-yellow-800';
      case 'approved': return 'bg-blue-100 text-blue-800';
      case 'paid': return 'bg-green-100 text-green-800';
      case 'rejected': return 'bg-red-100 text-red-800';
      case 'overdue': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <button
            onClick={() => navigate('/finance/invoices')}
            className="p-2 hover:bg-gray-100 rounded-lg"
          >
            <ArrowLeft className="h-5 w-5" />
          </button>
          <div>
            <h1 className="text-2xl font-bold text-gray-900">{invoice.invoiceNumber}</h1>
            <p className="text-gray-600">{invoice.description}</p>
          </div>
        </div>
        <div className="flex items-center space-x-3">
          <span className={`px-3 py-1 rounded-full text-sm font-medium ${getStatusColor(invoice.status)}`}>
            {invoice.status.charAt(0).toUpperCase() + invoice.status.slice(1)}
          </span>
          <button className="px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 flex items-center">
            <Download className="h-4 w-4 mr-2" />
            Download
          </button>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Main Content */}
        <div className="lg:col-span-2 space-y-6">
          {/* Invoice Summary */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Invoice Summary</h3>
            <div className="grid grid-cols-2 gap-6">
              <div>
                <h4 className="font-medium text-gray-900 mb-2">Vendor Information</h4>
                <div className="space-y-1 text-sm text-gray-600">
                  <p className="font-medium text-gray-900">{invoice.vendor.name}</p>
                  <p>{invoice.vendor.email}</p>
                  <p>{invoice.vendor.phone}</p>
                  <p>{invoice.vendor.address}</p>
                </div>
              </div>
              <div>
                <h4 className="font-medium text-gray-900 mb-2">Work Order Details</h4>
                <div className="space-y-1 text-sm text-gray-600">
                  <p><span className="font-medium">ID:</span> {invoice.workOrder.id}</p>
                  <p><span className="font-medium">Vehicle:</span> {invoice.workOrder.vehicle}</p>
                  <p><span className="font-medium">Requested by:</span> {invoice.workOrder.requestedBy}</p>
                  <p><span className="font-medium">Department:</span> {invoice.department}</p>
                </div>
              </div>
            </div>
          </div>

          {/* Line Items */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Line Items</h3>
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">Description</th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">Qty</th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">Unit Price</th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">Total</th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {invoice.lineItems.map((item, index) => (
                    <tr key={index}>
                      <td className="px-4 py-3 text-sm text-gray-900">{item.description}</td>
                      <td className="px-4 py-3 text-sm text-gray-900">{item.quantity}</td>
                      <td className="px-4 py-3 text-sm text-gray-900">R {item.unitPrice.toLocaleString()}</td>
                      <td className="px-4 py-3 text-sm font-medium text-gray-900">R {item.total.toLocaleString()}</td>
                    </tr>
                  ))}
                </tbody>
                <tfoot className="bg-gray-50">
                  <tr>
                    <td colSpan={3} className="px-4 py-3 text-sm font-medium text-gray-900 text-right">Subtotal:</td>
                    <td className="px-4 py-3 text-sm font-medium text-gray-900">R {invoice.amount.toLocaleString()}</td>
                  </tr>
                  <tr>
                    <td colSpan={3} className="px-4 py-3 text-sm font-medium text-gray-900 text-right">VAT (15%):</td>
                    <td className="px-4 py-3 text-sm font-medium text-gray-900">R {invoice.taxAmount.toLocaleString()}</td>
                  </tr>
                  <tr>
                    <td colSpan={3} className="px-4 py-3 text-lg font-bold text-gray-900 text-right">Total:</td>
                    <td className="px-4 py-3 text-lg font-bold text-gray-900">R {invoice.totalAmount.toLocaleString()}</td>
                  </tr>
                </tfoot>
              </table>
            </div>
          </div>

          {/* Attachments */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Attachments</h3>
            <div className="space-y-3">
              {invoice.attachments.map((attachment, index) => (
                <div key={index} className="flex items-center justify-between p-3 border border-gray-200 rounded-lg">
                  <div className="flex items-center">
                    <Paperclip className="h-5 w-5 text-gray-400 mr-3" />
                    <div>
                      <p className="text-sm font-medium text-gray-900">{attachment.name}</p>
                      <p className="text-xs text-gray-500">{attachment.type} • {attachment.size}</p>
                    </div>
                  </div>
                  <button className="text-blue-600 hover:text-blue-700">
                    <Eye className="h-4 w-4" />
                  </button>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          {/* Actions */}
          {invoice.status === 'pending' && (
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Actions</h3>
              <div className="space-y-3">
                <button
                  onClick={() => handleApproval('approve')}
                  className="w-full px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 flex items-center justify-center"
                >
                  <CheckCircle className="h-4 w-4 mr-2" />
                  Approve Invoice
                </button>
                <button
                  onClick={() => handleApproval('reject')}
                  className="w-full px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 flex items-center justify-center"
                >
                  <XCircle className="h-4 w-4 mr-2" />
                  Reject Invoice
                </button>
                <button className="w-full px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 flex items-center justify-center">
                  <Clock className="h-4 w-4 mr-2" />
                  Hold for Review
                </button>
              </div>
            </div>
          )}

          {/* Invoice Details */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Invoice Details</h3>
            <div className="space-y-3 text-sm">
              <div className="flex justify-between">
                <span className="text-gray-600">Submitted:</span>
                <span className="text-gray-900">{invoice.submittedDate}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Due Date:</span>
                <span className="text-gray-900">{invoice.dueDate}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Amount:</span>
                <span className="font-medium text-gray-900">R {invoice.totalAmount.toLocaleString()}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Department:</span>
                <span className="text-gray-900">{invoice.department}</span>
              </div>
            </div>
          </div>

          {/* Approval History */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Approval History</h3>
            <div className="space-y-4">
              {invoice.approvalHistory.map((entry, index) => (
                <div key={index} className="border-l-2 border-blue-200 pl-4">
                  <div className="flex items-center justify-between">
                    <span className="font-medium text-gray-900">{entry.action}</span>
                    <span className="text-xs text-gray-500">{entry.date}</span>
                  </div>
                  <p className="text-sm text-gray-600">{entry.user}</p>
                  {entry.comment && (
                    <p className="text-sm text-gray-500 mt-1">{entry.comment}</p>
                  )}
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* Approval Modal */}
      {showApprovalModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-md">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">
              {actionType === 'approve' ? 'Approve Invoice' : 'Reject Invoice'}
            </h3>
            <p className="text-gray-600 mb-4">
              {actionType === 'approve' 
                ? 'Are you sure you want to approve this invoice? It will be forwarded for payment processing.'
                : 'Please provide a reason for rejecting this invoice.'
              }
            </p>
            <textarea
              value={comment}
              onChange={(e) => setComment(e.target.value)}
              placeholder={actionType === 'approve' ? 'Optional comment...' : 'Reason for rejection...'}
              className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              rows={3}
            />
            <div className="flex justify-end space-x-3 mt-4">
              <button
                onClick={() => setShowApprovalModal(false)}
                className="px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50"
              >
                Cancel
              </button>
              <button
                onClick={confirmAction}
                className={`px-4 py-2 rounded-lg text-white ${
                  actionType === 'approve' 
                    ? 'bg-green-600 hover:bg-green-700' 
                    : 'bg-red-600 hover:bg-red-700'
                }`}
              >
                {actionType === 'approve' ? 'Approve' : 'Reject'}
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default InvoiceDetailView;