import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import {
  Search,
  Link,
  Unlink,
  Star,
  MapPin,
  Phone,
  Mail,
  Users,
  CheckCircle,
  AlertTriangle
} from 'lucide-react';
import { MerchantService } from '@/services/merchantService';
import { useNotifications } from '@/hooks/useNotifications';
import type { Merchant } from '@/types/merchant';

const MerchantLinker: React.FC = () => {
  const [merchants, setMerchants] = useState<Merchant[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedRegion, setSelectedRegion] = useState<string>('all');
  const [selectedMerchants, setSelectedMerchants] = useState<string[]>([]);
  const [linking, setLinking] = useState(false);
  const { showSuccess, showError } = useNotifications();

  useEffect(() => {
    loadMerchants();
  }, []);

  const loadMerchants = async () => {
    try {
      setLoading(true);
      const data = await MerchantService.fetchMerchants();
      setMerchants(data);
    } catch (error) {
      showError('Failed to load merchants');
    } finally {
      setLoading(false);
    }
  };

  const filteredMerchants = merchants.filter(merchant => {
    const matchesSearch = merchant.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         merchant.address.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesRegion = selectedRegion === 'all' || merchant.region === selectedRegion;
    return matchesSearch && matchesRegion;
  });

  const regions = Array.from(new Set(merchants.map(m => m.region)));

  const handleSelectMerchant = (merchantId: string) => {
    setSelectedMerchants(prev =>
      prev.includes(merchantId)
        ? prev.filter(id => id !== merchantId)
        : [...prev, merchantId]
    );
  };

  const handleSelectAll = () => {
    if (selectedMerchants.length === filteredMerchants.length) {
      setSelectedMerchants([]);
    } else {
      setSelectedMerchants(filteredMerchants.map(m => m.id));
    }
  };

  const handleLinkMerchants = async () => {
    if (selectedMerchants.length === 0) return;

    try {
      setLinking(true);
      await MerchantService.linkMerchants(selectedMerchants);
      showSuccess(`Successfully linked ${selectedMerchants.length} merchants`);
      setSelectedMerchants([]);
    } catch (error) {
      showError('Failed to link merchants');
    } finally {
      setLinking(false);
    }
  };

  const handleUnlinkMerchants = async () => {
    if (selectedMerchants.length === 0) return;

    try {
      setLinking(true);
      await MerchantService.unlinkMerchants(selectedMerchants);
      showSuccess(`Successfully unlinked ${selectedMerchants.length} merchants`);
      setSelectedMerchants([]);
    } catch (error) {
      showError('Failed to unlink merchants');
    } finally {
      setLinking(false);
    }
  };

  const getStatusColor = (status: Merchant['status']) => {
    switch (status) {
      case 'active':
        return 'bg-green-100 text-green-800';
      case 'inactive':
        return 'bg-gray-100 text-gray-800';
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusIcon = (status: Merchant['status']) => {
    switch (status) {
      case 'active':
        return <CheckCircle className="h-4 w-4 text-green-600" />;
      case 'inactive':
        return <AlertTriangle className="h-4 w-4 text-gray-600" />;
      case 'pending':
        return <AlertTriangle className="h-4 w-4 text-yellow-600" />;
      default:
        return <AlertTriangle className="h-4 w-4 text-gray-600" />;
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-gray-500">Loading merchants...</div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-6 space-y-6 max-w-7xl">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Merchant Linker</h1>
          <p className="text-gray-600">Link and manage merchant partnerships</p>
        </div>
        <div className="flex gap-2">
          <Button
            onClick={handleLinkMerchants}
            disabled={selectedMerchants.length === 0 || linking}
            className="bg-green-600 hover:bg-green-700"
          >
            <Link className="h-4 w-4 mr-2" />
            Link ({selectedMerchants.length})
          </Button>
          <Button
            onClick={handleUnlinkMerchants}
            disabled={selectedMerchants.length === 0 || linking}
            variant="outline"
            className="border-red-300 text-red-600 hover:bg-red-50"
          >
            <Unlink className="h-4 w-4 mr-2" />
            Unlink ({selectedMerchants.length})
          </Button>
        </div>
      </div>

      {/* Filters */}
      <Card>
        <CardContent className="p-4">
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  placeholder="Search merchants..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            <div className="sm:w-48">
              <select
                value={selectedRegion}
                onChange={(e) => setSelectedRegion(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="all">All Regions</option>
                {regions.map(region => (
                  <option key={region} value={region}>{region}</option>
                ))}
              </select>
            </div>
            <Button
              onClick={handleSelectAll}
              variant="outline"
              className="sm:w-auto"
            >
              {selectedMerchants.length === filteredMerchants.length ? 'Deselect All' : 'Select All'}
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Merchants Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {filteredMerchants.map((merchant) => (
          <Card
            key={merchant.id}
            className={`cursor-pointer transition-all hover:shadow-md ${
              selectedMerchants.includes(merchant.id)
                ? 'ring-2 ring-blue-500 bg-blue-50'
                : 'hover:shadow-md'
            }`}
            onClick={() => handleSelectMerchant(merchant.id)}
          >
            <CardHeader className="pb-3">
              <div className="flex items-start justify-between">
                <div className="flex items-center space-x-2">
                  <Users className="h-5 w-5 text-blue-700" />
                  <CardTitle className="text-lg">{merchant.name}</CardTitle>
                </div>
                <div className="flex items-center space-x-1">
                  {getStatusIcon(merchant.status)}
                  <Badge className={getStatusColor(merchant.status)}>
                    {merchant.status}
                  </Badge>
                </div>
              </div>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <div className="flex items-center space-x-2 text-sm text-gray-600">
                  <MapPin className="h-4 w-4" />
                  <span>{merchant.region}</span>
                </div>
                <div className="flex items-center space-x-2 text-sm text-gray-600">
                  <Phone className="h-4 w-4" />
                  <span>{merchant.phone}</span>
                </div>
                <div className="flex items-center space-x-2 text-sm text-gray-600">
                  <Mail className="h-4 w-4" />
                  <span>{merchant.email}</span>
                </div>
                <div className="flex items-center space-x-2 text-sm text-gray-600">
                  <Star className="h-4 w-4 fill-yellow-400 text-yellow-400" />
                  <span>{merchant.rating.toFixed(1)} rating</span>
                </div>
              </div>

              <div className="space-y-2">
                <div className="text-sm font-medium text-gray-700">Specialties:</div>
                <div className="flex flex-wrap gap-1">
                  {merchant.specialty.slice(0, 2).map((spec, index) => (
                    <span
                      key={index}
                      className="bg-blue-100 text-blue-800 px-2 py-1 rounded text-xs"
                    >
                      {spec}
                    </span>
                  ))}
                  {merchant.specialty.length > 2 && (
                    <span className="text-xs text-gray-500">
                      +{merchant.specialty.length - 2} more
                    </span>
                  )}
                </div>
              </div>

              <div className="flex justify-between text-sm text-gray-600">
                <span>Capacity: {merchant.capacity}</span>
                <span>{merchant.distance.toFixed(1)} km away</span>
              </div>

              <div className="space-y-1">
                <div className="text-sm font-medium text-gray-700">Certifications:</div>
                <div className="flex flex-wrap gap-1">
                  {merchant.certifications.map((cert, index) => (
                    <span
                      key={index}
                      className="bg-green-100 text-green-800 px-2 py-1 rounded text-xs"
                    >
                      {cert}
                    </span>
                  ))}
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Empty State */}
      {filteredMerchants.length === 0 && (
        <div className="text-center py-12">
          <Users className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <p className="text-gray-500 mb-4">No merchants found</p>
          <p className="text-sm text-gray-400">
            Try adjusting your search criteria or region filter
          </p>
        </div>
      )}

      {/* Summary */}
      <Card>
        <CardContent className="p-4">
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
            <div className="text-sm text-gray-600">
              Showing {filteredMerchants.length} of {merchants.length} merchants
              {selectedMerchants.length > 0 && (
                <span className="ml-2 font-medium">
                  • {selectedMerchants.length} selected
                </span>
              )}
            </div>
            {selectedMerchants.length > 0 && (
              <div className="flex gap-2">
                <Button
                  onClick={handleLinkMerchants}
                  disabled={linking}
                  size="sm"
                  className="bg-green-600 hover:bg-green-700"
                >
                  <Link className="h-4 w-4 mr-2" />
                  Link Selected
                </Button>
                <Button
                  onClick={handleUnlinkMerchants}
                  disabled={linking}
                  size="sm"
                  variant="outline"
                  className="border-red-300 text-red-600 hover:bg-red-50"
                >
                  <Unlink className="h-4 w-4 mr-2" />
                  Unlink Selected
                </Button>
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default MerchantLinker;