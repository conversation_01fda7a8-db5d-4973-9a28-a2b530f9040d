import React, { useState, useCallback } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  Upload,
  FileText,
  CheckCircle,
  AlertCircle,
  ArrowLeft,
  X,
  Eye,
  Download,
  Shield,
  MapPin,
  CreditCard
} from 'lucide-react';

interface DocumentStatus {
  id: string;
  name: string;
  description: string;
  required: boolean;
  file: File | null;
  status: 'not_uploaded' | 'uploaded' | 'rejected';
  rejectionReason?: string;
  icon: React.ReactNode;
}

const DocumentUploadSection: React.FC = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const customerData = location.state?.customerData;
  const fleetData = location.state?.fleetData;

  // Redirect if required data is missing
  React.useEffect(() => {
    if (!customerData || !fleetData) {
      navigate('/fleet/customers', { replace: true });
    }
  }, [customerData, fleetData, navigate]);

  const [documents, setDocuments] = useState<DocumentStatus[]>([
    {
      id: 'ownership_certificate',
      name: 'Ownership Certificate',
      description: 'Official certificate of vehicle ownership from the relevant authority',
      required: true,
      file: null,
      status: 'not_uploaded',
      icon: <Shield className="h-5 w-5 text-blue-600" />
    },
    {
      id: 'location_map',
      name: 'Location Map',
      description: 'Detailed map showing department locations and operational areas',
      required: true,
      file: null,
      status: 'not_uploaded',
      icon: <MapPin className="h-5 w-5 text-green-600" />
    },
    {
      id: 'insurance_proof',
      name: 'Insurance Proof',
      description: 'Current insurance certificates for all vehicles in the fleet',
      required: true,
      file: null,
      status: 'not_uploaded',
      icon: <CreditCard className="h-5 w-5 text-purple-600" />
    },
    {
      id: 'registration_documents',
      name: 'Registration Documents',
      description: 'Vehicle registration documents and license renewals',
      required: false,
      file: null,
      status: 'not_uploaded',
      icon: <FileText className="h-5 w-5 text-orange-600" />
    },
    {
      id: 'maintenance_records',
      name: 'Maintenance Records',
      description: 'Historical maintenance and service records (if available)',
      required: false,
      file: null,
      status: 'not_uploaded',
      icon: <FileText className="h-5 w-5 text-gray-600" />
    }
  ]);

  const [dragActiveId, setDragActiveId] = useState<string | null>(null);

  const handleDrag = useCallback((e: React.DragEvent, documentId: string) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === "dragenter" || e.type === "dragover") {
      setDragActiveId(documentId);
    } else if (e.type === "dragleave") {
      setDragActiveId(null);
    }
  }, []);

  const handleDrop = useCallback((e: React.DragEvent, documentId: string) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActiveId(null);
    
    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      handleFileUpload(e.dataTransfer.files[0], documentId);
    }
  }, []);

  const handleFileUpload = async (file: File, documentId: string) => {
    if (!file) return;

    const allowedTypes = [
      'application/pdf',
      'image/jpeg',
      'image/png',
      'image/jpg',
      'application/msword',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
    ];

    if (!allowedTypes.includes(file.type)) {
      alert('Please upload a valid file (PDF, JPG, PNG, DOC, DOCX)');
      return;
    }

    if (file.size > 10 * 1024 * 1024) { // 10MB limit
      alert('File size must be less than 10MB');
      return;
    }

    setDocuments(prev => prev.map(doc => 
      doc.id === documentId 
        ? { ...doc, file, status: 'uploaded' as const }
        : doc
    ));

    // Simulate validation
    setTimeout(() => {
      // Randomly reject some documents for demo
      const shouldReject = Math.random() < 0.2;
      if (shouldReject) {
        setDocuments(prev => prev.map(doc => 
          doc.id === documentId 
            ? { 
                ...doc, 
                status: 'rejected' as const, 
                rejectionReason: 'Document quality is too low. Please upload a clearer image or PDF.'
              }
            : doc
        ));
      }
    }, 2000);
  };

  const removeDocument = (documentId: string) => {
    setDocuments(prev => prev.map(doc => 
      doc.id === documentId 
        ? { ...doc, file: null, status: 'not_uploaded' as const, rejectionReason: undefined }
        : doc
    ));
  };

  const getStatusColor = (status: DocumentStatus['status']) => {
    switch (status) {
      case 'uploaded': return 'text-green-600 bg-green-50';
      case 'rejected': return 'text-red-600 bg-red-50';
      default: return 'text-gray-600 bg-gray-50';
    }
  };

  const getStatusIcon = (status: DocumentStatus['status']) => {
    switch (status) {
      case 'uploaded': return <CheckCircle className="h-4 w-4" />;
      case 'rejected': return <AlertCircle className="h-4 w-4" />;
      default: return <Upload className="h-4 w-4" />;
    }
  };

  const getStatusText = (status: DocumentStatus['status']) => {
    switch (status) {
      case 'uploaded': return 'Uploaded';
      case 'rejected': return 'Rejected';
      default: return 'Not Uploaded';
    }
  };

  const requiredDocuments = documents.filter(doc => doc.required);
  const allRequiredUploaded = requiredDocuments.every(doc => doc.status === 'uploaded');
  const hasRejectedRequired = requiredDocuments.some(doc => doc.status === 'rejected');

  const handleContinue = () => {
    navigate('/fleet/customers/hierarchy-builder', {
      state: {
        customerData,
        fleetData,
        documents: documents.filter(doc => doc.file)
      }
    });
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <FileText className="h-8 w-8 text-blue-600" />
              <div>
                <h1 className="text-xl font-bold text-gray-900">Document Upload</h1>
                <p className="text-sm text-gray-600">
                  {customerData?.departmentName || 'Customer'} - Required Documents
                </p>
              </div>
            </div>
            <Button variant="outline" onClick={() => navigate(-1)}>
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back
            </Button>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-6xl mx-auto space-y-6">
          
          {/* Instructions */}
          <Card>
            <CardHeader>
              <CardTitle>Document Requirements</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <p className="text-gray-600">
                  Please upload the required documents to complete the onboarding process. 
                  All documents should be clear, legible, and in PDF, JPG, PNG, DOC, or DOCX format.
                </p>
                
                <Alert>
                  <AlertCircle className="h-4 w-4" />
                  <AlertDescription>
                    <strong>Required documents</strong> must be uploaded before you can continue. 
                    Optional documents can be uploaded later through the customer management portal.
                  </AlertDescription>
                </Alert>
              </div>
            </CardContent>
          </Card>

          {/* Document Upload Grid */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {documents.map((document) => (
              <Card key={document.id} className="relative">
                <CardHeader className="pb-4">
                  <div className="flex items-start justify-between">
                    <div className="flex items-center gap-3">
                      {document.icon}
                      <div>
                        <CardTitle className="text-lg flex items-center gap-2">
                          {document.name}
                          {document.required && (
                            <span className="text-red-500 text-sm">*</span>
                          )}
                        </CardTitle>
                        <p className="text-sm text-gray-600 mt-1">
                          {document.description}
                        </p>
                      </div>
                    </div>
                    <div className={`px-2 py-1 rounded-full text-xs font-medium flex items-center gap-1 ${getStatusColor(document.status)}`}>
                      {getStatusIcon(document.status)}
                      {getStatusText(document.status)}
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  {document.file ? (
                    <div className="space-y-3">
                      <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                        <div className="flex items-center gap-3">
                          <FileText className="h-5 w-5 text-gray-600" />
                          <div>
                            <p className="font-medium text-gray-900">{document.file.name}</p>
                            <p className="text-sm text-gray-500">
                              {(document.file.size / 1024 / 1024).toFixed(2)} MB
                            </p>
                          </div>
                        </div>
                        <div className="flex items-center gap-2">
                          <Button variant="ghost" size="sm">
                            <Eye className="h-4 w-4" />
                          </Button>
                          <Button 
                            variant="ghost" 
                            size="sm"
                            onClick={() => removeDocument(document.id)}
                            className="text-red-600 hover:text-red-700"
                          >
                            <X className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>
                      
                      {document.status === 'rejected' && document.rejectionReason && (
                        <Alert variant="destructive">
                          <AlertCircle className="h-4 w-4" />
                          <AlertDescription>
                            <strong>Rejected:</strong> {document.rejectionReason}
                          </AlertDescription>
                        </Alert>
                      )}
                    </div>
                  ) : (
                    <div
                      className={`border-2 border-dashed rounded-lg p-6 text-center transition-colors cursor-pointer ${
                        dragActiveId === document.id
                          ? 'border-blue-500 bg-blue-50' 
                          : 'border-gray-300 hover:border-gray-400'
                      }`}
                      onDragEnter={(e) => handleDrag(e, document.id)}
                      onDragLeave={(e) => handleDrag(e, document.id)}
                      onDragOver={(e) => handleDrag(e, document.id)}
                      onDrop={(e) => handleDrop(e, document.id)}
                      onClick={() => window.document.getElementById(`file-upload-${document.id}`)?.click()}
                    >
                      <Upload className="h-8 w-8 text-gray-400 mx-auto mb-2" />
                      <p className="text-sm font-medium text-gray-900">
                        Drop file here or click to browse
                      </p>
                      <p className="text-xs text-gray-500 mt-1">
                        PDF, JPG, PNG, DOC, DOCX up to 10MB
                      </p>
                      <input
                        id={`file-upload-${document.id}`}
                        type="file"
                        className="hidden"
                        accept=".pdf,.jpg,.jpeg,.png,.doc,.docx"
                        onChange={(e) => e.target.files?.[0] && handleFileUpload(e.target.files[0], document.id)}
                      />
                    </div>
                  )}
                </CardContent>
              </Card>
            ))}
          </div>

          {/* Progress Summary */}
          <Card>
            <CardContent className="pt-6">
              <div className="flex items-center justify-between">
                <div>
                  <h3 className="font-medium text-gray-900">Upload Progress</h3>
                  <p className="text-sm text-gray-600">
                    {documents.filter(doc => doc.status === 'uploaded').length} of {documents.length} documents uploaded
                    ({requiredDocuments.filter(doc => doc.status === 'uploaded').length} of {requiredDocuments.length} required)
                  </p>
                </div>
                <div className="flex gap-4">
                  <Button variant="outline">
                    Save Draft
                  </Button>
                  <Button 
                    onClick={handleContinue}
                    disabled={!allRequiredUploaded || hasRejectedRequired}
                    className="min-w-[120px]"
                  >
                    Continue Setup
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
};

export default DocumentUploadSection;
