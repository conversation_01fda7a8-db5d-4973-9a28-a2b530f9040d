# UI Screens for Adding a Driver to FMS - Design Specification

# UI Screens for Driver Onboarding Flow

Based on the Job Map for adding a driver to the Fleet Management System, this document outlines the UI screens required to complete the workflow. This specification is intended for UI designers implementing the driver onboarding process.

## Core Flow Overview

<aside>
This driver onboarding process follows a multi-step wizard approach similar to the vehicle onboarding process, with screens mapped to each stage of the JTBD framework.

</aside>

## 1. Entry Points

### 1.1 Driver Management Dashboard

- Screen showing list of all drivers with filterable table
- Prominent "Add New Driver" button in top-right corner
- Quick stats cards showing driver metrics (active drivers, license expirations)
- Navigation path: Fleet Manager Dashboard → Driver Management

### 1.2 Fleet Manager Dashboard Widget

- Driver management card with "Add Driver" quick action
- Dashboard should show count of active/inactive drivers

## 2. Main Screens

### 2.1 Driver Onboarding Wizard - Step 1: Basic Information

- Form with fields for:
    - First Name and Last Name (required)
    - Employee ID (required)
    - Department dropdown (required)
    - Email and Phone
    - Driver status selector (active, inactive, probationary)
- Progress indicator showing step 1 of 4
- Next and Cancel buttons

### 2.2 Driver Onboarding Wizard - Step 2: License & Qualifications

- Form with fields for:
    - License Number (required)
    - License Class/Type dropdown (required)
    - License Expiry Date picker with expiration warning
    - Endorsements/Restrictions checkboxes
    - Document upload zone for license scan (required)
    - Additional certifications upload (optional)
- Progress indicator showing step 2 of 4
- Previous, Next and Cancel buttons

### 2.3 Driver Onboarding Wizard - Step 3: Vehicle Assignment & Permissions

- Available vehicles table with search/filter
- Multi-select capability for assigning multiple vehicles
- Primary vehicle designation option
- Driver permissions section:
    - Vehicle type restrictions checkboxes
    - Geofencing permissions toggle
    - After-hours driving permission toggle
    - Maximum booking duration settings
- Progress indicator showing step 3 of 4
- Previous, Next and Cancel buttons

### 2.4 Driver Onboarding Wizard - Step 4: System Access & Review

- Driver App access configuration:
    - Generate credentials toggle
    - Send welcome email checkbox
    - QR code for app download
- Traffic fine management enrollment (AARTO):
    - Checkbox to enroll driver in AARTO system
    - Warning about required information
- Summary of all information entered in previous steps
- Progress indicator showing step 4 of 4
- Previous, Submit and Cancel buttons

### 2.5 Driver Onboarding Success Screen

- Success message with checkmark icon
- Driver ID and system identifiers displayed prominently
- Next steps instructions:
    - Link to driver's profile
    - Option to print driver credentials
    - Option to add another driver
    - Return to driver dashboard

## 3. Supporting Screens

### 3.1 Driver Profile View

- Complete driver information display
- Tabs for:
    - Personal Info
    - License & Qualifications
    - Assigned Vehicles
    - Trip History
    - Safety Score
    - Incidents & Violations
- Edit button to modify driver information
- Quick actions (suspend driver, reassign vehicles, etc.)

### 3.2 Driver Verification Modal

- Popup showing license verification status
- Integration with transport authority database
- Verification result with status indicator
- Option to override verification with reason

### 3.3 Vehicle Assignment Modal

- Popup for assigning/removing vehicles from driver
- Searchable list of available vehicles
- Currently assigned vehicles with remove option
- Save and Cancel buttons

## 4. Monitoring & Management Screens

### 4.1 Driver Dashboard

- Overview of all drivers with status indicators
- Filter by department, status, license expiry
- Quick access to driver profiles
- Bulk actions (export, send notifications)

### 4.2 Driver Behavior Monitoring

- Driver safety scores with trends
- Incident logs and violation history
- Trip analysis with charts
- Comparison tools for driver performance

### 4.3 License Expiry Monitor

- Calendar view of upcoming license expirations
- Notification configuration for reminders
- Bulk renewal processing

## 5. Related Modals & Popups

### 5.1 Error/Warning Modals

- Duplicate driver detection warning
- License verification failure
- Missing required information

### 5.2 Confirmation Dialogs

- Submit driver confirmation
- Cancel onboarding confirmation
- Vehicle assignment confirmation

## Design Guidelines

### 1. Layout & Consistency

- Maintain consistent header/footer across all screens
- Use the established RT46 design system components
- Ensure responsive design for tablet usage

### 2. Navigation

- Breadcrumb trail: Dashboard > Driver Management > Add Driver
- Clear step indicators in the wizard flow
- Save progress between steps

### 3. Accessibility

- Support keyboard navigation through forms
- Field validation with clear error messages
- High contrast mode support

### 4. Performance

- Optimize image uploads with compression
- Implement progressive loading for vehicle lists
- Cache department lists and other reference data

## Implementation Notes

- Reference AddVehiclePage.tsx for similar wizard pattern implementation
- The driver onboarding process is a P1 priority in the UI/UX priority guide
- Driver API models are defined in driverApi.ts
- Consider integration with the RTIA system for traffic fine nominations
- Driver behavior scoring integration will be needed for the monitoring screens