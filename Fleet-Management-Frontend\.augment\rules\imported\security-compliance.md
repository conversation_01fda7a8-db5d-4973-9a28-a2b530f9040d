---
type: "always_apply"
---

# Security & Compliance Requirements

## POPIA Compliance (Protection of Personal Information Act)
- All personal data must be encrypted at rest and in transit
- Implement data minimization principles
- Provide user consent management capabilities
- Support individual rights: access, correction, deletion, portability
- Maintain comprehensive audit trails for all personal data access
- Implement data retention policies with automatic purging

## Government Security Standards
- Multi-factor authentication required for all administrative accounts
- Role-based access control with principle of least privilege
- End-to-end encryption for all sensitive communications
- Regular security audits and vulnerability assessments
- Incident response procedures and breach notification protocols

## South African Regulatory Compliance
- **BBBEE Compliance**: Support Broad-Based Black Economic Empowerment requirements
- **PFMA Compliance**: Public Finance Management Act adherence
- **Government Procurement Regulations**: Follow supply chain management requirements
- **National Treasury Regulations**: Financial reporting and audit requirements

## API Security Requirements
- OAuth 2.0 for authentication with JWT tokens
- Rate limiting and DDoS protection
- Input validation and sanitization for all endpoints
- API versioning and backward compatibility
- Comprehensive logging without exposing sensitive data

## Data Classification & Protection
- **Public**: Marketing materials, general information
- **Internal**: Operational data, non-sensitive reports  
- **Confidential**: Personal information, financial data
- **Restricted**: Authentication credentials, encryption keys

## Audit and Monitoring
- 24/7 security monitoring and alerting
- Immutable audit logs with tamper detection
- Real-time anomaly detection and threat analysis
- Regular penetration testing and security assessments
- Compliance reporting automation
---
alwaysApply: true
---
