import { api } from '../api';
import type { 
  VehicleResponse, 
  VehicleRequest, 
  PaginatedResponse, 
  VehicleFilters,
  ApiResponse 
} from '../../types/api';

export const vehicleApi = api.injectEndpoints({
  endpoints: (builder) => ({
    // Get vehicles with filtering and pagination
    getVehicles: builder.query<PaginatedResponse<VehicleResponse>, VehicleFilters>({
      query: (filters = {}) => {
        const params = new URLSearchParams();
        
        // Add pagination
        params.append('page', String(filters.page || 1));
        params.append('limit', String(filters.limit || 20));
        
        // Add filters
        if (filters.department) params.append('department', filters.department);
        if (filters.status) params.append('status', filters.status);
        if (filters.search) params.append('search', filters.search);
        if (filters.make) params.append('make', filters.make);
        if (filters.year_from) params.append('year_from', String(filters.year_from));
        if (filters.year_to) params.append('year_to', String(filters.year_to));
        
        return {
          url: '/vehicles',
          params: Object.fromEntries(params),
        };
      },
      providesTags: (result) =>
        result
          ? [
              ...result.data.map(({ id }) => ({ type: 'Vehicle' as const, id })),
              { type: 'Vehicle', id: 'LIST' },
            ]
          : [{ type: 'Vehicle', id: 'LIST' }],
      // Transform response to handle different API response formats
      transformResponse: (response: any): PaginatedResponse<VehicleResponse> => {
        // Handle direct array response (fallback)
        if (Array.isArray(response)) {
          return {
            data: response,
            pagination: {
              page: 1,
              limit: response.length,
              total: response.length,
              totalPages: 1,
              hasNext: false,
              hasPrev: false,
            },
            timestamp: new Date().toISOString(),
          };
        }
        
        // Handle paginated response
        return {
          data: response.data || response.vehicles || [],
          pagination: response.pagination || {
            page: 1,
            limit: 20,
            total: response.total || 0,
            totalPages: Math.ceil((response.total || 0) / 20),
            hasNext: false,
            hasPrev: false,
          },
          message: response.message,
          timestamp: response.timestamp || new Date().toISOString(),
        };
      },
    }),

    // Get single vehicle
    getVehicle: builder.query<VehicleResponse, string>({
      query: (id) => `/vehicles/${id}`,
      providesTags: (result, error, id) => [{ type: 'Vehicle', id }],
      transformResponse: (response: any): VehicleResponse => {
        // Handle wrapped response
        return response.data || response;
      },
    }),

    // Create vehicle
    createVehicle: builder.mutation<VehicleResponse, VehicleRequest>({
      query: (vehicle) => ({
        url: '/vehicles',
        method: 'POST',
        body: vehicle,
      }),
      invalidatesTags: [{ type: 'Vehicle', id: 'LIST' }],
      transformResponse: (response: any): VehicleResponse => {
        return response.data || response;
      },
    }),

    // Update vehicle
    updateVehicle: builder.mutation<VehicleResponse, { id: string; data: Partial<VehicleRequest> }>({
      query: ({ id, data }) => ({
        url: `/vehicles/${id}`,
        method: 'PUT',
        body: data,
      }),
      invalidatesTags: (result, error, { id }) => [
        { type: 'Vehicle', id },
        { type: 'Vehicle', id: 'LIST' },
      ],
      transformResponse: (response: any): VehicleResponse => {
        return response.data || response;
      },
    }),

    // Delete vehicle
    deleteVehicle: builder.mutation<{ success: boolean; message: string }, string>({
      query: (id) => ({
        url: `/vehicles/${id}`,
        method: 'DELETE',
      }),
      invalidatesTags: (result, error, id) => [
        { type: 'Vehicle', id },
        { type: 'Vehicle', id: 'LIST' },
      ],
    }),

    // Bulk operations
    bulkCreateVehicles: builder.mutation<{ created: number; errors: any[] }, VehicleRequest[]>({
      query: (vehicles) => ({
        url: '/vehicles/bulk',
        method: 'POST',
        body: { vehicles },
      }),
      invalidatesTags: [{ type: 'Vehicle', id: 'LIST' }],
    }),

    // Get vehicle statistics
    getVehicleStats: builder.query<{
      total: number;
      by_status: Record<string, number>;
      by_department: Record<string, number>;
    }, void>({
      query: () => '/vehicles/stats',
      providesTags: [{ type: 'Vehicle', id: 'STATS' }],
    }),
  }),
});

export const {
  useGetVehiclesQuery,
  useGetVehicleQuery,
  useCreateVehicleMutation,
  useUpdateVehicleMutation,
  useDeleteVehicleMutation,
  useBulkCreateVehiclesMutation,
  useGetVehicleStatsQuery,
  // Lazy queries for conditional fetching
  useLazyGetVehiclesQuery,
  useLazyGetVehicleQuery,
} = vehicleApi;

// Export the entire API for external use
export default vehicleApi;

// Custom hook for vehicle operations (optional enhancement)
export const useVehicleOperations = () => {
  const [createVehicle] = useCreateVehicleMutation();
  const [updateVehicle] = useUpdateVehicleMutation();
  const [deleteVehicle] = useDeleteVehicleMutation();

  return {
    createVehicle: (data: VehicleRequest) => createVehicle(data).unwrap(),
    updateVehicle: (id: string, data: Partial<VehicleRequest>) => 
      updateVehicle({ id, data }).unwrap(),
    deleteVehicle: (id: string) => deleteVehicle(id).unwrap(),
  };
};

