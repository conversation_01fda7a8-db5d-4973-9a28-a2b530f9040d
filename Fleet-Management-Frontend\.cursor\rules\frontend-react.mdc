# React.js Frontend Development Rules for RT46-2026

## React Architecture Principles

### Component Design Philosophy
- **Functional Components Only** - Use hooks instead of class components
- **Single Responsibility Principle** - Each component has one clear purpose
- **Composition over Inheritance** - Build complex UIs from simple components
- **Props Interface Design** - Use TypeScript interfaces for all props
- **Controlled Components** - Manage form state through React state
- **Pure Components** - Avoid side effects in render functions

### Component Organization
- **Feature-Based Structure** - Group by business domain, not technical layer
- **Shared Components** - Reusable UI components in shared directory
- **Page Components** - Top-level route components
- **Container Components** - Data fetching and state management
- **Presentation Components** - Pure UI components with props only
- **Hook Components** - Custom hooks for reusable logic

### Folder Structure Standards
```
src/
├── components/           # Shared/reusable components
│   ├── ui/              # Basic UI components (Button, Input, Modal)
│   ├── forms/           # Form-specific components
│   ├── layout/          # Layout components (Head<PERSON>, <PERSON>bar, Footer)
│   └── charts/          # Data visualization components
├── features/            # Feature-specific components
│   ├── vehicles/        # Vehicle management components
│   ├── workorders/      # Work order components
│   ├── vendors/         # Vendor management components
│   └── dashboard/       # Dashboard components
├── hooks/               # Custom React hooks
├── services/            # API services and data fetching
├── store/               # Redux store configuration
├── utils/               # Utility functions
├── types/               # TypeScript type definitions
└── assets/              # Static assets (images, icons, fonts)
```

## TypeScript Integration

### Type Safety Requirements
- **Strict TypeScript Configuration** - Enable all strict mode options
- **Interface Definitions** - Define interfaces for all props, state, and API responses
- **Generic Types** - Use generics for reusable components and hooks
- **Union Types** - Use for status fields and enums (pending | approved | rejected)
- **Optional Properties** - Mark optional props and fields explicitly
- **Type Guards** - Implement runtime type checking for API responses

### Component Typing Patterns
- **Props Interfaces** - Define clear interfaces for all component props
- **Event Handler Types** - Use React's built-in event types
- **Ref Types** - Properly type useRef and forwardRef usage
- **State Types** - Define interfaces for complex state objects
- **Hook Return Types** - Type custom hook return values
- **Children Types** - Use React.ReactNode for children props

### API Integration Types
- **Request/Response Types** - Match backend API data structures
- **Error Types** - Define error interfaces for consistent error handling
- **Loading States** - Type loading, success, and error states
- **Form Data Types** - Define interfaces for form validation
- **Query Parameters** - Type URL parameters and search filters
- **Pagination Types** - Define interfaces for paginated responses

## State Management with Redux Toolkit

### Store Architecture
- **Feature-Based Slices** - One slice per business domain
- **Normalized State** - Use entity adapters for collections
- **Immutable Updates** - Redux Toolkit's Immer integration
- **Async Thunks** - Handle async operations with createAsyncThunk
- **RTK Query** - Use for server state management and caching
- **Selector Patterns** - Use createSelector for computed state

### State Organization
- **Domain Entities** - vehicles, workOrders, vendors, users
- **UI State** - modals, loading states, selected items
- **User Preferences** - theme, language, dashboard layout
- **Cache Management** - API response caching with RTK Query
- **Authentication State** - User session and permissions
- **Error Handling** - Global error state management

### Performance Optimization
- **Memoization** - Use React.memo, useMemo, useCallback appropriately
- **State Normalization** - Avoid nested objects in Redux state
- **Selective Subscriptions** - Use specific selectors to minimize re-renders
- **Code Splitting** - Lazy load components and routes
- **Bundle Analysis** - Regular bundle size monitoring and optimization
- **Tree Shaking** - Ensure unused code is eliminated

## UI/UX Standards

### Material-UI Implementation
- **Design System Consistency** - Use Material-UI theme configuration
- **Custom Theme** - Government-appropriate color scheme and typography
- **Responsive Design** - Mobile-first approach with breakpoints
- **Accessibility Compliance** - WCAG 2.1 AA standards
- **Component Customization** - Extend Material-UI components appropriately
- **Icon Standards** - Consistent icon usage across the application

### Form Management
- **React Hook Form** - Use for form state management and validation
- **Zod Validation** - Type-safe form validation schemas
- **Error Handling** - Consistent error message display
- **Loading States** - Show loading indicators during form submission
- **Field Validation** - Real-time validation with clear feedback
- **Accessibility** - Proper labeling and keyboard navigation

### Data Display Standards
- **Table Components** - Sortable, filterable, and paginated tables
- **Chart Integration** - Use Chart.js or D3.js for data visualization
- **Loading Skeletons** - Skeleton screens for better perceived performance
- **Empty States** - Meaningful empty state messages and actions
- **Error Boundaries** - Graceful error handling with recovery options
- **Infinite Scrolling** - For large data sets where appropriate

## Performance and Optimization

### Code Splitting Strategies
- **Route-Based Splitting** - Lazy load page components
- **Feature-Based Splitting** - Load features on demand
- **Component-Level Splitting** - Split heavy components
- **Third-Party Library Splitting** - Separate vendor bundles
- **Dynamic Imports** - Use for conditional feature loading
- **Preloading** - Preload critical routes and components

### Rendering Optimization
- **Virtual Scrolling** - For large lists and tables
- **Image Optimization** - Lazy loading and responsive images
- **Memory Management** - Cleanup subscriptions and event listeners
- **Re-render Prevention** - Optimize component updates
- **State Updates** - Batch state updates for better performance
- **Effect Dependencies** - Properly manage useEffect dependencies

### Network Optimization
- **Request Deduplication** - Avoid duplicate API calls
- **Background Sync** - Update data without blocking UI
- **Optimistic Updates** - Update UI before server confirmation
- **Error Recovery** - Retry failed requests with exponential backoff
- **Pagination** - Load data in chunks for better performance
- **Search Debouncing** - Delay search requests to reduce API calls

## Security Implementation

### Authentication Integration
- **JWT Token Management** - Secure token storage and refresh
- **Route Protection** - Implement protected routes based on roles
- **Permission Checks** - Component-level permission validation
- **Session Management** - Handle session expiry and renewal
- **Multi-Factor Authentication** - Support MFA flows in UI
- **Logout Handling** - Secure logout with token cleanup

### Data Security
- **Input Sanitization** - Sanitize user inputs to prevent XSS
- **HTTPS Enforcement** - Ensure all API calls use HTTPS
- **Sensitive Data Handling** - Avoid storing sensitive data in localStorage
- **Content Security Policy** - Implement CSP headers
- **CSRF Protection** - Include CSRF tokens in requests
- **Data Validation** - Client-side validation aligned with backend

### Privacy Compliance (POPIA)
- **User Consent Management** - Cookie and data usage consent
- **Data Minimization** - Only request necessary user data
- **Right to Access** - Provide user data export functionality
- **Right to Deletion** - Implement data deletion requests
- **Privacy Settings** - User control over data sharing preferences
- **Audit Logging** - Log user actions for compliance

## Testing Strategy

### Unit Testing
- **Component Testing** - Test component rendering and behavior
- **Hook Testing** - Test custom hooks in isolation
- **Utility Testing** - Test pure functions and utilities
- **Mock Implementation** - Mock external dependencies appropriately
- **Snapshot Testing** - Use sparingly for stable components
- **Coverage Requirements** - 80% minimum coverage for components

### Integration Testing
- **User Flow Testing** - Test complete user workflows
- **API Integration** - Test component interaction with APIs
- **State Management** - Test Redux actions and reducers
- **Form Validation** - Test form submission and validation
- **Navigation Testing** - Test routing and navigation flows
- **Error Scenario Testing** - Test error handling and recovery

### End-to-End Testing
- **Critical Path Testing** - Test main user journeys
- **Cross-Browser Testing** - Ensure compatibility across browsers
- **Responsive Testing** - Test on different screen sizes
- **Accessibility Testing** - Test keyboard navigation and screen readers
- **Performance Testing** - Test loading times and responsiveness
- **Visual Regression Testing** - Catch unintended visual changes

## Development Workflow

### Code Quality Standards
- **ESLint Configuration** - Strict linting rules for consistency
- **Prettier Formatting** - Automated code formatting
- **Pre-commit Hooks** - Run linting and tests before commits
- **Type Checking** - TypeScript compilation without errors
- **Import Organization** - Consistent import ordering and grouping
- **Component Documentation** - JSDoc comments for complex components

### Development Tools
- **React DevTools** - For debugging component state and props
- **Redux DevTools** - For debugging state management
- **Browser DevTools** - Performance profiling and debugging
- **Storybook** - Component development and documentation
- **Hot Reloading** - Fast development feedback loop
- **Error Boundaries** - Development error reporting

### Build and Deployment
- **Vite Build System** - Fast build and development server
- **Environment Configuration** - Different configs for dev/staging/prod
- **Asset Optimization** - Image compression and bundling
- **Progressive Web App** - PWA configuration for offline support
- **Cache Management** - Browser cache optimization strategies
- **Bundle Analysis** - Regular bundle size monitoring

## Common Anti-Patterns to Avoid

### React Anti-Patterns
- **Prop Drilling** - Use context or Redux for deeply nested props
- **Mutating Props** - Never modify props directly
- **Index as Key** - Use stable, unique keys for list items
- **Unnecessary Re-renders** - Optimize component updates
- **Memory Leaks** - Clean up event listeners and subscriptions
- **Inline Functions** - Avoid creating functions in render

### State Management Anti-Patterns
- **Local State Overuse** - Use global state for shared data
- **Redux for Everything** - Keep local state for UI-only concerns
- **Mutating State** - Always create new state objects
- **Async in Reducers** - Use thunks for async operations
- **Deeply Nested State** - Normalize complex state structures
- **Missing Error Handling** - Handle all async operation states

### Performance Anti-Patterns
- **Premature Optimization** - Profile before optimizing
- **Over-memoization** - Don't memoize everything unnecessarily
- **Large Bundle Sizes** - Monitor and optimize bundle size
- **Blocking Operations** - Keep the main thread free
- **Memory Leaks** - Properly cleanup resources
- **Excessive API Calls** - Implement proper caching and batching

