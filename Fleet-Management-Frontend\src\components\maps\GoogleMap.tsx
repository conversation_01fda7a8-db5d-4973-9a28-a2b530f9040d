import React, { useEffect, useRef, useState } from 'react';

interface GoogleMapProps {
  center: { lat: number; lng: number };
  zoom: number;
  vehicles: Array<{
    id: string;
    registrationNumber: string;
    lat: number;
    lng: number;
    status: 'moving' | 'idling' | 'stopped';
    speed: number;
    driver: string;
    lastUpdate: string;
    department: string;
  }>;
  onVehicleClick: (vehicle: any) => void;
  onMapClick?: (lat: number, lng: number) => void;
}

const GoogleMap: React.FC<GoogleMapProps> = ({
  center,
  zoom,
  vehicles,
  onVehicleClick,
  onMapClick
}) => {
  const mapRef = useRef<HTMLDivElement>(null);
  const [map, setMap] = useState<google.maps.Map | null>(null);
  const [markers, setMarkers] = useState<google.maps.Marker[]>([]);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'moving': return '#4caf50';
      case 'idling': return '#2196f3';
      case 'stopped': return '#f44336';
      default: return '#757575';
    }
  };

  // Initialize map
  useEffect(() => {
    if (mapRef.current && !map) {
      const newMap = new google.maps.Map(mapRef.current, {
        center,
        zoom,
        mapTypeId: google.maps.MapTypeId.ROADMAP,
        styles: [
          {
            featureType: 'poi',
            elementType: 'labels',
            stylers: [{ visibility: 'off' }]
          }
        ]
      });

      // Add click listener for geofencing
      if (onMapClick) {
        newMap.addListener('click', (e: google.maps.MapMouseEvent) => {
          if (e.latLng) {
            onMapClick(e.latLng.lat(), e.latLng.lng());
          }
        });
      }

      setMap(newMap);
    }
  }, [center, zoom, map, onMapClick]);

  // Update vehicle markers
  useEffect(() => {
    if (!map) return;

    // Clear existing markers
    markers.forEach(marker => marker.setMap(null));

    // Create new markers
    const newMarkers = vehicles.map(vehicle => {
      const marker = new google.maps.Marker({
        position: { lat: vehicle.lat, lng: vehicle.lng },
        map,
        title: vehicle.registrationNumber,
        icon: {
          path: google.maps.SymbolPath.CIRCLE,
          scale: 12,
          fillColor: getStatusColor(vehicle.status),
          fillOpacity: 1,
          strokeColor: '#ffffff',
          strokeWeight: 2
        }
      });

      // Add click listener
      marker.addListener('click', () => {
        onVehicleClick(vehicle);
      });

      return marker;
    });

    setMarkers(newMarkers);
  }, [map, vehicles, onVehicleClick]);

  return <div ref={mapRef} style={{ width: '100%', height: '100%' }} />;
};

export default GoogleMap;