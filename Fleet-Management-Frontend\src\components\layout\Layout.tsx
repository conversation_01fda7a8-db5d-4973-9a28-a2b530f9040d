import React, { useState } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import { useAppSelector, useAppDispatch } from '@/hooks/redux';
import { logout } from '@/store/slices/authSlice';
import LogoutConfirmModal from '../modals/LogoutConfirmModal';
import {
  Menu,
  X,
  Bell,
  User,
  Settings,
  LogOut,
  LayoutDashboard,
  Car,
  Wrench,
  Building2,
  FileText,
  Calendar,
  ClipboardList,
  AlertTriangle,
  ChevronDown,
  CheckCircle,
  Fuel,
  DollarSign,
  TrendingUp
} from 'lucide-react';

interface MenuItem {
  text: string;
  path?: string;
  icon: React.ReactNode;
  roles?: string[];
  children?: MenuItem[];
}

const Layout: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [userMenuOpen, setUserMenuOpen] = useState(false);
  const [expandedItems, setExpandedItems] = useState<string[]>([]);
  const [showLogoutModal, setShowLogoutModal] = useState(false);
  const [isLoggingOut, setIsLoggingOut] = useState(false);
  
  const location = useLocation();
  const navigate = useNavigate();
  const dispatch = useAppDispatch();
  const { user } = useAppSelector((state) => state.auth);

  // Get user role from auth state
  const userRole = user?.role || 'fleet_manager';

  const handleLogout = async () => {
    setIsLoggingOut(true);
    try {
      // Dispatch logout action
      dispatch(logout());
      
      // Close modals
      setShowLogoutModal(false);
      setUserMenuOpen(false);
      
      // Navigate to login page
      navigate('/login');
    } catch (error) {
      console.error('Logout error:', error);
    } finally {
      setIsLoggingOut(false);
    }
  };

  const handleSignOutClick = () => {
    setUserMenuOpen(false);
    setShowLogoutModal(true);
  };

  // Get user display info based on role and user data
  const getUserDisplayInfo = () => {
    if (user) {
      return {
        name: `${user.firstName} ${user.lastName}`,
        title: getRoleDisplayName(user.role),
        email: user.email
      };
    }
    
    // Fallback for when user data is not available
    switch (userRole) {
      case 'transport_officer':
        return { name: 'Transport Officer', title: 'Transport Officer', email: '<EMAIL>' };
      case 'fleet_manager':
        return { name: 'Fleet Manager', title: 'Fleet Manager', email: '<EMAIL>' };
      case 'finance_officer':
        return { name: 'Finance Officer', title: 'Finance Officer', email: '<EMAIL>' };
      case 'admin':
        return { name: 'System Admin', title: 'Administrator', email: '<EMAIL>' };
      default:
        return { name: 'User', title: 'Staff Member', email: '<EMAIL>' };
    }
  };

  const getRoleDisplayName = (role: string) => {
    switch (role) {
      case 'fleet_manager':
        return 'Fleet Manager';
      case 'transport_officer':
        return 'Transport Officer';
      case 'finance_officer':
        return 'Finance Officer';
      case 'driver':
        return 'Driver';
      case 'vendor':
        return 'Vendor';
      case 'merchant':
        return 'Merchant';
      case 'inspector':
        return 'Inspector';
      case 'executive':
        return 'Executive';
      case 'auditor':
        return 'Auditor';
      case 'admin':
        return 'Administrator';
      default:
        return 'Staff Member';
    }
  };

  const userInfo = getUserDisplayInfo();

  const menuItems: MenuItem[] = [
    {
      text: 'Dashboard',
      path: userRole === 'driver' ? '/driver/dashboard' : '/dashboard',
      icon: <LayoutDashboard className="h-5 w-5" />
    },
    {
      text: 'Fleet Management',
      icon: <Car className="h-5 w-5" />,
      roles: ['fleet_manager', 'admin'],
      children: [
        { text: 'Customer Onboarding', path: '/fleet/customers/new', icon: <Building2 className="h-4 w-4" /> },
        { text: 'Vehicle List', path: '/vehicles', icon: <Car className="h-4 w-4" /> },
        { text: 'Add Vehicle', path: '/vehicles/add', icon: <Car className="h-4 w-4" /> },
        { text: 'Live Tracking', path: '/tracking', icon: <Car className="h-4 w-4" /> }
      ]
    },
    {
      text: 'Transport Operations',
      icon: <Car className="h-5 w-5" />,
      roles: ['transport_officer', 'admin'],
      children: [
        { text: 'Transport Dashboard', path: '/transport/dashboard', icon: <LayoutDashboard className="h-4 w-4" /> },
        { text: 'Booking Calendar', path: '/transport/booking-calendar', icon: <Calendar className="h-4 w-4" /> },
        { text: 'Trip Logs', path: '/transport/trip-logs', icon: <FileText className="h-4 w-4" /> },
        { text: 'Maintenance Request', path: '/transport/maintenance/request', icon: <AlertTriangle className="h-4 w-4" /> },
        { text: 'Schedule Maintenance', path: '/transport/maintenance/schedule', icon: <Calendar className="h-4 w-4" /> }
      ]
    },
    {
      text: 'Maintenance',
      icon: <Wrench className="h-5 w-5" />,
      roles: ['fleet_manager', 'transport_officer', 'admin'],
      children: [
        { text: 'Request List', path: '/maintenance/requests', icon: <ClipboardList className="h-4 w-4" /> },
        { text: 'Accident Cases', path: '/maintenance/accidents', icon: <AlertTriangle className="h-4 w-4" /> },
        { text: 'Schedule Service', path: '/maintenance/schedule', icon: <Calendar className="h-4 w-4" /> }
      ]
    },
    {
      text: 'Work Orders',
      icon: <Wrench className="h-5 w-5" />,
      roles: ['fleet_manager', 'admin'],
      children: [
        { text: 'All Work Orders', path: '/work-orders', icon: <Wrench className="h-4 w-4" /> },
        { text: 'Create Work Order', path: '/work-orders/create', icon: <Wrench className="h-4 w-4" /> }
      ]
    },
    {
      text: 'Vendor Management',
      path: '/vendors',
      icon: <Building2 className="h-5 w-5" />,
      roles: ['fleet_manager', 'admin']
    },
    {
      text: 'Live Tracking',
      path: '/tracking',
      icon: <Car className="h-5 w-5" />,
      roles: ['fleet_manager', 'transport_officer', 'admin']
    },
    {
      text: 'Reports & Analytics',
      path: '/reports',
      icon: <FileText className="h-5 w-5" />
    },
    {
      text: 'Settings',
      path: '/settings',
      icon: <Settings className="h-5 w-5" />
    },
    {
      text: 'Driver Operations',
      icon: <Car className="h-5 w-5" />,
      roles: ['driver', 'admin'],
      children: [
        { text: 'My Dashboard', path: '/driver/dashboard', icon: <LayoutDashboard className="h-4 w-4" /> },
        { text: 'Pre-Trip Check', path: '/driver/pre-trip-check', icon: <CheckCircle className="h-4 w-4" /> },
        { text: 'Report Issue', path: '/driver/report-issue', icon: <AlertTriangle className="h-4 w-4" /> },
        { text: 'Log Fuel', path: '/driver/fuel-log', icon: <Fuel className="h-4 w-4" /> },
        { text: 'Book Vehicle', path: '/driver/book-vehicle', icon: <Calendar className="h-4 w-4" /> },
        { text: 'Trip History', path: '/driver/trip-history', icon: <FileText className="h-4 w-4" /> }
      ]
    },
    {
      text: 'Finance Management',
      icon: <DollarSign className="h-5 w-5" />,
      roles: ['finance_officer', 'admin'],
      children: [
        { text: 'Finance Dashboard', path: '/finance/dashboard', icon: <LayoutDashboard className="h-4 w-4" /> },
        { text: 'Invoice Queue', path: '/finance/invoices', icon: <FileText className="h-4 w-4" /> },
        { text: 'Payment Tracker', path: '/finance/payments', icon: <TrendingUp className="h-4 w-4" /> },
        { text: 'Budget Management', path: '/finance/budget', icon: <Settings className="h-4 w-4" /> }
      ]
    },
    {
      text: 'Merchant Portal',
      icon: <Building2 className="h-5 w-5" />,
      roles: ['merchant', 'admin'],
      children: [
        { text: 'Dashboard', path: '/merchant/dashboard', icon: <LayoutDashboard className="h-4 w-4" /> },
        { text: 'Work Orders', path: '/merchant/work-orders', icon: <ClipboardList className="h-4 w-4" /> },
        { text: 'Quote Submission', path: '/merchant/quotes', icon: <FileText className="h-4 w-4" /> },
        { text: 'Invoices', path: '/merchant/invoices', icon: <DollarSign className="h-4 w-4" /> },
        { text: 'Payment Tracker', path: '/merchant/payments', icon: <TrendingUp className="h-4 w-4" /> }
      ]
    },
    {
      text: 'Inspector Operations',
      icon: <ClipboardList className="h-5 w-5" />,
      roles: ['inspector', 'admin'],
      children: [
        { text: 'Inspector Dashboard', path: '/inspector/dashboard', icon: <LayoutDashboard className="h-4 w-4" /> },
        { text: 'Inspection Schedule', path: '/inspector/schedule', icon: <Calendar className="h-4 w-4" /> },
        { text: 'Inspection Checklist', path: '/inspector/checklist/1', icon: <CheckCircle className="h-4 w-4" /> },
        { text: 'Inspection Reports', path: '/inspector/reports', icon: <FileText className="h-4 w-4" /> }
      ]
    },
    {
      text: 'Administration',
      icon: <Settings className="h-5 w-5" />,
      roles: ['admin'],
      children: [
        { text: 'Admin Dashboard', path: '/dashboard', icon: <LayoutDashboard className="h-4 w-4" /> },
        { text: 'Pending Applications', path: '/admin/departments/pending', icon: <ClipboardList className="h-4 w-4" /> },
        { text: 'Department Management', path: '/admin/departments', icon: <Building2 className="h-4 w-4" /> },
        { text: 'User Management', path: '/admin/users', icon: <User className="h-4 w-4" /> }
      ]
    }
  ];

  // Filter menu items based on user role
  const filteredMenuItems = menuItems.filter(item => {
    if (!item.roles) return true; // No role restriction
    return item.roles.includes(userRole);
  }).map(item => ({
    ...item,
    children: item.children?.filter(child => {
      if (!child.roles) return true;
      return child.roles.includes(userRole);
    })
  }));

  const isActiveRoute = (path: string) => {
    return location.pathname === path || location.pathname.startsWith(path + '/');
  };

  const toggleExpanded = (itemText: string) => {
    setExpandedItems(prev => 
      prev.includes(itemText) 
        ? prev.filter(item => item !== itemText)
        : [...prev, itemText]
    );
  };

  const handleNavigation = (path: string) => {
    navigate(path);
    setSidebarOpen(false);
  };

  const renderMenuItem = (item: MenuItem, depth = 0) => {
    const isExpanded = expandedItems.includes(item.text);
    const hasChildren = item.children && item.children.length > 0;
    const isActive = item.path ? isActiveRoute(item.path) : false;

    return (
      <div key={item.text} className={depth > 0 ? 'ml-4' : ''}>
        <div className={`bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden ${depth > 0 ? 'border-l-4 border-l-blue-200' : ''}`}>
          <button
            onClick={() => {
              if (hasChildren) {
                toggleExpanded(item.text);
              } else if (item.path) {
                handleNavigation(item.path);
              }
            }}
            className={`w-full flex items-center justify-between p-3 text-sm font-medium transition-all duration-200 ${
              isActive
                ? 'bg-blue-50 text-blue-700 border-l-4 border-l-blue-600'
                : 'text-gray-700 hover:bg-gray-50 hover:text-gray-900'
            }`}
          >
            <div className="flex items-center">
              <div className={`p-1.5 rounded-md mr-2.5 ${
                isActive 
                  ? 'bg-blue-100 text-blue-600' 
                  : 'bg-gray-100 text-gray-600'
              }`}>
                {item.icon}
              </div>
              <span className="font-medium">{item.text}</span>
            </div>
            {hasChildren && (
              <div className={`p-0.5 rounded transition-transform duration-200 ${
                isExpanded ? 'rotate-180 bg-gray-100' : 'bg-gray-50'
              }`}>
                <ChevronDown className="h-3.5 w-3.5 text-gray-500" />
              </div>
            )}
          </button>

          {hasChildren && isExpanded && (
            <div className="bg-gray-50 border-t border-gray-100">
              <div className="p-1 space-y-0.5">
                {item.children?.map(child => (
                  <button
                    key={child.text}
                    onClick={() => child.path && handleNavigation(child.path)}
                    className={`w-full flex items-center p-2 text-sm rounded-lg transition-colors ${
                      child.path && isActiveRoute(child.path)
                        ? 'bg-blue-100 text-blue-700 font-medium'
                        : 'text-gray-600 hover:bg-white hover:text-gray-900'
                    }`}
                  >
                    <div className={`p-1 rounded-md mr-2.5 ${
                      child.path && isActiveRoute(child.path)
                        ? 'bg-blue-200 text-blue-700'
                        : 'bg-gray-200 text-gray-500'
                    }`}>
                      {child.icon}
                    </div>
                    <span>{child.text}</span>
                  </button>
                ))}
              </div>
            </div>
          )}
        </div>
      </div>
    );
  };

  return (
    <div className="flex h-screen bg-gray-50">
      {/* Logout Confirmation Modal */}
      <LogoutConfirmModal
        isOpen={showLogoutModal}
        onClose={() => setShowLogoutModal(false)}
        onConfirm={handleLogout}
        isLoading={isLoggingOut}
      />

      {/* Mobile sidebar overlay */}
      {sidebarOpen && (
        <div 
          className="fixed inset-0 z-40 bg-black bg-opacity-50 lg:hidden"
          onClick={() => setSidebarOpen(false)}
        />
      )}

      {/* Sidebar */}
      <div className={`fixed inset-y-0 left-0 z-50 w-64 bg-white shadow-xl transform transition-transform duration-300 ease-in-out lg:translate-x-0 lg:relative lg:flex lg:flex-col ${
        sidebarOpen ? 'translate-x-0' : '-translate-x-full'
      }`}>
        {/* Sidebar Header */}
        <div className="flex items-center justify-between h-16 px-6 border-b border-gray-200 bg-blue-600">
          <h1 className="text-xl font-bold text-white">RT46 Fleet</h1>
          <button
            className="lg:hidden p-2 rounded-md text-white hover:bg-blue-700"
            onClick={() => setSidebarOpen(false)}
          >
            <X className="h-5 w-5" />
          </button>
        </div>

        {/* User Role Badge */}
        {/* <div className="px-4 py-3 bg-gray-50 border-b border-gray-200">
          <div className="flex items-center space-x-2.5">
            <div className="h-8 w-8 bg-gradient-to-br from-blue-500 to-blue-600 rounded-lg flex items-center justify-center shadow-sm">
              <User className="h-4 w-4 text-white" />
            </div>
            <div className="flex-1 min-w-0">
              <p className="text-sm font-semibold text-gray-900 truncate">{userInfo.name}</p>
              <p className="text-xs text-gray-500 truncate">{userInfo.title}</p>
              <div className="mt-1">
                <span className="inline-flex items-center px-1.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                  {userInfo.role}
                </span>
              </div>
            </div>
          </div>
        </div> */}

        {/* Navigation */}
        <nav className="flex-1 px-4 py-6 space-y-1 overflow-y-auto">
          {filteredMenuItems.map(item => renderMenuItem(item))}
        </nav>
      </div>

      {/* Main content */}
      <div className="flex-1 flex flex-col overflow-hidden">
        {/* Top navigation */}
        <header className="bg-white shadow-sm border-b border-gray-200">
          <div className="flex items-center justify-between h-16 px-6">
            <div className="flex items-center">
              <button
                className="lg:hidden p-2 rounded-md hover:bg-gray-100"
                onClick={() => setSidebarOpen(true)}
              >
                <Menu className="h-5 w-5" />
              </button>
              <h2 className="ml-4 text-lg font-semibold text-gray-800 lg:ml-0">
                Fleet Management System
              </h2>
            </div>

            <div className="flex items-center space-x-4">
              <button className="relative p-2 rounded-md hover:bg-gray-100">
                <Bell className="h-5 w-5" />
                <span className="absolute -top-1 -right-1 h-3 w-3 bg-red-500 rounded-full"></span>
              </button>

              <div className="relative">
                <button
                  className="flex items-center space-x-2 p-2 rounded-md hover:bg-gray-100"
                  onClick={() => setUserMenuOpen(!userMenuOpen)}
                >
                  <div className="h-8 w-8 bg-blue-500 rounded-full flex items-center justify-center">
                    <User className="h-4 w-4 text-white" />
                  </div>
                  <div className="text-left hidden sm:block">
                    <span className="text-sm font-medium text-gray-700">{userInfo.name}</span>
                    <p className="text-xs text-gray-500">{userInfo.title}</p>
                  </div>
                </button>

                {userMenuOpen && (
                  <div className="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg border border-gray-200 z-50">
                    <div className="py-1">
                      <div className="px-4 py-2 border-b border-gray-100">
                        <p className="text-sm font-medium text-gray-900">{userInfo.name}</p>
                        <p className="text-xs text-gray-500">{userInfo.email}</p>
                      </div>
                      <button 
                        onClick={() => {
                          handleNavigation('/profile');
                          setUserMenuOpen(false);
                        }}
                        className="flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                      >
                        <User className="h-4 w-4 mr-2" />
                        Profile
                      </button>
                      <button 
                        onClick={() => {
                          handleNavigation('/settings');
                          setUserMenuOpen(false);
                        }}
                        className="flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                      >
                        <Settings className="h-4 w-4 mr-2" />
                        Settings
                      </button>
                      <hr className="my-1" />
                      <button 
                        onClick={handleSignOutClick}
                        className="flex items-center w-full px-4 py-2 text-sm text-red-600 hover:bg-red-50"
                      >
                        <LogOut className="h-4 w-4 mr-2" />
                        Sign out
                      </button>
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>
        </header>

        {/* Page content */}
        <main className="flex-1 w-full overflow-auto">
          <div className="w-full h-full">
            {children}
          </div>
        </main>
      </div>
    </div>
  );
};

export default Layout;



































