import { api } from '../api';

export interface Document {
  id: string;
  filename: string;
  original_filename: string;
  file_size: number;
  mime_type: string;
  category: 'invoice' | 'inspection' | 'maintenance' | 'insurance' | 'license' | 'other';
  entity_type: 'vehicle' | 'work_order' | 'invoice' | 'vendor' | 'driver';
  entity_id: string;
  url: string;
  thumbnail_url?: string;
  uploaded_by: string;
  uploaded_at: string;
  is_public: boolean;
  metadata?: Record<string, any>;
}

export interface PaginatedResponse<T> {
  data: T[];
  total: number;
  page: number;
  pageSize: number;
}

export interface DocumentFilters {
  entity_type?: string;
  entity_id?: string;
  category?: string;
  page?: number;
  pageSize?: number;
}

export const documentApi = api.injectEndpoints({
  endpoints: (builder) => ({
    // Upload document
    uploadDocument: builder.mutation<Document, FormData>({
      query: (formData) => ({
        url: '/documents/upload',
        method: 'POST',
        body: formData,
      }),
      invalidatesTags: [{ type: 'Document', id: 'LIST' }],
    }),

    // Get documents
    getDocuments: builder.query<PaginatedResponse<Document>, DocumentFilters>({
      query: (filters = {}) => {
        const params = new URLSearchParams();
        Object.entries(filters).forEach(([key, value]) => {
          if (value) params.append(key, String(value));
        });
        return {
          url: '/documents',
          params: Object.fromEntries(params),
        };
      },
      providesTags: (result) =>
        result
          ? [
              ...result.data.map(({ id }) => ({ type: 'Document' as const, id })),
              { type: 'Document', id: 'LIST' },
            ]
          : [{ type: 'Document', id: 'LIST' }],
    }),

    // Delete document
    deleteDocument: builder.mutation<void, string>({
      query: (id) => ({
        url: `/documents/${id}`,
        method: 'DELETE',
      }),
      invalidatesTags: [{ type: 'Document', id: 'LIST' }],
    }),

    // Get document download URL
    getDocumentDownloadUrl: builder.query<{ download_url: string }, string>({
      query: (id) => `/documents/${id}/download`,
    }),
  }),
});

export const {
  useUploadDocumentMutation,
  useGetDocumentsQuery,
  useDeleteDocumentMutation,
  useGetDocumentDownloadUrlQuery,
} = documentApi;


