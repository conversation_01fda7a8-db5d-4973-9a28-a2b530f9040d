import React, { useState } from 'react';
import { Search, Plus, Eye, Edit, Star, MapPin, Phone, Mail, Award, TrendingUp, TrendingDown, Users, Wrench } from 'lucide-react';
import { useNavigate } from 'react-router-dom';

interface Merchant {
  id: string;
  name: string;
  businessType: 'Dealership' | 'Independent' | 'Specialist' | 'Chain';
  location: string;
  address: string;
  contactPerson: string;
  phone: string;
  email: string;
  website?: string;
  rating: number;
  totalJobs: number;
  completedJobs: number;
  averageCost: number;
  averageTime: number;
  onTimeDelivery: number;
  qualityScore: number;
  responseTime: number; // hours
  specialties: string[];
  certifications: string[];
  status: 'Active' | 'Inactive' | 'Suspended' | 'Pending';
  joinDate: string;
  lastJobDate: string;
  performanceTrend: 'up' | 'down' | 'stable';
  preferredVendor: boolean;
}

const MerchantDirectoryPage: React.FC = () => {
  const navigate = useNavigate();
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('All');
  const [typeFilter, setTypeFilter] = useState('All');
  const [specialtyFilter, setSpecialtyFilter] = useState('All');
  const [sortBy, setSortBy] = useState('rating');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc');

  // Mock data - replace with API call
  const merchants: Merchant[] = [
    {
      id: 'M-001',
      name: 'AutoCare Services',
      businessType: 'Independent',
      location: 'Pretoria Central',
      address: '123 Church Street, Pretoria Central, 0002',
      contactPerson: 'John Smith',
      phone: '************',
      email: '<EMAIL>',
      website: 'www.autocare.co.za',
      rating: 4.8,
      totalJobs: 156,
      completedJobs: 148,
      averageCost: 8500,
      averageTime: 3.2,
      onTimeDelivery: 94,
      qualityScore: 4.7,
      responseTime: 2.5,
      specialties: ['Engine Repair', 'Brake Service', 'General Maintenance'],
      certifications: ['ISO 9001', 'MIWA Certified', 'Toyota Approved'],
      status: 'Active',
      joinDate: '2023-03-15',
      lastJobDate: '2025-01-10',
      performanceTrend: 'up',
      preferredVendor: true
    },
    {
      id: 'M-002',
      name: 'Premium Motors',
      businessType: 'Dealership',
      location: 'Sandton',
      address: '45 Rivonia Road, Sandton, 2196',
      contactPerson: 'Sarah Johnson',
      phone: '************',
      email: '<EMAIL>',
      website: 'www.premiummotors.co.za',
      rating: 4.6,
      totalJobs: 89,
      completedJobs: 85,
      averageCost: 12500,
      averageTime: 2.8,
      onTimeDelivery: 96,
      qualityScore: 4.8,
      responseTime: 1.5,
      specialties: ['Luxury Vehicles', 'Warranty Service', 'Diagnostics'],
      certifications: ['BMW Certified', 'Mercedes Approved', 'Audi Service'],
      status: 'Active',
      joinDate: '2022-11-20',
      lastJobDate: '2025-01-12',
      performanceTrend: 'stable',
      preferredVendor: true
    },
    {
      id: 'M-003',
      name: 'Budget Auto Repair',
      businessType: 'Independent',
      location: 'Johannesburg South',
      address: '78 Main Road, Johannesburg South, 2190',
      contactPerson: 'Mike Wilson',
      phone: '************',
      email: '<EMAIL>',
      rating: 4.2,
      totalJobs: 234,
      completedJobs: 220,
      averageCost: 6200,
      averageTime: 4.1,
      onTimeDelivery: 87,
      qualityScore: 4.1,
      responseTime: 4.2,
      specialties: ['Basic Maintenance', 'Tire Service', 'Oil Changes'],
      certifications: ['MIWA Certified'],
      status: 'Active',
      joinDate: '2023-07-10',
      lastJobDate: '2025-01-08',
      performanceTrend: 'down',
      preferredVendor: false
    },
    {
      id: 'M-004',
      name: 'Truck & Fleet Specialists',
      businessType: 'Specialist',
      location: 'Kempton Park',
      address: '12 Industrial Avenue, Kempton Park, 1619',
      contactPerson: 'David Brown',
      phone: '************',
      email: '<EMAIL>',
      website: 'www.truckfleet.co.za',
      rating: 4.9,
      totalJobs: 67,
      completedJobs: 66,
      averageCost: 15800,
      averageTime: 5.2,
      onTimeDelivery: 98,
      qualityScore: 4.9,
      responseTime: 1.8,
      specialties: ['Heavy Vehicles', 'Fleet Maintenance', 'Emergency Repairs'],
      certifications: ['Heavy Vehicle Certified', 'Fleet Specialist', 'Emergency Service'],
      status: 'Active',
      joinDate: '2023-01-05',
      lastJobDate: '2025-01-11',
      performanceTrend: 'up',
      preferredVendor: true
    },
    {
      id: 'M-005',
      name: 'Quick Fix Auto',
      businessType: 'Chain',
      location: 'Cape Town',
      address: '56 Long Street, Cape Town, 8001',
      contactPerson: 'Lisa Adams',
      phone: '************',
      email: '<EMAIL>',
      rating: 3.8,
      totalJobs: 45,
      completedJobs: 42,
      averageCost: 7800,
      averageTime: 3.8,
      onTimeDelivery: 82,
      qualityScore: 3.9,
      responseTime: 6.5,
      specialties: ['Quick Service', 'Basic Repairs', 'Inspections'],
      certifications: ['Basic Service Certified'],
      status: 'Inactive',
      joinDate: '2024-05-15',
      lastJobDate: '2024-12-20',
      performanceTrend: 'down',
      preferredVendor: false
    }
  ];

  const specialties = Array.from(new Set(merchants.flatMap(m => m.specialties)));

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'Active': return 'bg-green-100 text-green-800';
      case 'Inactive': return 'bg-gray-100 text-gray-800';
      case 'Suspended': return 'bg-red-100 text-red-800';
      case 'Pending': return 'bg-yellow-100 text-yellow-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getPerformanceTrendIcon = (trend: string) => {
    switch (trend) {
      case 'up': return <TrendingUp className="w-4 h-4 text-green-500" />;
      case 'down': return <TrendingDown className="w-4 h-4 text-red-500" />;
      default: return <div className="w-4 h-4 bg-gray-400 rounded-full" />;
    }
  };

  const filteredMerchants = merchants.filter(merchant => {
    const matchesSearch = merchant.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         merchant.location.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         merchant.contactPerson.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         merchant.specialties.some(s => s.toLowerCase().includes(searchTerm.toLowerCase()));
    const matchesStatus = statusFilter === 'All' || merchant.status === statusFilter;
    const matchesType = typeFilter === 'All' || merchant.businessType === typeFilter;
    const matchesSpecialty = specialtyFilter === 'All' || merchant.specialties.includes(specialtyFilter);
    
    return matchesSearch && matchesStatus && matchesType && matchesSpecialty;
  }).sort((a, b) => {
    let aValue, bValue;
    switch (sortBy) {
      case 'rating':
        aValue = a.rating;
        bValue = b.rating;
        break;
      case 'totalJobs':
        aValue = a.totalJobs;
        bValue = b.totalJobs;
        break;
      case 'averageCost':
        aValue = a.averageCost;
        bValue = b.averageCost;
        break;
      case 'onTimeDelivery':
        aValue = a.onTimeDelivery;
        bValue = b.onTimeDelivery;
        break;
      case 'name':
        aValue = a.name.toLowerCase();
        bValue = b.name.toLowerCase();
        break;
      default:
        aValue = a.rating;
        bValue = b.rating;
    }
    
    if (sortOrder === 'asc') {
      return aValue > bValue ? 1 : -1;
    } else {
      return aValue < bValue ? 1 : -1;
    }
  });

  const handleViewMerchant = (merchantId: string) => {
    navigate(`/maintenance/merchants/${merchantId}`);
  };

  const handleEditMerchant = (merchantId: string) => {
    navigate(`/maintenance/merchants/${merchantId}/edit`);
  };

  const handleAddMerchant = () => {
    navigate('/maintenance/merchants/add');
  };

  return (
    <div className="p-6">
      {/* Header */}
      <div className="flex justify-between items-center mb-6">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Merchant Directory</h1>
          <p className="text-gray-600">Manage your network of service providers and vendors</p>
        </div>
        <button 
          onClick={handleAddMerchant}
          className="bg-blue-600 text-white px-4 py-2 rounded-lg flex items-center gap-2 hover:bg-blue-700"
        >
          <Plus className="w-4 h-4" />
          Add Merchant
        </button>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
        <div className="bg-white rounded-lg shadow-sm border p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">Total Merchants</p>
              <p className="text-2xl font-bold text-gray-900">{merchants.length}</p>
            </div>
            <Users className="w-8 h-8 text-blue-500" />
          </div>
        </div>
        <div className="bg-white rounded-lg shadow-sm border p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">Active Merchants</p>
              <p className="text-2xl font-bold text-green-600">{merchants.filter(m => m.status === 'Active').length}</p>
            </div>
            <Wrench className="w-8 h-8 text-green-500" />
          </div>
        </div>
        <div className="bg-white rounded-lg shadow-sm border p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">Avg Rating</p>
              <p className="text-2xl font-bold text-yellow-600">
                {(merchants.reduce((sum, m) => sum + m.rating, 0) / merchants.length).toFixed(1)}
              </p>
            </div>
            <Star className="w-8 h-8 text-yellow-500" />
          </div>
        </div>
        <div className="bg-white rounded-lg shadow-sm border p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">Preferred Vendors</p>
              <p className="text-2xl font-bold text-purple-600">{merchants.filter(m => m.preferredVendor).length}</p>
            </div>
            <Award className="w-8 h-8 text-purple-500" />
          </div>
        </div>
      </div>

      {/* Filters */}
      <div className="bg-white rounded-lg shadow-sm border p-4 mb-6">
        <div className="grid grid-cols-1 md:grid-cols-6 gap-4">
          {/* Search */}
          <div className="md:col-span-2">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
              <input
                type="text"
                placeholder="Search merchants..."
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>
          </div>

          {/* Status Filter */}
          <select
            className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            value={statusFilter}
            onChange={(e) => setStatusFilter(e.target.value)}
          >
            <option value="All">All Statuses</option>
            <option value="Active">Active</option>
            <option value="Inactive">Inactive</option>
            <option value="Suspended">Suspended</option>
            <option value="Pending">Pending</option>
          </select>

          {/* Type Filter */}
          <select
            className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            value={typeFilter}
            onChange={(e) => setTypeFilter(e.target.value)}
          >
            <option value="All">All Types</option>
            <option value="Dealership">Dealership</option>
            <option value="Independent">Independent</option>
            <option value="Specialist">Specialist</option>
            <option value="Chain">Chain</option>
          </select>

          {/* Specialty Filter */}
          <select
            className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            value={specialtyFilter}
            onChange={(e) => setSpecialtyFilter(e.target.value)}
          >
            <option value="All">All Specialties</option>
            {specialties.map(specialty => (
              <option key={specialty} value={specialty}>{specialty}</option>
            ))}
          </select>

          {/* Sort */}
          <select
            className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            value={`${sortBy}-${sortOrder}`}
            onChange={(e) => {
              const [field, order] = e.target.value.split('-');
              setSortBy(field);
              setSortOrder(order as 'asc' | 'desc');
            }}
          >
            <option value="rating-desc">Rating (High to Low)</option>
            <option value="rating-asc">Rating (Low to High)</option>
            <option value="totalJobs-desc">Most Jobs</option>
            <option value="averageCost-asc">Lowest Cost</option>
            <option value="onTimeDelivery-desc">Best On-Time</option>
            <option value="name-asc">Name (A-Z)</option>
          </select>
        </div>
      </div>

      {/* Results Summary */}
      <div className="mb-4">
        <p className="text-gray-600">
          Showing {filteredMerchants.length} of {merchants.length} merchants
        </p>
      </div>

      {/* Merchants Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
        {filteredMerchants.map((merchant) => (
          <div key={merchant.id} className="bg-white rounded-lg shadow-sm border hover:shadow-md transition-shadow">
            <div className="p-6">
              {/* Header */}
              <div className="flex items-start justify-between mb-4">
                <div className="flex-1">
                  <div className="flex items-center gap-2 mb-1">
                    <h3 className="text-lg font-semibold text-gray-900">{merchant.name}</h3>
                    {merchant.preferredVendor && (
                      <Award className="w-4 h-4 text-purple-500" />
                    )}
                  </div>
                  <p className="text-sm text-gray-600">{merchant.businessType}</p>
                  <div className="flex items-center gap-1 mt-1">
                    {Array.from({ length: 5 }, (_, i) => (
                      <Star key={i} className={`w-4 h-4 ${i < Math.floor(merchant.rating) ? 'text-yellow-400 fill-current' : 'text-gray-300'}`} />
                    ))}
                    <span className="text-sm text-gray-600 ml-1">({merchant.rating})</span>
                    {getPerformanceTrendIcon(merchant.performanceTrend)}
                  </div>
                </div>
                <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(merchant.status)}`}>
                  {merchant.status}
                </span>
              </div>

              {/* Contact Info */}
              <div className="mb-4 space-y-2">
                <div className="flex items-center gap-2 text-sm text-gray-600">
                  <MapPin className="w-4 h-4" />
                  {merchant.location}
                </div>
                <div className="flex items-center gap-2 text-sm text-gray-600">
                  <Phone className="w-4 h-4" />
                  {merchant.phone}
                </div>
                <div className="flex items-center gap-2 text-sm text-gray-600">
                  <Mail className="w-4 h-4" />
                  {merchant.email}
                </div>
              </div>

              {/* Performance Metrics */}
              <div className="mb-4 grid grid-cols-2 gap-4">
                <div className="text-center">
                  <p className="text-lg font-semibold text-gray-900">{merchant.totalJobs}</p>
                  <p className="text-xs text-gray-600">Total Jobs</p>
                </div>
                <div className="text-center">
                  <p className="text-lg font-semibold text-green-600">{merchant.onTimeDelivery}%</p>
                  <p className="text-xs text-gray-600">On Time</p>
                </div>
                <div className="text-center">
                  <p className="text-lg font-semibold text-blue-600">R{(merchant.averageCost / 1000).toFixed(1)}k</p>
                  <p className="text-xs text-gray-600">Avg Cost</p>
                </div>
                <div className="text-center">
                  <p className="text-lg font-semibold text-orange-600">{merchant.averageTime}d</p>
                  <p className="text-xs text-gray-600">Avg Time</p>
                </div>
              </div>

              {/* Specialties */}
              <div className="mb-4">
                <p className="text-sm text-gray-600 mb-2">Specialties:</p>
                <div className="flex flex-wrap gap-1">
                  {merchant.specialties.slice(0, 3).map((specialty, index) => (
                    <span key={index} className="bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded">
                      {specialty}
                    </span>
                  ))}
                  {merchant.specialties.length > 3 && (
                    <span className="bg-gray-100 text-gray-600 text-xs px-2 py-1 rounded">
                      +{merchant.specialties.length - 3} more
                    </span>
                  )}
                </div>
              </div>

              {/* Actions */}
              <div className="flex gap-2">
                <button
                  onClick={() => handleViewMerchant(merchant.id)}
                  className="flex-1 bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg flex items-center justify-center gap-2"
                >
                  <Eye className="w-4 h-4" />
                  View Details
                </button>
                <button
                  onClick={() => handleEditMerchant(merchant.id)}
                  className="bg-gray-100 hover:bg-gray-200 text-gray-700 px-3 py-2 rounded-lg"
                >
                  <Edit className="w-4 h-4" />
                </button>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Empty State */}
      {filteredMerchants.length === 0 && (
        <div className="text-center py-12">
          <Users className="mx-auto h-12 w-12 text-gray-400" />
          <h3 className="mt-2 text-sm font-medium text-gray-900">No merchants found</h3>
          <p className="mt-1 text-sm text-gray-500">
            Try adjusting your search criteria or add a new merchant.
          </p>
        </div>
      )}
    </div>
  );
};

export default MerchantDirectoryPage;
