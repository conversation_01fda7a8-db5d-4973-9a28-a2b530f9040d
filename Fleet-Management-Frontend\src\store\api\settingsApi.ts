import { api } from '../api';

export interface SystemSettings {
  id: string;
  category: 'general' | 'notifications' | 'security' | 'integrations' | 'maintenance';
  key: string;
  value: string;
  description: string;
  data_type: 'string' | 'number' | 'boolean' | 'json';
  is_public: boolean;
  updated_by: string;
  updated_at: string;
}

export interface NotificationSettings {
  email_enabled: boolean;
  sms_enabled: boolean;
  push_enabled: boolean;
  maintenance_reminders: boolean;
  budget_alerts: boolean;
  invoice_notifications: boolean;
  emergency_alerts: boolean;
}

export const settingsApi = api.injectEndpoints({
  endpoints: (builder) => ({
    // Get system settings
    getSystemSettings: builder.query<SystemSettings[], { category?: string }>({
      query: (params = {}) => ({
        url: '/settings/system',
        params,
      }),
      providesTags: [{ type: 'User', id: 'SETTINGS' }],
    }),

    // Update system setting
    updateSystemSetting: builder.mutation<SystemSettings, {
      key: string;
      value: string;
    }>({
      query: (data) => ({
        url: '/settings/system',
        method: 'PUT',
        body: data,
      }),
      invalidatesTags: [{ type: 'User', id: 'SETTINGS' }],
    }),

    // Get user notification settings
    getNotificationSettings: builder.query<NotificationSettings, void>({
      query: () => '/settings/notifications',
      providesTags: [{ type: 'User', id: 'NOTIFICATIONS' }],
    }),

    // Update notification settings
    updateNotificationSettings: builder.mutation<NotificationSettings, Partial<NotificationSettings>>({
      query: (data) => ({
        url: '/settings/notifications',
        method: 'PUT',
        body: data,
      }),
      invalidatesTags: [{ type: 'User', id: 'NOTIFICATIONS' }],
    }),

    // Backup system data
    createBackup: builder.mutation<{ backup_id: string; download_url: string }, {
      include_files: boolean;
      password_protect: boolean;
    }>({
      query: (data) => ({
        url: '/settings/backup',
        method: 'POST',
        body: data,
      }),
    }),

    // Get system health
    getSystemHealth: builder.query<{
      database_status: 'healthy' | 'warning' | 'error';
      api_status: 'healthy' | 'warning' | 'error';
      storage_usage: number;
      memory_usage: number;
      active_users: number;
      last_backup: string;
    }, void>({
      query: () => '/settings/health',
    }),
  }),
});

export const {
  useGetSystemSettingsQuery,
  useUpdateSystemSettingMutation,
  useGetNotificationSettingsQuery,
  useUpdateNotificationSettingsMutation,
  useCreateBackupMutation,
  useGetSystemHealthQuery,
} = settingsApi;