# Documentation & Planning Patterns

## Document Structure Standards
- Follow the established pattern from [design_specification.md](mdc:design_specification.md)
- Use natural language descriptions rather than code snippets in technical specifications (unless it is a code example)
- Focus on architectural decisions and requirements, not implementations
- Include comprehensive metadata: version, date, author, status

## Key Documentation Files
- **PRD.md**: Product Requirements Document with user stories and acceptance criteria
- **design_specification.md**: Technical architecture without code examples
- **rules.md**: Development best practices and coding standards  
- **mvp_plan.md**: 15-day sprint plan for rapid prototyping
- **plan.md**: 12-month implementation roadmap

## Documentation Principles
- Write for developers who will implement the code, not copy-paste examples
- Include business context and regulatory requirements
- Maintain version control and change history
- Cross-reference related documents and sections
- Use clear section numbering and hierarchical structure

## Planning Document Requirements
- Executive summaries for stakeholder overview
- Detailed technical requirements for development teams
- Risk assessment and mitigation strategies
- Success criteria and acceptance definitions
- Timeline with realistic milestones and dependencies

## Markdown Standards
- Use consistent heading hierarchy (H1 for main sections, H2 for subsections)
- Include table of contents for long documents
- Use code blocks only for configuration examples, not implementation code
- Include metadata tables for document versioning
- Use bullet points and numbered lists for clarity

## Review and Update Process
- Documents are living artifacts that evolve with the project
- Regular reviews ensure accuracy and relevance
- Version control tracks all changes and rationale
- Stakeholder sign-offs for major specification changes
---
globs: "*.md,*.mdc"
---
